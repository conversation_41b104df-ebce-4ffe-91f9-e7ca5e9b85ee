# Flutter DCT Compress

Package nén ảnh thuần Dart sử dụng thuật toán Discrete Cosine Transform (DCT) với tính năng nâng cao. Package này cung cấp khả năng nén ảnh đa nền tảng mà không cần mã native, đảm bảo tương thích trên tất cả các nền tảng Flutter hỗ trợ (iOS, Android, Web, Desktop).

> **English Documentation**: [README_EN.md](README_EN.md)

## Tính năng chính

- 🎯 **Thuần Dart**: 100% mã Dart không phụ thuộc native
- 🌐 **Đa nền tảng**: Hoạt động trên iOS, Android, Web, và Desktop
- 🔧 **Nén DCT**: Triển khai thuật toán Discrete Cosine Transform
- ⚡ **Xử lý hiệu quả**: Tối ưu hiệu năng trên mọi nền tảng
- 🎛️ **<PERSON>ểm soát chất lượng**: Đi<PERSON>u chỉnh chất lượng nén (1-100)
- 📁 **Hỗ trợ đa định dạng**: JPEG, PNG, BMP, TIFF, WebP input/output
- 🔄 **Xử lý nền**: Sử dụng Isolate để không chặn UI
- 📊 **Kiểm soát tỷ lệ nén**: Thiết lập min/max compression ratio
- 🚀 **Xử lý hàng loạt**: Nén nhiều ảnh cùng lúc với progress tracking
- 📱 **API linh hoạt**: Hỗ trợ File, đường dẫn, và byte data
- 🧪 **Kiểm thử toàn diện**: Test coverage đầy đủ

## Bắt đầu sử dụng

Thêm package này vào `pubspec.yaml`:

```yaml
dependencies:
  flutter_dct_compress: ^0.0.1
```

Sau đó chạy:
```bash
flutter pub get
```

## Cách sử dụng

### Nén ảnh cơ bản

```dart
import 'package:flutter_dct_compress/flutter_dct_compress.dart';
import 'dart:typed_data';
import 'dart:io';

// Nén ảnh với chất lượng mặc định (85)
Future<void> compressImage() async {
  final compressor = DctCompressor();

  // Có thể sử dụng File, đường dẫn String, hoặc Uint8List
  final result = await compressor.compressImage(
    File('path/to/image.jpg'), // hoặc 'path/to/image.jpg' hoặc imageBytes
    options: CompressionOptions(quality: 85),
  );

  print('Kích thước gốc: ${result.originalSize} bytes');
  print('Kích thước nén: ${result.compressedSize} bytes');
  print('Tỷ lệ nén: ${result.compressionRatio}');
  print('Thời gian xử lý: ${result.processingTime}');
}
```

### Nén với kiểm soát tỷ lệ nén

```dart
import 'package:flutter_dct_compress/flutter_dct_compress.dart';

Future<void> compressionWithBounds() async {
  final compressor = DctCompressor();

  final options = CompressionOptions(
    quality: 75,
    minCompressionRatio: 0.2, // Tối thiểu 20% kích thước gốc
    maxCompressionRatio: 0.8, // Tối đa 80% kích thước gốc
    preserveMetadata: true,
    outputFormat: OutputFormat.jpeg,
  );

  final result = await compressor.compressImage(
    'path/to/image.png',
    options: options,
  );

  // Lưu kết quả
  await OutputHandler.saveToFile(result, 'path/to/compressed.jpg');
}
```

### Xử lý hàng loạt với progress tracking

```dart
Future<void> batchCompression() async {
  final compressor = DctCompressor();
  final imagePaths = ['image1.jpg', 'image2.png', 'image3.bmp'];

  final results = await compressor.compressBatch(
    imagePaths,
    options: CompressionOptions(quality: 80),
    onProgress: (int completed, int total, String currentFile) {
      print('Tiến trình: $completed/$total - Đang xử lý: $currentFile');
    },
  );

  for (final result in results) {
    print('Đã nén ${result.imageInfo.width}x${result.imageInfo.height} '
          'với tỷ lệ ${result.compressionRatio}');
  }
}
```

## Project Plan

For detailed information about the project architecture, implementation approach, and development roadmap, see [PROJECT_PLAN.md](PROJECT_PLAN.md).

## Additional information

- **Platform Support**: iOS, Android, Web, macOS, Windows, Linux
- **Dart SDK**: Requires Dart 3.0.0 or higher
- **Flutter SDK**: Requires Flutter 3.0.0 or higher
- **Dependencies**: Minimal dependencies for maximum compatibility

### Contributing

Contributions are welcome! Please read our contributing guidelines and submit pull requests to our repository.

### Issues

If you encounter any issues or have feature requests, please file them in our [issue tracker](https://github.com/your-username/flutter-dct-compress/issues).

### License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
