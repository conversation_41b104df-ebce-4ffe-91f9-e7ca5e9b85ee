/// Sample image utilities
/// 
/// Tạo và quản lý sample images cho demo application.
library;

import 'dart:typed_data';
import 'dart:math' as math;
import 'package:flutter_dct_compress/flutter_dct_compress.dart';

class SampleImages {
  /// Tạo test image với pattern đơn giản
  static PixelData createTestImage(int width, int height, int channels, {int seed = 0}) {
    final imageSize = width * height * channels;
    final random = math.Random(seed);
    
    final data = Uint8List.fromList(
      List.generate(imageSize, (i) {
        final pixel = i ~/ channels;
        final channel = i % channels;
        final x = pixel % width;
        final y = pixel ~/ width;
        
        // Create a pattern based on position and seed
        return ((x + y + channel + seed) * 37) % 256;
      })
    );
    
    final imageInfo = ImageInfo(
      width: width,
      height: height,
      channels: channels,
      format: 'RGB',
      bitDepth: 8,
      hasAlpha: false,
      colorSpace: 'RGB',
    );
    
    return PixelData.fromBytes(imageInfo, data);
  }
  
  /// Tạo gradient image
  static PixelData createGradientImage(int width, int height) {
    final imageSize = width * height * 3;
    
    final data = Uint8List.fromList(
      List.generate(imageSize, (i) {
        final pixel = i ~/ 3;
        final channel = i % 3;
        final x = pixel % width;
        final y = pixel ~/ width;
        
        switch (channel) {
          case 0: // Red
            return ((x / width) * 255).round();
          case 1: // Green
            return ((y / height) * 255).round();
          case 2: // Blue
            return 128;
          default:
            return 0;
        }
      })
    );
    
    final imageInfo = ImageInfo(
      width: width,
      height: height,
      channels: 3,
      format: 'RGB',
      bitDepth: 8,
      hasAlpha: false,
      colorSpace: 'RGB',
    );
    
    return PixelData.fromBytes(imageInfo, data);
  }
  
  /// Tạo checkerboard pattern
  static PixelData createCheckerboardImage(int width, int height, {int squareSize = 8}) {
    final imageSize = width * height * 3;
    
    final data = Uint8List.fromList(
      List.generate(imageSize, (i) {
        final pixel = i ~/ 3;
        final x = pixel % width;
        final y = pixel ~/ width;
        
        final squareX = x ~/ squareSize;
        final squareY = y ~/ squareSize;
        final isWhite = (squareX + squareY) % 2 == 0;
        
        return isWhite ? 255 : 0;
      })
    );
    
    final imageInfo = ImageInfo(
      width: width,
      height: height,
      channels: 3,
      format: 'RGB',
      bitDepth: 8,
      hasAlpha: false,
      colorSpace: 'RGB',
    );
    
    return PixelData.fromBytes(imageInfo, data);
  }
  
  /// Tạo noise image (high complexity)
  static PixelData createNoiseImage(int width, int height, {int seed = 42}) {
    final imageSize = width * height * 3;
    final random = math.Random(seed);
    
    final data = Uint8List.fromList(
      List.generate(imageSize, (i) => random.nextInt(256))
    );
    
    final imageInfo = ImageInfo(
      width: width,
      height: height,
      channels: 3,
      format: 'RGB',
      bitDepth: 8,
      hasAlpha: false,
      colorSpace: 'RGB',
    );
    
    return PixelData.fromBytes(imageInfo, data);
  }
  
  /// Tạo solid color image (low complexity)
  static PixelData createSolidColorImage(int width, int height, {int r = 128, int g = 128, int b = 128}) {
    final imageSize = width * height * 3;
    
    final data = Uint8List.fromList(
      List.generate(imageSize, (i) {
        final channel = i % 3;
        switch (channel) {
          case 0: return r;
          case 1: return g;
          case 2: return b;
          default: return 0;
        }
      })
    );
    
    final imageInfo = ImageInfo(
      width: width,
      height: height,
      channels: 3,
      format: 'RGB',
      bitDepth: 8,
      hasAlpha: false,
      colorSpace: 'RGB',
    );
    
    return PixelData.fromBytes(imageInfo, data);
  }
  
  /// Tạo circular pattern
  static PixelData createCircularImage(int width, int height) {
    final imageSize = width * height * 3;
    final centerX = width / 2;
    final centerY = height / 2;
    final maxRadius = math.min(centerX, centerY);
    
    final data = Uint8List.fromList(
      List.generate(imageSize, (i) {
        final pixel = i ~/ 3;
        final channel = i % 3;
        final x = pixel % width;
        final y = pixel ~/ width;
        
        final dx = x - centerX;
        final dy = y - centerY;
        final distance = math.sqrt(dx * dx + dy * dy);
        final normalizedDistance = (distance / maxRadius).clamp(0.0, 1.0);
        
        switch (channel) {
          case 0: // Red - increases towards center
            return ((1.0 - normalizedDistance) * 255).round();
          case 1: // Green - increases towards edge
            return (normalizedDistance * 255).round();
          case 2: // Blue - constant
            return 128;
          default:
            return 0;
        }
      })
    );
    
    final imageInfo = ImageInfo(
      width: width,
      height: height,
      channels: 3,
      format: 'RGB',
      bitDepth: 8,
      hasAlpha: false,
      colorSpace: 'RGB',
    );
    
    return PixelData.fromBytes(imageInfo, data);
  }
  
  /// Tạo sine wave pattern
  static PixelData createSineWaveImage(int width, int height, {double frequency = 0.1}) {
    final imageSize = width * height * 3;
    
    final data = Uint8List.fromList(
      List.generate(imageSize, (i) {
        final pixel = i ~/ 3;
        final channel = i % 3;
        final x = pixel % width;
        final y = pixel ~/ width;
        
        final sineX = math.sin(x * frequency * math.pi);
        final sineY = math.sin(y * frequency * math.pi);
        
        switch (channel) {
          case 0: // Red
            return ((sineX + 1) * 127.5).round();
          case 1: // Green
            return ((sineY + 1) * 127.5).round();
          case 2: // Blue
            return ((sineX * sineY + 1) * 127.5).round();
          default:
            return 0;
        }
      })
    );
    
    final imageInfo = ImageInfo(
      width: width,
      height: height,
      channels: 3,
      format: 'RGB',
      bitDepth: 8,
      hasAlpha: false,
      colorSpace: 'RGB',
    );
    
    return PixelData.fromBytes(imageInfo, data);
  }
  
  /// Tạo batch sample images với các pattern khác nhau
  static List<PixelData> createSampleBatch(int count, {int width = 64, int height = 64}) {
    final samples = <PixelData>[];
    
    for (int i = 0; i < count; i++) {
      switch (i % 6) {
        case 0:
          samples.add(createGradientImage(width, height));
          break;
        case 1:
          samples.add(createCheckerboardImage(width, height));
          break;
        case 2:
          samples.add(createNoiseImage(width, height, seed: i));
          break;
        case 3:
          samples.add(createSolidColorImage(width, height, 
                     r: (i * 50) % 256, g: (i * 75) % 256, b: (i * 100) % 256));
          break;
        case 4:
          samples.add(createCircularImage(width, height));
          break;
        case 5:
          samples.add(createSineWaveImage(width, height, frequency: 0.05 + (i * 0.02)));
          break;
      }
    }
    
    return samples;
  }
  
  /// Lấy tên mô tả cho sample image
  static String getSampleImageName(int index) {
    switch (index % 6) {
      case 0: return 'Gradient';
      case 1: return 'Checkerboard';
      case 2: return 'Noise';
      case 3: return 'Solid Color';
      case 4: return 'Circular';
      case 5: return 'Sine Wave';
      default: return 'Unknown';
    }
  }
  
  /// Lấy mô tả complexity cho sample image
  static String getSampleImageComplexity(int index) {
    switch (index % 6) {
      case 0: return 'Medium';
      case 1: return 'Medium';
      case 2: return 'High';
      case 3: return 'Low';
      case 4: return 'Medium';
      case 5: return 'Medium';
      default: return 'Unknown';
    }
  }
  
  /// Tạo image với kích thước cụ thể cho testing
  static List<PixelData> createSizeTestImages() {
    final sizes = [
      [32, 32],
      [64, 64],
      [128, 128],
      [256, 256],
      [512, 512],
    ];
    
    return sizes.map((size) => 
      createGradientImage(size[0], size[1])
    ).toList();
  }
  
  /// Lấy tên kích thước cho size test images
  static List<String> getSizeTestImageNames() {
    return [
      '32x32',
      '64x64',
      '128x128',
      '256x256',
      '512x512',
    ];
  }
}
