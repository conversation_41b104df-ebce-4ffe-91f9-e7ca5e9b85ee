/// Single image compression screen
/// 
/// <PERSON><PERSON><PERSON> hình demo nén một ảnh đơn lẻ với các tùy chọn
/// quality, color space, và hiển thị kết quả chi tiết.
library;

import 'package:flutter/material.dart';
import 'package:flutter_dct_compress/flutter_dct_compress.dart';
import '../main.dart';
import '../utils/sample_images.dart';

class SingleCompressionScreen extends StatefulWidget {
  const SingleCompressionScreen({Key? key}) : super(key: key);

  @override
  State<SingleCompressionScreen> createState() => _SingleCompressionScreenState();
}

class _SingleCompressionScreenState extends State<SingleCompressionScreen> {
  final DctCompressor _compressor = DctCompressor();
  
  // UI State
  bool _isLoading = false;
  CompressionResult? _result;
  String? _error;
  
  // Compression Settings
  int _quality = 75;
  ColorSpace _colorSpace = ColorSpace.yuv;
  bool _useAdaptiveQuantization = true;
  bool _progressive = false;
  bool _useIsolate = true;
  
  // Sample Image Selection
  int _selectedSampleIndex = 0;
  final List<String> _sampleNames = [
    'Gradient',
    'Checkerboard', 
    'Noise (High Complexity)',
    'Solid Color (Low Complexity)',
    'Circular Pattern',
    'Sine Wave',
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CommonAppBar(title: 'Nén ảnh đơn lẻ'),
      body: LoadingOverlay(
        isLoading: _isLoading,
        message: 'Đang nén ảnh...',
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildSampleImageSelector(),
              const SizedBox(height: 20),
              _buildCompressionSettings(),
              const SizedBox(height: 20),
              _buildCompressButton(),
              const SizedBox(height: 20),
              if (_error != null) _buildErrorDisplay(),
              if (_result != null) _buildResultDisplay(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSampleImageSelector() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.image,
                  color: Theme.of(context).primaryColor,
                ),
                const SizedBox(width: 8),
                Text(
                  'Chọn ảnh mẫu',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<int>(
              value: _selectedSampleIndex,
              decoration: const InputDecoration(
                labelText: 'Loại ảnh mẫu',
                border: OutlineInputBorder(),
              ),
              items: List.generate(_sampleNames.length, (index) {
                return DropdownMenuItem(
                  value: index,
                  child: Text(_sampleNames[index]),
                );
              }),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _selectedSampleIndex = value;
                    _result = null;
                    _error = null;
                  });
                }
              },
            ),
            const SizedBox(height: 12),
            Text(
              'Complexity: ${SampleImages.getSampleImageComplexity(_selectedSampleIndex)}',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCompressionSettings() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.tune,
                  color: Theme.of(context).primaryColor,
                ),
                const SizedBox(width: 8),
                Text(
                  'Cài đặt nén',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            
            // Quality Slider
            Text(
              'Chất lượng: $_quality',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            Slider(
              value: _quality.toDouble(),
              min: 1,
              max: 100,
              divisions: 99,
              label: _quality.toString(),
              onChanged: (value) {
                setState(() {
                  _quality = value.round();
                });
              },
            ),
            const SizedBox(height: 16),
            
            // Color Space
            Text(
              'Color Space',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            SegmentedButton<ColorSpace>(
              segments: const [
                ButtonSegment(
                  value: ColorSpace.rgb,
                  label: Text('RGB'),
                ),
                ButtonSegment(
                  value: ColorSpace.yuv,
                  label: Text('YUV'),
                ),
              ],
              selected: {_colorSpace},
              onSelectionChanged: (Set<ColorSpace> selection) {
                setState(() {
                  _colorSpace = selection.first;
                });
              },
            ),
            const SizedBox(height: 16),
            
            // Advanced Options
            SwitchListTile(
              title: const Text('Adaptive Quantization'),
              subtitle: const Text('Tự động điều chỉnh quantization dựa trên nội dung'),
              value: _useAdaptiveQuantization,
              onChanged: (value) {
                setState(() {
                  _useAdaptiveQuantization = value;
                });
              },
            ),
            SwitchListTile(
              title: const Text('Progressive'),
              subtitle: const Text('Nén progressive cho web'),
              value: _progressive,
              onChanged: (value) {
                setState(() {
                  _progressive = value;
                });
              },
            ),
            SwitchListTile(
              title: const Text('Use Isolate'),
              subtitle: const Text('Xử lý trong background thread'),
              value: _useIsolate,
              onChanged: (value) {
                setState(() {
                  _useIsolate = value;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCompressButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: _isLoading ? null : _compressImage,
        icon: const Icon(Icons.compress),
        label: const Text('Nén ảnh'),
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 16),
        ),
      ),
    );
  }

  Widget _buildErrorDisplay() {
    return ErrorDisplay(
      error: _error!,
      onRetry: _compressImage,
    );
  }

  Widget _buildResultDisplay() {
    final result = _result!;
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.check_circle,
                  color: Colors.green,
                ),
                const SizedBox(width: 8),
                Text(
                  'Kết quả nén',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // Basic Info
            Row(
              children: [
                Expanded(
                  child: InfoCard(
                    title: 'Tỷ lệ nén',
                    value: '${(result.compressionRatio * 100).toStringAsFixed(1)}%',
                    icon: Icons.compress,
                    color: Colors.blue,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: InfoCard(
                    title: 'Thời gian',
                    value: '${result.processingTime.inMilliseconds}ms',
                    icon: Icons.timer,
                    color: Colors.orange,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            
            // Size Info
            Row(
              children: [
                Expanded(
                  child: InfoCard(
                    title: 'Kích thước gốc',
                    value: _formatBytes(result.originalSize),
                    icon: Icons.file_present,
                    color: Colors.grey,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: InfoCard(
                    title: 'Kích thước nén',
                    value: _formatBytes(result.compressedSize),
                    icon: Icons.file_download,
                    color: Colors.green,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // Quality Metrics
            if (result.psnr != null || result.ssim != null) ...[
              Text(
                'Chỉ số chất lượng',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  if (result.psnr != null)
                    Expanded(
                      child: InfoCard(
                        title: 'PSNR',
                        value: '${result.psnr!.toStringAsFixed(2)} dB',
                        icon: Icons.signal_cellular_alt,
                        color: Colors.purple,
                      ),
                    ),
                  if (result.psnr != null && result.ssim != null)
                    const SizedBox(width: 12),
                  if (result.ssim != null)
                    Expanded(
                      child: InfoCard(
                        title: 'SSIM',
                        value: result.ssim!.toStringAsFixed(3),
                        icon: Icons.visibility,
                        color: Colors.indigo,
                      ),
                    ),
                ],
              ),
              const SizedBox(height: 16),
            ],
            
            // Technical Details
            ExpansionTile(
              title: const Text('Chi tiết kỹ thuật'),
              children: [
                _buildDetailRow('Quality sử dụng', '${result.qualityUsed}'),
                _buildDetailRow('Color Space', _colorSpace.name.toUpperCase()),
                _buildDetailRow('Adaptive Quantization', result.usedAdaptiveQuantization ? 'Có' : 'Không'),
                _buildDetailRow('Progressive', _progressive ? 'Có' : 'Không'),
                _buildDetailRow('Sử dụng Isolate', result.usedIsolate ? 'Có' : 'Không'),
                if (result.memoryUsed != null)
                  _buildDetailRow('Bộ nhớ sử dụng', _formatBytes(result.memoryUsed!)),
                _buildDetailRow('Kích thước ảnh', '${result.imageInfo.width}x${result.imageInfo.height}'),
                _buildDetailRow('Channels', '${result.imageInfo.channels}'),
                _buildDetailRow('Bit Depth', '${result.imageInfo.bitDepth}'),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  String _formatBytes(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
  }

  Future<void> _compressImage() async {
    setState(() {
      _isLoading = true;
      _result = null;
      _error = null;
    });

    try {
      // Create sample image
      final sampleImage = _createSampleImage();
      
      // Create compression options
      final options = CompressionOptions(
        quality: _quality,
        colorSpace: _colorSpace,
        useAdaptiveQuantization: _useAdaptiveQuantization,
        progressive: _progressive,
        useIsolate: _useIsolate,
      );
      
      // Perform compression
      final result = await _compressor.compressImage(sampleImage, options: options);
      
      if (result.isSuccess) {
        setState(() {
          _result = result;
        });
      } else {
        setState(() {
          _error = result.errorMessage ?? 'Unknown error';
        });
      }
    } catch (e) {
      setState(() {
        _error = e.toString();
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  PixelData _createSampleImage() {
    const size = 128; // Fixed size for demo
    
    switch (_selectedSampleIndex) {
      case 0:
        return SampleImages.createGradientImage(size, size);
      case 1:
        return SampleImages.createCheckerboardImage(size, size);
      case 2:
        return SampleImages.createNoiseImage(size, size);
      case 3:
        return SampleImages.createSolidColorImage(size, size);
      case 4:
        return SampleImages.createCircularImage(size, size);
      case 5:
        return SampleImages.createSineWaveImage(size, size);
      default:
        return SampleImages.createTestImage(size, size, 3);
    }
  }
}
