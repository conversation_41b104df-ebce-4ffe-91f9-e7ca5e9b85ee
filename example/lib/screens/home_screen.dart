/// Home screen - <PERSON><PERSON> chủ của ứng dụng demo
/// 
/// <PERSON><PERSON><PERSON> thị tổng quan về tính năng và quick access
/// đến các chức năng chính của plugin.
library;

import 'package:flutter/material.dart';
import 'package:flutter_dct_compress/flutter_dct_compress.dart';
import '../main.dart';
import '../utils/sample_images.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({Key? key}) : super(key: key);

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  bool _isLoading = false;
  String? _quickTestResult;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CommonAppBar(
        title: 'Flutter DCT Compress Demo',
        showBackButton: false,
      ),
      drawer: const AppDrawer(),
      body: LoadingOverlay(
        isLoading: _isLoading,
        message: '<PERSON><PERSON> thực hiện quick test...',
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: <PERSON>umn(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildWelcomeCard(),
              const SizedBox(height: 20),
              _buildQuickTestCard(),
              const SizedBox(height: 20),
              _buildFeaturesGrid(),
              const SizedBox(height: 20),
              _buildQuickAccessGrid(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildWelcomeCard() {
    return Card(
      elevation: 4,
      child: Container(
        width: double.infinity,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            colors: [
              Theme.of(context).primaryColor,
              Theme.of(context).primaryColor.withOpacity(0.8),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Icon(
                Icons.compress,
                size: 48,
                color: Colors.white,
              ),
              const SizedBox(height: 16),
              const Text(
                'Chào mừng đến với Flutter DCT Compress!',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 8),
              const Text(
                'Plugin nén ảnh hiệu quả sử dụng thuật toán DCT (Discrete Cosine Transform) '
                'với hỗ trợ xử lý đa luồng và kiểm soát chất lượng thông minh.',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.white70,
                ),
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  _buildFeatureBadge('DCT Algorithm'),
                  const SizedBox(width: 8),
                  _buildFeatureBadge('Multi-threading'),
                  const SizedBox(width: 8),
                  _buildFeatureBadge('Quality Control'),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFeatureBadge(String text) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        text,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildQuickTestCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.flash_on,
                  color: Theme.of(context).primaryColor,
                ),
                const SizedBox(width: 8),
                Text(
                  'Quick Test',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            const Text(
              'Thực hiện test nhanh để kiểm tra hoạt động của plugin.',
            ),
            const SizedBox(height: 16),
            if (_quickTestResult != null) ...[
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.green.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.green.shade200),
                ),
                child: Text(
                  _quickTestResult!,
                  style: TextStyle(
                    color: Colors.green.shade800,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              const SizedBox(height: 16),
            ],
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isLoading ? null : _runQuickTest,
                icon: const Icon(Icons.play_arrow),
                label: const Text('Chạy Quick Test'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeaturesGrid() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Tính năng chính',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: 1.5,
          children: [
            _buildFeatureCard(
              icon: Icons.speed,
              title: 'Hiệu năng cao',
              description: 'Thuật toán DCT tối ưu',
              color: Colors.blue,
            ),
            _buildFeatureCard(
              icon: Icons.tune,
              title: 'Kiểm soát chất lượng',
              description: 'Adaptive quantization',
              color: Colors.green,
            ),
            _buildFeatureCard(
              icon: Icons.memory,
              title: 'Quản lý bộ nhớ',
              description: 'Memory-efficient processing',
              color: Colors.orange,
            ),
            _buildFeatureCard(
              icon: Icons.batch_prediction,
              title: 'Xử lý hàng loạt',
              description: 'Batch processing với progress',
              color: Colors.purple,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildFeatureCard({
    required IconData icon,
    required String title,
    required String description,
    required Color color,
  }) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Icon(
              icon,
              color: color,
              size: 32,
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Expanded(
              child: Text(
                description,
                style: Theme.of(context).textTheme.bodySmall,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickAccessGrid() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Truy cập nhanh',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: 1.2,
          children: [
            _buildQuickAccessCard(
              icon: Icons.image,
              title: 'Nén đơn lẻ',
              subtitle: 'Nén một ảnh',
              route: '/single',
              color: Colors.blue,
            ),
            _buildQuickAccessCard(
              icon: Icons.photo_library,
              title: 'Nén hàng loạt',
              subtitle: 'Xử lý nhiều ảnh',
              route: '/batch',
              color: Colors.green,
            ),
            _buildQuickAccessCard(
              icon: Icons.compare,
              title: 'So sánh chất lượng',
              subtitle: 'PSNR, SSIM',
              route: '/quality',
              color: Colors.orange,
            ),
            _buildQuickAccessCard(
              icon: Icons.speed,
              title: 'Benchmark',
              subtitle: 'Đo hiệu năng',
              route: '/benchmark',
              color: Colors.purple,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildQuickAccessCard({
    required IconData icon,
    required String title,
    required String subtitle,
    required String route,
    required Color color,
  }) {
    return Card(
      child: InkWell(
        onTap: () => Navigator.pushNamed(context, route),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Icon(
                icon,
                color: color,
                size: 36,
              ),
              const SizedBox(height: 12),
              Text(
                title,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
              const Spacer(),
              Align(
                alignment: Alignment.centerRight,
                child: Icon(
                  Icons.arrow_forward_ios,
                  size: 16,
                  color: Colors.grey[400],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _runQuickTest() async {
    setState(() {
      _isLoading = true;
      _quickTestResult = null;
    });

    try {
      // Create a small test image
      final testImage = SampleImages.createTestImage(64, 64, 3);
      
      // Initialize compressor
      final compressor = DctCompressor();
      
      // Perform compression
      final stopwatch = Stopwatch()..start();
      final result = await compressor.compressImage(
        testImage,
        options: CompressionOptions.quality(75),
      );
      stopwatch.stop();
      
      if (result.isSuccess) {
        final compressionRatio = (result.compressionRatio * 100).toStringAsFixed(1);
        final processingTime = stopwatch.elapsedMilliseconds;
        
        setState(() {
          _quickTestResult = 'Thành công! Tỷ lệ nén: $compressionRatio%, '
                           'Thời gian: ${processingTime}ms';
        });
      } else {
        setState(() {
          _quickTestResult = 'Lỗi: ${result.errorMessage}';
        });
      }
    } catch (e) {
      setState(() {
        _quickTestResult = 'Lỗi: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
