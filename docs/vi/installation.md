# Hướng dẫn cài đặt - Flutter DCT Compress

Hướng dẫn chi tiết cài đặt và cấu hình Flutter DCT Compress plugin cho các platform khác nhau.

## 📋 Y<PERSON>u cầu hệ thống

### Flutter SDK
- **Minimum**: Flutter 3.0.0
- **Recommended**: Flutter 3.10.0 hoặc mới hơn
- **Dart**: 3.0.0 hoặc mới hơn

### Platform Support
- ✅ **Android**: API level 21+ (Android 5.0+)
- ✅ **iOS**: iOS 11.0+
- ✅ **Web**: Chrome 88+, Firefox 85+, Safari 14+
- ✅ **Windows**: Windows 10+
- ✅ **macOS**: macOS 10.14+
- ✅ **Linux**: Ubuntu 18.04+

## 🚀 Cài đặt cơ bản

### 1. Thêm dependency

Thêm vào file `pubspec.yaml`:

```yaml
dependencies:
  flutter:
    sdk: flutter
  flutter_dct_compress: ^1.0.0
```

### 2. Cài đặt packages

```bash
flutter pub get
```

### 3. Import trong code

```dart
import 'package:flutter_dct_compress/flutter_dct_compress.dart';
```

### 4. Kiểm tra cài đặt

```dart
void main() async {
  // Test basic functionality
  final compressor = DctCompressor();
  print('Flutter DCT Compress đã sẵn sàng!');
}
```

## 📱 Cấu hình theo Platform

### Android

#### Minimum SDK Version

Trong `android/app/build.gradle`:

```gradle
android {
    compileSdkVersion 34
    
    defaultConfig {
        minSdkVersion 21  // Minimum required
        targetSdkVersion 34
    }
}
```

#### Permissions (nếu cần)

Trong `android/app/src/main/AndroidManifest.xml`:

```xml
<manifest xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- Chỉ cần nếu app đọc/ghi file từ external storage -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    
    <application>
        <!-- App configuration -->
    </application>
</manifest>
```

#### ProGuard (nếu sử dụng)

Trong `android/app/proguard-rules.pro`:

```proguard
# Flutter DCT Compress
-keep class com.example.flutter_dct_compress.** { *; }
-dontwarn com.example.flutter_dct_compress.**
```

### iOS

#### Minimum iOS Version

Trong `ios/Runner.xcodeproj/project.pbxproj` hoặc `ios/Podfile`:

```ruby
platform :ios, '11.0'
```

#### Info.plist (nếu cần)

Trong `ios/Runner/Info.plist`:

```xml
<dict>
    <!-- Chỉ cần nếu app truy cập photo library -->
    <key>NSPhotoLibraryUsageDescription</key>
    <string>App cần truy cập thư viện ảnh để nén ảnh</string>
    
    <key>NSCameraUsageDescription</key>
    <string>App cần truy cập camera để chụp và nén ảnh</string>
</dict>
```

### Web

#### HTML Meta Tags

Trong `web/index.html`:

```html
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <!-- Tối ưu cho image processing -->
    <meta http-equiv="Cross-Origin-Embedder-Policy" content="require-corp">
    <meta http-equiv="Cross-Origin-Opener-Policy" content="same-origin">
</head>
<body>
    <!-- App content -->
</body>
</html>
```

#### Web-specific Considerations

```dart
// Kiểm tra web capabilities
final capabilities = PlatformCapabilities.detect();

if (capabilities.isWeb) {
    // Web có giới hạn memory và không hỗ trợ file I/O
    final webOptions = CompressionOptions(
        quality: 75,
        useIsolate: false, // Web workers có thể không available
        maxImageSizeInMemory: 10 * 1024 * 1024, // 10MB limit
    );
}
```

### Desktop (Windows/macOS/Linux)

#### Windows

Không cần cấu hình đặc biệt. Plugin tự động detect và sử dụng Windows APIs.

#### macOS

Trong `macos/Runner/DebugProfile.entitlements` và `macos/Runner/Release.entitlements`:

```xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <!-- Nếu cần truy cập file system -->
    <key>com.apple.security.files.user-selected.read-write</key>
    <true/>
</dict>
</plist>
```

#### Linux

Cài đặt dependencies (Ubuntu/Debian):

```bash
sudo apt-get update
sudo apt-get install -y \
    libgtk-3-dev \
    libblkid-dev \
    liblzma-dev
```

## ⚙️ Cấu hình nâng cao

### Memory Configuration

```dart
// Cấu hình memory limits theo platform
class AppConfig {
    static CompressionOptions getDefaultOptions() {
        final capabilities = PlatformCapabilities.detect();
        
        if (capabilities.isMobile) {
            return CompressionOptions(
                quality: 75,
                maxImageSizeInMemory: 50 * 1024 * 1024, // 50MB
                enableMemoryOptimization: true,
            );
        } else if (capabilities.isWeb) {
            return CompressionOptions(
                quality: 70,
                maxImageSizeInMemory: 20 * 1024 * 1024, // 20MB
                useIsolate: false,
            );
        } else {
            // Desktop
            return CompressionOptions(
                quality: 80,
                maxImageSizeInMemory: 200 * 1024 * 1024, // 200MB
                useIsolate: true,
            );
        }
    }
}
```

### Performance Tuning

```dart
// Cấu hình performance theo device
class PerformanceConfig {
    static int getOptimalConcurrency() {
        final capabilities = PlatformCapabilities.detect();
        
        if (capabilities.isMobile) {
            return 2; // Conservative cho mobile
        } else if (capabilities.isWeb) {
            return 1; // Single thread cho web
        } else {
            return capabilities.recommendedConcurrency;
        }
    }
}
```

## 🔧 Troubleshooting

### Lỗi thường gặp

#### 1. "Plugin not found" Error

```bash
# Clean và rebuild
flutter clean
flutter pub get
flutter run
```

#### 2. Memory Issues trên Mobile

```dart
// Giảm memory usage
final mobileOptions = CompressionOptions(
    quality: 70,
    maxImageSizeInMemory: 20 * 1024 * 1024, // 20MB
    enableMemoryOptimization: true,
    useStreamingCompression: true,
);
```

#### 3. Web Performance Issues

```dart
// Tối ưu cho web
final webOptions = CompressionOptions(
    quality: 65,
    useIsolate: false,
    maxImageSizeInMemory: 10 * 1024 * 1024,
    progressive: false, // Tắt progressive cho web
);
```

#### 4. iOS Build Issues

```bash
# Clean iOS build
cd ios
rm -rf Pods Podfile.lock
pod install
cd ..
flutter clean
flutter run
```

#### 5. Android Build Issues

```bash
# Clean Android build
cd android
./gradlew clean
cd ..
flutter clean
flutter run
```

### Debug Mode

```dart
// Enable debug logging
void main() {
    // Set debug mode
    FlutterDctCompress.setDebugMode(true);
    
    runApp(MyApp());
}
```

### Performance Monitoring

```dart
// Monitor performance
class PerformanceMonitor {
    static void logCompressionResult(CompressionResult result) {
        print('Compression Stats:');
        print('- Time: ${result.processingTime.inMilliseconds}ms');
        print('- Ratio: ${result.compressionRatio}');
        print('- Memory: ${result.memoryUsed} bytes');
        print('- Used Isolate: ${result.usedIsolate}');
    }
}
```

## 📊 Verification

### Test Installation

Tạo file `test_installation.dart`:

```dart
import 'package:flutter_dct_compress/flutter_dct_compress.dart';
import 'dart:typed_data';

void main() async {
    print('Testing Flutter DCT Compress installation...');
    
    try {
        // Test basic compression
        final compressor = DctCompressor();
        
        // Create test image (64x64 RGB)
        final testData = Uint8List.fromList(
            List.generate(64 * 64 * 3, (i) => i % 256)
        );
        
        final imageInfo = ImageInfo(
            width: 64,
            height: 64,
            channels: 3,
            format: 'RGB',
            bitDepth: 8,
            hasAlpha: false,
            colorSpace: 'RGB',
        );
        
        final pixelData = PixelData.fromBytes(imageInfo, testData);
        
        final result = await compressor.compressImage(
            pixelData,
            options: CompressionOptions.quality(75),
        );
        
        if (result.isSuccess) {
            print('✅ Installation successful!');
            print('   Compression ratio: ${result.compressionRatio}');
            print('   Processing time: ${result.processingTime.inMilliseconds}ms');
        } else {
            print('❌ Installation failed: ${result.errorMessage}');
        }
        
    } catch (e) {
        print('❌ Installation error: $e');
    }
}
```

Chạy test:

```bash
dart test_installation.dart
```

### Platform Capabilities Check

```dart
void checkPlatformCapabilities() {
    final capabilities = PlatformCapabilities.detect();
    
    print('Platform Capabilities:');
    print('- Platform: ${capabilities.isWeb ? "Web" : capabilities.isMobile ? "Mobile" : "Desktop"}');
    print('- Isolates: ${capabilities.supportsIsolates}');
    print('- File I/O: ${capabilities.supportsFileIO}');
    print('- Max Memory: ${capabilities.maxMemoryLimit ~/ (1024 * 1024)}MB');
    print('- Recommended Concurrency: ${capabilities.recommendedConcurrency}');
}
```

## 🔄 Updates

### Cập nhật plugin

```bash
# Kiểm tra version mới
flutter pub outdated

# Cập nhật
flutter pub upgrade flutter_dct_compress

# Hoặc cập nhật specific version
flutter pub add flutter_dct_compress:^1.1.0
```

### Migration Guide

Khi cập nhật major version, xem [Migration Guide](migration_guide.md) để biết breaking changes.

## 📞 Support

Nếu gặp vấn đề trong quá trình cài đặt:

1. Kiểm tra [FAQ](faq.md)
2. Xem [GitHub Issues](https://github.com/your-repo/flutter_dct_compress/issues)
3. Tạo issue mới với thông tin:
   - Flutter version (`flutter --version`)
   - Platform và OS version
   - Error logs đầy đủ
   - Minimal reproduction code

---

Made with ❤️ in Vietnam
