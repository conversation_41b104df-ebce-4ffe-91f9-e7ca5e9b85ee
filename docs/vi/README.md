# Flutter DCT Compress

Plugin nén ảnh hiệu quả cho Flutter sử dụng thuật toán DCT (Discrete Cosine Transform) với hỗ trợ xử lý đa luồng và kiểm soát chất lượng thông minh.

## 🌟 Tính năng chính

- **Thu<PERSON>t toán DCT tối ưu**: Sử dụng DCT 2D với Fast DCT implementation
- **Adaptive Quantization**: Tự động điều chỉnh quantization dựa trên nội dung ảnh
- **Xử lý đa luồng**: Background processing với Isolate support
- **Kiểm soát chất lượng**: Bounds control với min/max compression ratio
- **Batch processing**: Xử lý hàng loạt với progress tracking
- **Cross-platform**: Hỗ trợ Android, iOS, Web, Desktop
- **Memory management**: Quản lý bộ nhớ hiệu quả
- **Multiple formats**: JPEG, PNG, BMP, TIFF, WebP

## 📦 Cài đặt

Thêm dependency vào `pubspec.yaml`:

```yaml
dependencies:
  flutter_dct_compress: ^1.0.0
```

Chạy lệnh:

```bash
flutter pub get
```

## 🚀 Sử dụng cơ bản

### Import package

```dart
import 'package:flutter_dct_compress/flutter_dct_compress.dart';
```

### Nén ảnh đơn lẻ

```dart
// Khởi tạo compressor
final compressor = DctCompressor();

// Tạo compression options
final options = CompressionOptions.quality(75);

// Nén ảnh từ file path
final result = await compressor.compressImage(
  'path/to/image.jpg',
  options: options,
);

if (result.isSuccess) {
  print('Nén thành công!');
  print('Tỷ lệ nén: ${result.compressionRatio * 100}%');
  print('Kích thước gốc: ${result.originalSize} bytes');
  print('Kích thước nén: ${result.compressedSize} bytes');
  
  // Lưu ảnh đã nén
  await File('output.jpg').writeAsBytes(result.compressedData);
} else {
  print('Lỗi: ${result.errorMessage}');
}
```

### Nén với Uint8List

```dart
// Từ Uint8List
final imageBytes = await File('image.jpg').readAsBytes();
final result = await compressor.compressImage(imageBytes, options: options);
```

### Nén với PixelData

```dart
// Từ PixelData (cho advanced use cases)
final imageInfo = ImageInfo(
  width: 800,
  height: 600,
  channels: 3,
  format: 'RGB',
  bitDepth: 8,
  hasAlpha: false,
  colorSpace: 'RGB',
);

final pixelData = PixelData.fromBytes(imageInfo, imageBytes);
final result = await compressor.compressImage(pixelData, options: options);
```

## ⚙️ Compression Options

### Quality-based compression

```dart
// High quality (95)
final highQuality = CompressionOptions.quality(95);

// Medium quality (75) - default
final mediumQuality = CompressionOptions.quality(75);

// Low quality (25)
final lowQuality = CompressionOptions.quality(25);
```

### Bounds-based compression

```dart
// Kiểm soát tỷ lệ nén trong khoảng 30-70%
final boundsOptions = CompressionOptions.withBounds(
  minRatio: 0.3,
  maxRatio: 0.7,
  quality: 75,
);
```

### Advanced options

```dart
final advancedOptions = CompressionOptions(
  quality: 80,
  colorSpace: ColorSpace.yuv,           // RGB hoặc YUV
  useAdaptiveQuantization: true,        // Adaptive quantization
  progressive: true,                    // Progressive JPEG
  useIsolate: true,                     // Background processing
  preserveMetadata: false,              // Giữ metadata
  maxImageSizeInMemory: 50 * 1024 * 1024, // 50MB memory limit
);
```

### Preset options

```dart
// High quality preset
final highQuality = CompressionOptions.highQuality();

// High compression preset
final highCompression = CompressionOptions.highCompression();

// Web optimized preset
final webOptimized = CompressionOptions.webOptimized();

// Mobile optimized preset
final mobileOptimized = CompressionOptions.mobileOptimized();

// Desktop optimized preset
final desktopOptimized = CompressionOptions.desktopOptimized();
```

## 📊 Batch Processing

### Xử lý hàng loạt cơ bản

```dart
final batchProcessor = BatchProcessor();

// Danh sách input (có thể là file paths, Uint8List, hoặc PixelData)
final inputs = [
  'image1.jpg',
  'image2.png',
  'image3.bmp',
];

final result = await batchProcessor.processBatch(
  inputs,
  options: CompressionOptions.quality(75),
);

print('Tổng số: ${result.totalCount}');
print('Thành công: ${result.successCount}');
print('Thất bại: ${result.failureCount}');
print('Tỷ lệ thành công: ${result.successRate * 100}%');
```

### Batch processing với progress tracking

```dart
final result = await batchProcessor.processBatch(
  inputs,
  options: CompressionOptions.quality(75),
  onProgress: (completed, total, currentItem) {
    print('Tiến trình: $completed/$total ($currentItem)');
  },
  onItemComplete: (index, result) {
    print('Hoàn thành item $index: ${result.isSuccess}');
  },
  onError: (index, error) {
    print('Lỗi item $index: $error');
  },
);
```

### Batch processing với cancellation

```dart
final cancellationToken = CancellationToken();

// Cancel sau 10 giây
Timer(Duration(seconds: 10), () {
  cancellationToken.cancel('Timeout');
});

final result = await batchProcessor.processBatch(
  inputs,
  options: CompressionOptions.quality(75),
  cancellationToken: cancellationToken,
  maxConcurrency: 3, // Số luồng đồng thời
);

if (result.wasCancelled) {
  print('Batch processing bị hủy');
}
```

## 🎯 Quality Metrics

### PSNR và SSIM

```dart
// Nén với quality metrics
final options = CompressionOptions(
  quality: 75,
  calculateQualityMetrics: true,
);

final result = await compressor.compressImage(imageData, options: options);

if (result.psnr != null) {
  print('PSNR: ${result.psnr!.toStringAsFixed(2)} dB');
}

if (result.ssim != null) {
  print('SSIM: ${result.ssim!.toStringAsFixed(3)}');
}
```

### So sánh chất lượng

```dart
final originalPixelData = // ... load original image
final compressedResult = // ... compression result

final metrics = QualityController.calculateQualityMetrics(
  originalPixelData,
  await compressor.decompress(compressedResult.compressedData),
);

print('PSNR: ${metrics.psnr} dB');
print('SSIM: ${metrics.ssim}');
print('MSE: ${metrics.mse}');
```

## 🔧 Advanced Features

### Adaptive Quantization

```dart
// Tạo adaptive quantization table
final pixelData = // ... your pixel data
final adaptiveTable = AdaptiveQuantization.createAdaptiveQuantizationTable(
  pixelData,
  baseQuality: 75,
  type: AdaptiveQuantizationType.hybrid,
);
```

### Custom DCT Transform

```dart
// Forward DCT
final dctBlock = List.generate(8, (i) => 
  List.generate(8, (j) => (i * 8 + j).toDouble())
);

final dctResult = DctTransform.forwardDct(dctBlock);

// Inverse DCT
final reconstructed = DctTransform.inverseDct(dctResult);

// Fast DCT (optimized)
final fastResult = DctTransform.fastForwardDct(dctBlock);
```

### Memory Management

```dart
final memoryManager = MemoryManager();

// Kiểm tra khả năng allocate
if (memoryManager.canAllocateForImage(1920, 1080, 3)) {
  // Có thể xử lý ảnh này
}

// Estimate memory usage
final estimatedMemory = memoryManager.estimateMemoryForImage(1920, 1080, 3);
print('Ước tính bộ nhớ: $estimatedMemory bytes');

// Auto cleanup
memoryManager.autoCleanupIfNeeded();
```

## 📱 Platform-specific Features

### Web Platform

```dart
// Kiểm tra platform capabilities
final capabilities = PlatformCapabilities.detect();

if (capabilities.isWeb) {
  // Web-specific optimizations
  final webOptions = CompressionOptions(
    quality: 75,
    useIsolate: false, // Web có thể không hỗ trợ isolate
    maxImageSizeInMemory: 10 * 1024 * 1024, // 10MB limit cho web
  );
}
```

### Mobile Platform

```dart
if (capabilities.isMobile) {
  // Mobile-optimized settings
  final mobileOptions = CompressionOptions.mobileOptimized();
  
  // Hoặc custom mobile settings
  final customMobile = CompressionOptions(
    quality: 70,
    maxImageSizeInMemory: 20 * 1024 * 1024, // 20MB
    useIsolate: true,
    enableMemoryOptimization: true,
  );
}
```

## 🔍 Error Handling

```dart
try {
  final result = await compressor.compressImage(imageData, options: options);
  
  if (result.isSuccess) {
    // Xử lý thành công
  } else {
    // Xử lý lỗi từ result
    print('Compression failed: ${result.errorMessage}');
  }
} on CompressionException catch (e) {
  // Xử lý compression-specific errors
  print('Compression error: ${e.message}');
} on MemoryException catch (e) {
  // Xử lý memory-related errors
  print('Memory error: ${e.message}');
} catch (e) {
  // Xử lý các lỗi khác
  print('Unexpected error: $e');
}
```

## 📈 Performance Tips

### 1. Chọn quality phù hợp
- **95-100**: Chất lượng cao nhất, file size lớn
- **75-85**: Cân bằng tốt giữa chất lượng và size
- **50-70**: Compression cao, chất lượng chấp nhận được
- **1-50**: Compression rất cao, chất lượng thấp

### 2. Sử dụng color space phù hợp
- **RGB**: Tốt cho ảnh có nhiều màu sắc
- **YUV**: Tốt cho ảnh tự nhiên, compression tốt hơn

### 3. Batch processing optimization
```dart
// Sử dụng concurrency phù hợp
final maxConcurrency = capabilities.recommendedConcurrency;

// Memory management cho batch
final batchOptions = CompressionOptions(
  quality: 75,
  maxImageSizeInMemory: capabilities.maxMemoryLimit ~/ 10,
);
```

### 4. Memory optimization
```dart
// Cho ảnh lớn
final largeImageOptions = CompressionOptions(
  quality: 75,
  useStreamingCompression: true,
  enableMemoryOptimization: true,
  maxImageSizeInMemory: 50 * 1024 * 1024,
);
```

## 🧪 Testing

Xem thêm trong thư mục `test/` để biết cách viết tests cho ứng dụng sử dụng plugin này.

## 📚 API Reference

Xem [API Documentation](api_reference.md) để biết chi tiết về tất cả classes và methods.

## 🤝 Contributing

Xem [Contributing Guide](../CONTRIBUTING.md) để biết cách đóng góp cho project.

## 📄 License

MIT License - xem [LICENSE](../../LICENSE) file để biết chi tiết.

## 🆘 Support

- [GitHub Issues](https://github.com/your-repo/flutter_dct_compress/issues)
- [Documentation](https://your-docs-site.com)
- [Examples](../../example/)

---

Made with ❤️ in Vietnam
