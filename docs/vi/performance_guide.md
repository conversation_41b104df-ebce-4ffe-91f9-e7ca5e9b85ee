# Performance Guide - Flutter DCT Compress

Hướng dẫn tối ưu hiệu năng cho Flutter DCT Compress plugin.

## 📊 Tổng quan Performance

Flutter DCT Compress được thiết kế để đạt hiệu năng cao với:
- **DCT Algorithm tối ưu**: Fast DCT implementation
- **Multi-threading**: Background processing với Isolates
- **Memory management**: Efficient memory usage
- **Adaptive optimization**: Tự động điều chỉnh theo platform

## ⚡ Optimization Strategies

### 1. Quality Settings

#### Chọn Quality Level phù hợp

```dart
// High quality - Slow compression, Large file
final highQuality = CompressionOptions.quality(95);

// Balanced - Good compression speed và quality
final balanced = CompressionOptions.quality(75); // Recommended

// High compression - Fast compression, Small file
final highCompression = CompressionOptions.quality(50);

// Maximum compression - Fastest, Smallest file
final maxCompression = CompressionOptions.quality(25);
```

#### Quality vs Performance Benchmark

| Quality | Compression Speed | File Size | Visual Quality |
|---------|------------------|-----------|----------------|
| 95      | Slow (100%)      | Large     | Excellent      |
| 85      | Medium (150%)    | Medium    | Very Good      |
| 75      | Fast (200%)      | Small     | Good           |
| 50      | Very Fast (300%) | Very Small| Acceptable     |
| 25      | Fastest (400%)   | Minimal   | Poor           |

### 2. Color Space Optimization

```dart
// RGB - Tốt cho ảnh có nhiều màu sắc, chậm hơn
final rgbOptions = CompressionOptions(
    quality: 75,
    colorSpace: ColorSpace.rgb,
);

// YUV - Tốt cho ảnh tự nhiên, nhanh hơn và nén tốt hơn
final yuvOptions = CompressionOptions(
    quality: 75,
    colorSpace: ColorSpace.yuv, // Recommended
);
```

#### Performance Comparison

```dart
// Benchmark color space performance
Future<void> benchmarkColorSpace() async {
    final compressor = DctCompressor();
    final testImage = SampleImages.createTestImage(512, 512, 3);
    
    // RGB Benchmark
    final rgbStopwatch = Stopwatch()..start();
    final rgbResult = await compressor.compressImage(
        testImage,
        options: CompressionOptions(quality: 75, colorSpace: ColorSpace.rgb),
    );
    rgbStopwatch.stop();
    
    // YUV Benchmark
    final yuvStopwatch = Stopwatch()..start();
    final yuvResult = await compressor.compressImage(
        testImage,
        options: CompressionOptions(quality: 75, colorSpace: ColorSpace.yuv),
    );
    yuvStopwatch.stop();
    
    print('RGB: ${rgbStopwatch.elapsedMilliseconds}ms, Ratio: ${rgbResult.compressionRatio}');
    print('YUV: ${yuvStopwatch.elapsedMilliseconds}ms, Ratio: ${yuvResult.compressionRatio}');
}
```

### 3. Adaptive Quantization

```dart
// Disable adaptive quantization cho speed
final fastOptions = CompressionOptions(
    quality: 75,
    useAdaptiveQuantization: false, // Faster
);

// Enable adaptive quantization cho better quality
final qualityOptions = CompressionOptions(
    quality: 75,
    useAdaptiveQuantization: true, // Better quality, slower
);
```

### 4. Progressive vs Standard

```dart
// Standard - Faster compression
final standardOptions = CompressionOptions(
    quality: 75,
    progressive: false, // Faster
);

// Progressive - Better for web, slightly slower
final progressiveOptions = CompressionOptions(
    quality: 75,
    progressive: true, // Better for web loading
);
```

## 🚀 Multi-threading Optimization

### Isolate Usage

```dart
// Main thread - Faster cho small images
final mainThreadOptions = CompressionOptions(
    quality: 75,
    useIsolate: false,
);

// Background thread - Better cho large images
final isolateOptions = CompressionOptions(
    quality: 75,
    useIsolate: true, // Recommended cho images > 1MB
);
```

### Optimal Concurrency

```dart
class ConcurrencyOptimizer {
    static int getOptimalConcurrency() {
        final capabilities = PlatformCapabilities.detect();
        
        if (capabilities.isMobile) {
            // Mobile: Conservative để tránh overheating
            return math.min(2, capabilities.recommendedConcurrency);
        } else if (capabilities.isWeb) {
            // Web: Single thread thường tốt nhất
            return 1;
        } else {
            // Desktop: Có thể sử dụng nhiều cores
            return capabilities.recommendedConcurrency;
        }
    }
}
```

### Batch Processing Optimization

```dart
Future<void> optimizedBatchProcessing(List<String> imagePaths) async {
    final batchProcessor = BatchProcessor();
    final concurrency = ConcurrencyOptimizer.getOptimalConcurrency();
    
    // Chia batch thành chunks nhỏ hơn
    const chunkSize = 10;
    final chunks = _chunkList(imagePaths, chunkSize);
    
    for (final chunk in chunks) {
        final result = await batchProcessor.processBatch(
            chunk,
            options: CompressionOptions(
                quality: 75,
                useIsolate: true,
                maxImageSizeInMemory: 50 * 1024 * 1024, // 50MB
            ),
            maxConcurrency: concurrency,
        );
        
        print('Chunk processed: ${result.successCount}/${result.totalCount}');
    }
}

List<List<T>> _chunkList<T>(List<T> list, int chunkSize) {
    final chunks = <List<T>>[];
    for (int i = 0; i < list.length; i += chunkSize) {
        chunks.add(list.sublist(i, math.min(i + chunkSize, list.length)));
    }
    return chunks;
}
```

## 💾 Memory Optimization

### Memory-Efficient Settings

```dart
// Memory-optimized options
final memoryOptimizedOptions = CompressionOptions(
    quality: 75,
    maxImageSizeInMemory: 20 * 1024 * 1024, // 20MB limit
    enableMemoryOptimization: true,
    useStreamingCompression: true, // Process in chunks
);
```

### Platform-Specific Memory Limits

```dart
class MemoryOptimizer {
    static CompressionOptions getMemoryOptimizedOptions() {
        final capabilities = PlatformCapabilities.detect();
        
        if (capabilities.isMobile) {
            return CompressionOptions(
                quality: 70,
                maxImageSizeInMemory: 30 * 1024 * 1024, // 30MB
                enableMemoryOptimization: true,
                useStreamingCompression: true,
            );
        } else if (capabilities.isWeb) {
            return CompressionOptions(
                quality: 65,
                maxImageSizeInMemory: 15 * 1024 * 1024, // 15MB
                enableMemoryOptimization: true,
                useIsolate: false,
            );
        } else {
            return CompressionOptions(
                quality: 80,
                maxImageSizeInMemory: 100 * 1024 * 1024, // 100MB
                enableMemoryOptimization: false,
            );
        }
    }
}
```

### Memory Monitoring

```dart
class MemoryMonitor {
    static void logMemoryUsage(CompressionResult result) {
        if (result.memoryUsed != null) {
            final memoryMB = result.memoryUsed! / (1024 * 1024);
            print('Memory used: ${memoryMB.toStringAsFixed(2)} MB');
            
            if (memoryMB > 50) {
                print('⚠️ High memory usage detected');
            }
        }
    }
    
    static Future<void> monitorBatchMemory(
        BatchProcessor processor,
        List<dynamic> inputs,
        CompressionOptions options,
    ) async {
        final memoryManager = MemoryManager();
        final initialMemory = memoryManager.getCurrentMemoryUsage();
        
        final result = await processor.processBatch(inputs, options: options);
        
        final finalMemory = memoryManager.getCurrentMemoryUsage();
        final memoryGrowth = finalMemory - initialMemory;
        
        print('Batch memory growth: ${memoryGrowth / (1024 * 1024)} MB');
    }
}
```

## 📏 Image Size Optimization

### Size-Based Strategy

```dart
class SizeOptimizer {
    static CompressionOptions getOptionsForSize(int width, int height) {
        final pixels = width * height;
        
        if (pixels < 100000) { // < 0.1MP
            return CompressionOptions(
                quality: 85,
                useIsolate: false, // Main thread faster cho small images
                useAdaptiveQuantization: false,
            );
        } else if (pixels < 1000000) { // < 1MP
            return CompressionOptions(
                quality: 80,
                useIsolate: true,
                useAdaptiveQuantization: true,
            );
        } else if (pixels < 5000000) { // < 5MP
            return CompressionOptions(
                quality: 75,
                useIsolate: true,
                useAdaptiveQuantization: true,
                enableMemoryOptimization: true,
            );
        } else { // > 5MP
            return CompressionOptions(
                quality: 70,
                useIsolate: true,
                useAdaptiveQuantization: true,
                enableMemoryOptimization: true,
                useStreamingCompression: true,
            );
        }
    }
}
```

### Pre-processing Optimization

```dart
// Resize large images trước khi compress
Future<CompressionResult> compressWithResize(
    PixelData originalImage,
    {int? maxWidth, int? maxHeight}
) async {
    PixelData imageToCompress = originalImage;
    
    // Resize nếu cần
    if (maxWidth != null || maxHeight != null) {
        final shouldResize = (maxWidth != null && originalImage.info.width > maxWidth) ||
                           (maxHeight != null && originalImage.info.height > maxHeight);
        
        if (shouldResize) {
            imageToCompress = await ImageResizer.resize(
                originalImage,
                maxWidth: maxWidth,
                maxHeight: maxHeight,
                maintainAspectRatio: true,
            );
        }
    }
    
    // Compress với optimized options
    final options = SizeOptimizer.getOptionsForSize(
        imageToCompress.info.width,
        imageToCompress.info.height,
    );
    
    final compressor = DctCompressor();
    return await compressor.compressImage(imageToCompress, options: options);
}
```

## 🎯 Performance Benchmarking

### Benchmark Suite

```dart
class PerformanceBenchmark {
    static Future<void> runFullBenchmark() async {
        print('🚀 Starting Performance Benchmark...\n');
        
        await _benchmarkQualityLevels();
        await _benchmarkColorSpaces();
        await _benchmarkImageSizes();
        await _benchmarkConcurrency();
        
        print('✅ Benchmark completed!');
    }
    
    static Future<void> _benchmarkQualityLevels() async {
        print('📊 Benchmarking Quality Levels:');
        
        final compressor = DctCompressor();
        final testImage = SampleImages.createTestImage(256, 256, 3);
        final qualities = [25, 50, 75, 95];
        
        for (final quality in qualities) {
            final stopwatch = Stopwatch()..start();
            
            final result = await compressor.compressImage(
                testImage,
                options: CompressionOptions.quality(quality),
            );
            
            stopwatch.stop();
            
            print('  Quality $quality: ${stopwatch.elapsedMilliseconds}ms, '
                  'Ratio: ${(result.compressionRatio * 100).toStringAsFixed(1)}%');
        }
        print('');
    }
    
    static Future<void> _benchmarkColorSpaces() async {
        print('🎨 Benchmarking Color Spaces:');
        
        final compressor = DctCompressor();
        final testImage = SampleImages.createTestImage(256, 256, 3);
        
        // RGB
        final rgbStopwatch = Stopwatch()..start();
        final rgbResult = await compressor.compressImage(
            testImage,
            options: CompressionOptions(quality: 75, colorSpace: ColorSpace.rgb),
        );
        rgbStopwatch.stop();
        
        // YUV
        final yuvStopwatch = Stopwatch()..start();
        final yuvResult = await compressor.compressImage(
            testImage,
            options: CompressionOptions(quality: 75, colorSpace: ColorSpace.yuv),
        );
        yuvStopwatch.stop();
        
        print('  RGB: ${rgbStopwatch.elapsedMilliseconds}ms, '
              'Ratio: ${(rgbResult.compressionRatio * 100).toStringAsFixed(1)}%');
        print('  YUV: ${yuvStopwatch.elapsedMilliseconds}ms, '
              'Ratio: ${(yuvResult.compressionRatio * 100).toStringAsFixed(1)}%');
        print('');
    }
    
    static Future<void> _benchmarkImageSizes() async {
        print('📏 Benchmarking Image Sizes:');
        
        final compressor = DctCompressor();
        final sizes = [64, 128, 256, 512];
        
        for (final size in sizes) {
            final testImage = SampleImages.createTestImage(size, size, 3);
            final stopwatch = Stopwatch()..start();
            
            final result = await compressor.compressImage(
                testImage,
                options: CompressionOptions.quality(75),
            );
            
            stopwatch.stop();
            
            final pixels = size * size;
            final pixelsPerMs = pixels / stopwatch.elapsedMilliseconds;
            
            print('  ${size}x$size: ${stopwatch.elapsedMilliseconds}ms, '
                  '${pixelsPerMs.toStringAsFixed(0)} pixels/ms');
        }
        print('');
    }
    
    static Future<void> _benchmarkConcurrency() async {
        print('⚡ Benchmarking Concurrency:');
        
        final batchProcessor = BatchProcessor();
        final inputs = List.generate(20, (i) => 
            SampleImages.createTestImage(64, 64, 3, seed: i)
        );
        
        final concurrencyLevels = [1, 2, 4];
        
        for (final concurrency in concurrencyLevels) {
            final stopwatch = Stopwatch()..start();
            
            final result = await batchProcessor.processBatch(
                inputs,
                options: CompressionOptions.quality(75),
                maxConcurrency: concurrency,
            );
            
            stopwatch.stop();
            
            final itemsPerSecond = inputs.length / (stopwatch.elapsedMilliseconds / 1000);
            
            print('  Concurrency $concurrency: ${stopwatch.elapsedMilliseconds}ms, '
                  '${itemsPerSecond.toStringAsFixed(1)} items/sec');
        }
        print('');
    }
}
```

## 📱 Platform-Specific Optimizations

### Mobile Optimization

```dart
class MobileOptimizer {
    static CompressionOptions getMobileOptions() {
        return CompressionOptions(
            quality: 70, // Lower quality cho mobile
            colorSpace: ColorSpace.yuv,
            useAdaptiveQuantization: true,
            progressive: false, // Tắt progressive
            useIsolate: true,
            maxImageSizeInMemory: 30 * 1024 * 1024, // 30MB
            enableMemoryOptimization: true,
        );
    }
    
    static Future<void> warmupCompressor() async {
        // Warm up compressor với small image
        final compressor = DctCompressor();
        final warmupImage = SampleImages.createTestImage(32, 32, 3);
        
        await compressor.compressImage(
            warmupImage,
            options: CompressionOptions.quality(75),
        );
    }
}
```

### Web Optimization

```dart
class WebOptimizer {
    static CompressionOptions getWebOptions() {
        return CompressionOptions(
            quality: 65, // Lower quality cho web
            colorSpace: ColorSpace.yuv,
            useAdaptiveQuantization: false, // Tắt để tăng speed
            progressive: true, // Tốt cho web loading
            useIsolate: false, // Web workers có thể không stable
            maxImageSizeInMemory: 15 * 1024 * 1024, // 15MB
            enableMemoryOptimization: true,
        );
    }
}
```

### Desktop Optimization

```dart
class DesktopOptimizer {
    static CompressionOptions getDesktopOptions() {
        return CompressionOptions(
            quality: 80, // Higher quality cho desktop
            colorSpace: ColorSpace.yuv,
            useAdaptiveQuantization: true,
            progressive: false,
            useIsolate: true,
            maxImageSizeInMemory: 200 * 1024 * 1024, // 200MB
            enableMemoryOptimization: false, // Desktop có nhiều RAM
        );
    }
}
```

## 🔍 Performance Monitoring

### Real-time Monitoring

```dart
class PerformanceMonitor {
    static final List<CompressionResult> _results = [];
    
    static void recordResult(CompressionResult result) {
        _results.add(result);
        
        // Keep only last 100 results
        if (_results.length > 100) {
            _results.removeAt(0);
        }
        
        _analyzePerformance();
    }
    
    static void _analyzePerformance() {
        if (_results.length < 10) return;
        
        final recentResults = _results.takeLast(10).toList();
        final avgTime = recentResults
            .map((r) => r.processingTime.inMilliseconds)
            .reduce((a, b) => a + b) / recentResults.length;
        
        final avgRatio = recentResults
            .map((r) => r.compressionRatio)
            .reduce((a, b) => a + b) / recentResults.length;
        
        print('📊 Performance Stats (last 10):');
        print('   Avg Time: ${avgTime.toStringAsFixed(1)}ms');
        print('   Avg Ratio: ${(avgRatio * 100).toStringAsFixed(1)}%');
        
        if (avgTime > 5000) {
            print('⚠️ Performance warning: Slow compression detected');
        }
    }
    
    static Map<String, dynamic> getPerformanceReport() {
        if (_results.isEmpty) return {};
        
        final times = _results.map((r) => r.processingTime.inMilliseconds).toList();
        final ratios = _results.map((r) => r.compressionRatio).toList();
        
        return {
            'totalCompressions': _results.length,
            'avgTime': times.reduce((a, b) => a + b) / times.length,
            'minTime': times.reduce(math.min),
            'maxTime': times.reduce(math.max),
            'avgRatio': ratios.reduce((a, b) => a + b) / ratios.length,
            'successRate': _results.where((r) => r.isSuccess).length / _results.length,
        };
    }
}
```

---

## 💡 Best Practices Summary

1. **Quality**: Sử dụng 75 cho balanced performance
2. **Color Space**: Prefer YUV cho natural images
3. **Concurrency**: 2-3 threads cho mobile, 4+ cho desktop
4. **Memory**: Set appropriate limits theo platform
5. **Batch Size**: Process trong chunks 10-20 items
6. **Monitoring**: Track performance metrics
7. **Platform**: Optimize settings theo từng platform

Để biết thêm chi tiết, xem [API Reference](api_reference.md) và [Example App](../../example/).

---

Made with ❤️ in Vietnam
