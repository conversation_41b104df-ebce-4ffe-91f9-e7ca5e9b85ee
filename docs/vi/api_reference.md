# API Reference - Flutter DCT Compress

Tài liệu tham khảo API đầy đủ cho Flutter DCT Compress plugin.

## 📋 <PERSON><PERSON><PERSON> lục

- [Core Classes](#core-classes)
- [Compression Options](#compression-options)
- [Results và Metrics](#results-và-metrics)
- [DCT Algorithm](#dct-algorithm)
- [Batch Processing](#batch-processing)
- [Isolate Support](#isolate-support)
- [Utilities](#utilities)

## Core Classes

### DctCompressor

Class chính để thực hiện compression.

```dart
class DctCompressor {
  /// Nén ảnh từ nhiều loại input
  Future<CompressionResult> compressImage(
    dynamic input, {
    CompressionOptions? options,
  });
  
  /// Giải nén dữ liệu đã nén
  Future<PixelData> decompress(Uint8List compressedData);
  
  /// Validate input trước khi nén
  bool validateInput(dynamic input);
}
```

#### Methods

##### `compressImage(input, {options})`

<PERSON><PERSON> ảnh từ input đa dạng.

**Parameters:**
- `input` (dynamic): <PERSON><PERSON> thể là:
  - `String`: File path
  - `Uint8List`: Raw image bytes
  - `PixelData`: Structured pixel data
- `options` (CompressionOptions?): Tùy chọn nén

**Returns:** `Future<CompressionResult>`

**Example:**
```dart
final compressor = DctCompressor();

// Từ file path
final result1 = await compressor.compressImage('image.jpg');

// Từ bytes
final bytes = await File('image.jpg').readAsBytes();
final result2 = await compressor.compressImage(bytes);

// Từ PixelData
final pixelData = PixelData.fromBytes(imageInfo, rawData);
final result3 = await compressor.compressImage(pixelData);
```

##### `decompress(compressedData)`

Giải nén dữ liệu đã được nén.

**Parameters:**
- `compressedData` (Uint8List): Dữ liệu đã nén

**Returns:** `Future<PixelData>`

**Example:**
```dart
final decompressed = await compressor.decompress(result.compressedData);
print('Kích thước: ${decompressed.info.width}x${decompressed.info.height}');
```

### PixelData

Class đại diện cho dữ liệu pixel của ảnh.

```dart
class PixelData {
  final ImageInfo info;
  final Uint8List data;
  
  /// Tạo từ raw bytes
  factory PixelData.fromBytes(ImageInfo info, Uint8List data);
  
  /// Lấy pixel tại vị trí (x, y)
  List<int> getPixel(int x, int y);
  
  /// Set pixel tại vị trí (x, y)
  void setPixel(int x, int y, List<int> pixel);
  
  /// Copy với modifications
  PixelData copyWith({ImageInfo? info, Uint8List? data});
}
```

### ImageInfo

Thông tin metadata của ảnh.

```dart
class ImageInfo {
  final int width;
  final int height;
  final int channels;
  final String format;
  final int bitDepth;
  final bool hasAlpha;
  final String colorSpace;
  
  const ImageInfo({
    required this.width,
    required this.height,
    required this.channels,
    required this.format,
    required this.bitDepth,
    required this.hasAlpha,
    required this.colorSpace,
  });
}
```

## Compression Options

### CompressionOptions

Class cấu hình các tùy chọn nén.

```dart
class CompressionOptions {
  final int quality;
  final ColorSpace colorSpace;
  final bool useAdaptiveQuantization;
  final bool progressive;
  final bool useIsolate;
  final bool preserveMetadata;
  final double? minCompressionRatio;
  final double? maxCompressionRatio;
  final int? maxImageSizeInMemory;
  final bool enableMemoryOptimization;
  final bool useStreamingCompression;
  final bool calculateQualityMetrics;
  
  const CompressionOptions({
    this.quality = 85,
    this.colorSpace = ColorSpace.yuv,
    this.useAdaptiveQuantization = true,
    this.progressive = false,
    this.useIsolate = true,
    this.preserveMetadata = true,
    this.minCompressionRatio,
    this.maxCompressionRatio,
    this.maxImageSizeInMemory,
    this.enableMemoryOptimization = false,
    this.useStreamingCompression = false,
    this.calculateQualityMetrics = false,
  });
}
```

#### Factory Constructors

##### `CompressionOptions.quality(int quality)`

Tạo options với quality cụ thể.

```dart
final options = CompressionOptions.quality(75);
```

##### `CompressionOptions.withBounds({minRatio, maxRatio, quality})`

Tạo options với bounds control.

```dart
final options = CompressionOptions.withBounds(
  minRatio: 0.3,
  maxRatio: 0.7,
  quality: 75,
);
```

##### Preset Options

```dart
// High quality preset
final highQuality = CompressionOptions.highQuality();

// High compression preset  
final highCompression = CompressionOptions.highCompression();

// Web optimized
final webOptimized = CompressionOptions.webOptimized();

// Mobile optimized
final mobileOptimized = CompressionOptions.mobileOptimized();

// Desktop optimized
final desktopOptimized = CompressionOptions.desktopOptimized();
```

### ColorSpace

Enum định nghĩa color space.

```dart
enum ColorSpace {
  rgb,  // RGB color space
  yuv,  // YUV color space
}
```

### OutputFormat

Enum định nghĩa format output.

```dart
enum OutputFormat {
  jpeg,
  png,
  webp,
}
```

## Results và Metrics

### CompressionResult

Kết quả của quá trình nén.

```dart
class CompressionResult {
  final Uint8List compressedData;
  final double compressionRatio;
  final int originalSize;
  final int compressedSize;
  final Duration processingTime;
  final ImageInfo imageInfo;
  final String taskId;
  final int qualityUsed;
  final double? psnr;
  final double? ssim;
  final bool isSuccess;
  final String? errorMessage;
  final bool usedIsolate;
  final bool usedAdaptiveQuantization;
  final int? memoryUsed;
  
  // Computed properties
  int get spaceSaved => originalSize - compressedSize;
  double get compressionPercentage => (1.0 - compressionRatio) * 100;
}
```

#### Factory Constructors

##### `CompressionResult.error({taskId, errorMessage, imageInfo, originalSize})`

Tạo result cho trường hợp lỗi.

```dart
final errorResult = CompressionResult.error(
  taskId: 'task_1',
  errorMessage: 'Invalid input format',
  imageInfo: imageInfo,
  originalSize: 1024,
);
```

### QualityMetrics

Metrics đánh giá chất lượng ảnh.

```dart
class QualityMetrics {
  final double psnr;    // Peak Signal-to-Noise Ratio (dB)
  final double ssim;    // Structural Similarity Index
  final double mse;     // Mean Squared Error
  
  const QualityMetrics({
    required this.psnr,
    required this.ssim,
    required this.mse,
  });
}
```

## DCT Algorithm

### DctTransform

Class thực hiện DCT transform.

```dart
class DctTransform {
  /// Forward DCT 2D
  static List<List<double>> forwardDct(List<List<double>> block);
  
  /// Inverse DCT 2D
  static List<List<double>> inverseDct(List<List<double>> dctBlock);
  
  /// Fast Forward DCT (optimized)
  static List<List<double>> fastForwardDct(List<List<double>> block);
  
  /// Validate DCT transform accuracy
  static bool validateTransform(List<List<int>> block, {double threshold = 1.0});
}
```

### Quantization

Class xử lý quantization.

```dart
class Quantization {
  /// Standard luminance quantization table
  static const List<List<int>> standardLuminanceTable;
  
  /// Standard chrominance quantization table
  static const List<List<int>> standardChrominanceTable;
  
  /// Tạo quantization table từ quality
  static List<List<int>> createQuantizationTable(
    int quality, {
    bool isLuminance = true,
  });
  
  /// Quantize DCT coefficients
  static List<List<int>> quantize(
    List<List<double>> dctBlock,
    List<List<int>> quantTable,
  );
  
  /// Dequantize coefficients
  static List<List<double>> dequantize(
    List<List<int>> quantizedBlock,
    List<List<int>> quantTable,
  );
}
```

### AdaptiveQuantization

Class xử lý adaptive quantization.

```dart
class AdaptiveQuantization {
  /// Tạo adaptive quantization table
  static List<List<int>> createAdaptiveQuantizationTable(
    PixelData pixelData,
    int baseQuality, {
    double? targetRatio,
    AdaptiveQuantizationType type = AdaptiveQuantizationType.hybrid,
    bool isLuminance = true,
  });
}
```

#### AdaptiveQuantizationType

```dart
enum AdaptiveQuantizationType {
  contentBased,     // Dựa trên complexity của ảnh
  ratioBased,       // Dựa trên target compression ratio
  perceptualBased,  // Dựa trên perceptual quality
  hybrid,           // Kết hợp nhiều yếu tố
}
```

## Batch Processing

### BatchProcessor

Class xử lý batch compression.

```dart
class BatchProcessor {
  /// Xử lý batch compression
  Future<BatchProcessingResult> processBatch(
    List<dynamic> inputs, {
    CompressionOptions? options,
    BatchProgressCallback? onProgress,
    ItemCompletionCallback? onItemComplete,
    BatchErrorCallback? onError,
    CancellationToken? cancellationToken,
    int maxConcurrency = 3,
  });
  
  /// Estimate thời gian xử lý batch
  Future<Duration> estimateBatchProcessingTime(
    List<dynamic> inputs,
    CompressionOptions options,
  );
}
```

### BatchProcessingResult

Kết quả batch processing.

```dart
class BatchProcessingResult {
  final List<CompressionResult?> results;
  final int successCount;
  final int failureCount;
  final int cancelledCount;
  final Duration totalProcessingTime;
  final bool wasCancelled;
  final Map<int, String> errors;
  
  // Computed properties
  int get totalCount => results.length;
  double get successRate => totalCount > 0 ? successCount / totalCount : 0.0;
  bool get isCompleteSuccess => successCount == totalCount && !wasCancelled;
}
```

### CancellationToken

Token để cancel operations.

```dart
class CancellationToken {
  bool get isCancelled;
  String? get reason;
  Future<void> get cancelled;
  
  void cancel([String? reason]);
  void throwIfCancelled();
}
```

## Isolate Support

### ProgressTracker

Class theo dõi tiến trình.

```dart
class ProgressTracker {
  Stream<ProgressInfo> get progressStream;
  
  TaskProgressTracker createTracker({
    required String taskId,
    required int totalItems,
    ProgressCallback? callback,
  });
  
  void updateProgress(String taskId, {
    int? completed,
    String? currentItem,
    ProgressStatus? status,
    Map<String, dynamic>? metadata,
  });
  
  ProgressInfo? getProgress(String taskId);
  List<ProgressInfo> getAllActiveProgress();
}
```

### TaskManager

Class quản lý tasks.

```dart
class TaskManager {
  Stream<TaskInfo> get taskStatusStream;
  Stream<TaskManagerStats> get statsStream;
  
  String addTask({
    required String name,
    TaskPriority priority = TaskPriority.normal,
    Map<String, dynamic>? metadata,
    CancellationToken? cancellationToken,
  });
  
  bool cancelTask(String taskId, [String? reason]);
  bool pauseTask(String taskId);
  bool resumeTask(String taskId);
  
  TaskInfo? getTask(String taskId);
  List<TaskInfo> getAllTasks();
  TaskManagerStats getStats();
}
```

## Utilities

### MemoryManager

Class quản lý bộ nhớ.

```dart
class MemoryManager {
  int getCurrentMemoryUsage();
  int getAvailableMemory();
  
  bool canAllocateForImage(int width, int height, int channels);
  int estimateMemoryForImage(int width, int height, int channels);
  
  void autoCleanupIfNeeded();
}
```

### PlatformCapabilities

Class detect platform capabilities.

```dart
class PlatformCapabilities {
  final bool isWeb;
  final bool isMobile;
  final bool isDesktop;
  final bool supportsIsolates;
  final bool supportsFileIO;
  final int maxMemoryLimit;
  final int recommendedConcurrency;
  
  static PlatformCapabilities detect();
}
```

### FormatHandler

Class xử lý format detection.

```dart
class FormatHandler {
  static ImageFormat detectFormat(Uint8List data);
  static bool isFormatSupported(ImageFormat format);
  static ImageFormat getFormatFromExtension(String extension);
}
```

### ColorSpaceConverter

Class chuyển đổi color space.

```dart
class ColorSpaceConverter {
  static List<List<List<int>>> convertRgbToYuv(List<List<List<int>>> rgbData);
  static List<List<List<int>>> convertYuvToRgb(List<List<List<int>>> yuvData);
}
```

## Exceptions

### CompressionException

Exception chung cho compression errors.

```dart
class CompressionException implements Exception {
  final String message;
  final String? details;
  
  const CompressionException(this.message, [this.details]);
}
```

### MemoryException

Exception cho memory-related errors.

```dart
class MemoryException extends CompressionException {
  final int requestedMemory;
  final int availableMemory;
  
  const MemoryException(
    String message,
    this.requestedMemory,
    this.availableMemory,
  ) : super(message);
}
```

### TaskCancelledException

Exception khi task bị cancel.

```dart
class TaskCancelledException implements Exception {
  final String message;
  
  const TaskCancelledException(this.message);
}
```

---

## Constants

### Quality Levels

```dart
class QualityLevels {
  static const int minimum = 1;
  static const int low = 25;
  static const int medium = 50;
  static const int high = 75;
  static const int veryHigh = 90;
  static const int maximum = 100;
}
```

### Memory Limits

```dart
class MemoryLimits {
  static const int webDefault = 50 * 1024 * 1024;      // 50MB
  static const int mobileDefault = 100 * 1024 * 1024;   // 100MB
  static const int desktopDefault = 500 * 1024 * 1024;  // 500MB
}
```

---

Để biết thêm chi tiết và examples, xem [README.md](README.md) và [example app](../../example/).
