/// Hằng số và bảng tra cứu cho DCT compression
/// 
/// File này chứa tất cả các hằng số, bảng tra cứu đư<PERSON> tính trước
/// và các giá trị cấu hình cố định được sử dụng trong toàn bộ package.
library;

import 'dart:math' as math;

/// Hằng số toán học cho DCT
class DctConstants {
  /// Hệ số C(0) = 1/√2 ≈ 0.7071067811865476
  static const double c0 = 0.7071067811865476;
  
  /// Hệ số C(u) với u ≠ 0 = 1.0
  static const double c1 = 1.0;
  
  /// <PERSON><PERSON><PERSON> thước khối DCT chuẩn
  static const int blockSize = 8;
  
  /// Số lượng channels tối đa (RGB + Alpha)
  static const int maxChannels = 4;
  
  /// Giá trị pixel tối thiểu
  static const int minPixelValue = 0;
  
  /// G<PERSON><PERSON> trị pixel tối đa
  static const int maxPixelValue = 255;
  
  /// Offset để chuyển pixel từ [0,255] sang [-128,127] cho DCT
  static const int pixelOffset = 128;
}

/// Hằng số cho compression
class CompressionConstants {
  /// Quality mặc định
  static const int defaultQuality = 85;
  
  /// Quality tối thiểu
  static const int minQuality = 1;
  
  /// Quality tối đa
  static const int maxQuality = 100;
  
  /// Compression ratio tối thiểu
  static const double minCompressionRatio = 0.01;
  
  /// Compression ratio tối đa
  static const double maxCompressionRatio = 0.99;
  
  /// Kích thước bộ nhớ tối đa mặc định (50MB)
  static const int defaultMaxMemorySize = 50 * 1024 * 1024;
  
  /// Số lượng threads tối đa cho batch processing
  static const int maxConcurrentTasks = 4;
  
  /// Timeout mặc định cho compression task (30 giây)
  static const int defaultTimeoutSeconds = 30;
}

/// Hằng số cho file formats
class FormatConstants {
  /// JPEG magic bytes
  static const List<int> jpegMagicBytes = [0xFF, 0xD8, 0xFF];
  
  /// PNG magic bytes
  static const List<int> pngMagicBytes = [0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A];
  
  /// BMP magic bytes
  static const List<int> bmpMagicBytes = [0x42, 0x4D];
  
  /// TIFF magic bytes (little endian)
  static const List<int> tiffMagicBytesLE = [0x49, 0x49, 0x2A, 0x00];
  
  /// TIFF magic bytes (big endian)
  static const List<int> tiffMagicBytesBE = [0x4D, 0x4D, 0x00, 0x2A];
  
  /// WebP magic bytes
  static const List<int> webpMagicBytes = [0x52, 0x49, 0x46, 0x46];
  
  /// WebP format identifier
  static const List<int> webpFormatBytes = [0x57, 0x45, 0x42, 0x50];
  
  /// Supported input extensions
  static const Set<String> supportedInputExtensions = {
    '.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif', '.webp'
  };
  
  /// Supported output extensions
  static const Set<String> supportedOutputExtensions = {
    '.jpg', '.jpeg', '.png'
  };
}

/// Bảng tra cứu cosine được tính trước cho DCT
/// Tối ưu hiệu năng bằng cách tránh tính toán cos() trong runtime
class CosineLookupTable {
  static late final List<List<double>> _cosineTable;
  static bool _initialized = false;
  
  /// Khởi tạo bảng tra cứu cosine
  static void initialize() {
    if (_initialized) return;
    
    _cosineTable = List.generate(8, (u) => List.generate(8, (x) {
      return math.cos((2 * x + 1) * u * math.pi / 16);
    }));
    
    _initialized = true;
  }
  
  /// Lấy giá trị cosine từ bảng tra cứu
  /// [u]: Frequency index (0-7)
  /// [x]: Spatial index (0-7)
  static double getCosine(int u, int x) {
    assert(_initialized, 'Cosine lookup table chưa được khởi tạo');
    assert(u >= 0 && u < 8, 'u phải trong khoảng 0-7');
    assert(x >= 0 && x < 8, 'x phải trong khoảng 0-7');
    
    return _cosineTable[u][x];
  }
  
  /// Lấy toàn bộ bảng tra cứu (chỉ dùng cho testing)
  static List<List<double>> get table {
    assert(_initialized, 'Cosine lookup table chưa được khởi tạo');
    return _cosineTable;
  }
}

/// Hằng số cho error handling
class ErrorConstants {
  /// Error codes
  static const String invalidInput = 'INVALID_INPUT';
  static const String unsupportedFormat = 'UNSUPPORTED_FORMAT';
  static const String compressionFailed = 'COMPRESSION_FAILED';
  static const String memoryLimitExceeded = 'MEMORY_LIMIT_EXCEEDED';
  static const String taskCancelled = 'TASK_CANCELLED';
  static const String taskTimeout = 'TASK_TIMEOUT';
  static const String isolateError = 'ISOLATE_ERROR';
  
  /// Error messages
  static const Map<String, String> errorMessages = {
    invalidInput: 'Dữ liệu đầu vào không hợp lệ',
    unsupportedFormat: 'Định dạng file không được hỗ trợ',
    compressionFailed: 'Quá trình nén thất bại',
    memoryLimitExceeded: 'Vượt quá giới hạn bộ nhớ',
    taskCancelled: 'Tác vụ đã bị hủy',
    taskTimeout: 'Tác vụ bị timeout',
    isolateError: 'Lỗi trong isolate worker',
  };
}

/// Hằng số cho performance optimization
class PerformanceConstants {
  /// Kích thước chunk tối ưu cho streaming processing
  static const int optimalChunkSize = 64 * 1024; // 64KB
  
  /// Số lượng blocks tối đa xử lý cùng lúc
  static const int maxBlocksPerBatch = 100;
  
  /// Threshold để quyết định có dùng isolate hay không
  static const int isolateThresholdBytes = 1024 * 1024; // 1MB
  
  /// Số lượng retry tối đa cho failed operations
  static const int maxRetryAttempts = 3;
  
  /// Delay giữa các retry attempts (milliseconds)
  static const int retryDelayMs = 100;
}

/// Khởi tạo tất cả lookup tables và constants
void initializeConstants() {
  CosineLookupTable.initialize();
}
