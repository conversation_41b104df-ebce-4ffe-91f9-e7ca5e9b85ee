/// Tiện ích validation cho DCT compression
/// 
/// File này chứa các hàm validation để kiểm tra tính hợp lệ
/// của input data, parameters và configurations.
library;

import 'dart:io';
import 'dart:typed_data';
import '../compression/compression_options.dart';
import 'constants.dart';

/// Exception được throw khi validation thất bại
class ValidationException implements Exception {
  final String message;
  final String? errorCode;
  
  const ValidationException(this.message, [this.errorCode]);
  
  @override
  String toString() => 'ValidationException: $message';
}

/// Lớp tiện ích validation
class ValidationUtils {
  /// Validate compression options
  static bool validateCompressionOptions(CompressionOptions options) {
    // Validate quality
    if (options.quality < CompressionConstants.minQuality || 
        options.quality > CompressionConstants.maxQuality) {
      throw ValidationException(
        'Quality phải trong khoảng ${CompressionConstants.minQuality}-${CompressionConstants.maxQuality}',
        ErrorConstants.invalidInput
      );
    }
    
    // Validate compression ratios
    if (options.minCompressionRatio != null) {
      if (options.minCompressionRatio! < CompressionConstants.minCompressionRatio ||
          options.minCompressionRatio! > CompressionConstants.maxCompressionRatio) {
        throw ValidationException(
          'Min compression ratio phải trong khoảng ${CompressionConstants.minCompressionRatio}-${CompressionConstants.maxCompressionRatio}',
          ErrorConstants.invalidInput
        );
      }
    }
    
    if (options.maxCompressionRatio != null) {
      if (options.maxCompressionRatio! < CompressionConstants.minCompressionRatio ||
          options.maxCompressionRatio! > CompressionConstants.maxCompressionRatio) {
        throw ValidationException(
          'Max compression ratio phải trong khoảng ${CompressionConstants.minCompressionRatio}-${CompressionConstants.maxCompressionRatio}',
          ErrorConstants.invalidInput
        );
      }
    }
    
    // Validate ratio bounds
    if (options.minCompressionRatio != null && options.maxCompressionRatio != null) {
      if (options.minCompressionRatio! > options.maxCompressionRatio!) {
        throw ValidationException(
          'Min compression ratio không thể lớn hơn max compression ratio',
          ErrorConstants.invalidInput
        );
      }
    }
    
    // Validate custom quantization tables
    if (options.customLuminanceQuantizationTable != null) {
      if (!validateQuantizationTable(options.customLuminanceQuantizationTable!)) {
        throw ValidationException(
          'Custom luminance quantization table không hợp lệ',
          ErrorConstants.invalidInput
        );
      }
    }
    
    if (options.customChrominanceQuantizationTable != null) {
      if (!validateQuantizationTable(options.customChrominanceQuantizationTable!)) {
        throw ValidationException(
          'Custom chrominance quantization table không hợp lệ',
          ErrorConstants.invalidInput
        );
      }
    }
    
    // Validate memory limit
    if (options.maxImageSizeInMemory <= 0) {
      throw ValidationException(
        'Max image size in memory phải lớn hơn 0',
        ErrorConstants.invalidInput
      );
    }
    
    return true;
  }
  
  /// Validate quantization table
  static bool validateQuantizationTable(List<List<int>> table) {
    // Kiểm tra kích thước
    if (table.length != DctConstants.blockSize) return false;
    
    for (final row in table) {
      if (row.length != DctConstants.blockSize) return false;
      
      // Kiểm tra giá trị trong khoảng hợp lệ
      for (final value in row) {
        if (value <= 0 || value > 255) return false;
      }
    }
    
    return true;
  }
  
  /// Validate DCT block
  static bool validateDctBlock(List<List<double>> block) {
    if (block.length != DctConstants.blockSize) return false;
    
    for (final row in block) {
      if (row.length != DctConstants.blockSize) return false;
    }
    
    return true;
  }
  
  /// Validate pixel block
  static bool validatePixelBlock(List<List<int>> block) {
    if (block.length != DctConstants.blockSize) return false;
    
    for (final row in block) {
      if (row.length != DctConstants.blockSize) return false;
      
      for (final pixel in row) {
        if (pixel < DctConstants.minPixelValue || 
            pixel > DctConstants.maxPixelValue) return false;
      }
    }
    
    return true;
  }
  
  /// Validate image dimensions
  static bool validateImageDimensions(int width, int height) {
    if (width <= 0 || height <= 0) return false;
    if (width > 65535 || height > 65535) return false; // Reasonable limits
    return true;
  }
  
  /// Validate file path
  static bool validateFilePath(String path) {
    if (path.isEmpty) return false;
    
    try {
      final file = File(path);
      final extension = path.toLowerCase().split('.').last;
      
      // Kiểm tra extension
      if (!FormatConstants.supportedInputExtensions.contains('.$extension')) {
        return false;
      }
      
      return true;
    } catch (e) {
      return false;
    }
  }
  
  /// Validate file exists và readable
  static Future<bool> validateFileExists(String path) async {
    try {
      final file = File(path);
      return await file.exists();
    } catch (e) {
      return false;
    }
  }
  
  /// Validate byte data
  static bool validateByteData(Uint8List data) {
    if (data.isEmpty) return false;
    
    // Kiểm tra magic bytes để xác định format
    return _detectImageFormat(data) != null;
  }
  
  /// Detect image format từ magic bytes
  static String? _detectImageFormat(Uint8List data) {
    if (data.length < 8) return null;
    
    // JPEG
    if (data.length >= 3 && 
        data[0] == FormatConstants.jpegMagicBytes[0] &&
        data[1] == FormatConstants.jpegMagicBytes[1] &&
        data[2] == FormatConstants.jpegMagicBytes[2]) {
      return 'JPEG';
    }
    
    // PNG
    if (data.length >= 8) {
      bool isPng = true;
      for (int i = 0; i < FormatConstants.pngMagicBytes.length; i++) {
        if (data[i] != FormatConstants.pngMagicBytes[i]) {
          isPng = false;
          break;
        }
      }
      if (isPng) return 'PNG';
    }
    
    // BMP
    if (data.length >= 2 &&
        data[0] == FormatConstants.bmpMagicBytes[0] &&
        data[1] == FormatConstants.bmpMagicBytes[1]) {
      return 'BMP';
    }
    
    // TIFF (little endian)
    if (data.length >= 4) {
      bool isTiffLE = true;
      for (int i = 0; i < FormatConstants.tiffMagicBytesLE.length; i++) {
        if (data[i] != FormatConstants.tiffMagicBytesLE[i]) {
          isTiffLE = false;
          break;
        }
      }
      if (isTiffLE) return 'TIFF';
    }
    
    // TIFF (big endian)
    if (data.length >= 4) {
      bool isTiffBE = true;
      for (int i = 0; i < FormatConstants.tiffMagicBytesBE.length; i++) {
        if (data[i] != FormatConstants.tiffMagicBytesBE[i]) {
          isTiffBE = false;
          break;
        }
      }
      if (isTiffBE) return 'TIFF';
    }
    
    // WebP
    if (data.length >= 12 &&
        data[0] == FormatConstants.webpMagicBytes[0] &&
        data[1] == FormatConstants.webpMagicBytes[1] &&
        data[2] == FormatConstants.webpMagicBytes[2] &&
        data[3] == FormatConstants.webpMagicBytes[3] &&
        data[8] == FormatConstants.webpFormatBytes[0] &&
        data[9] == FormatConstants.webpFormatBytes[1] &&
        data[10] == FormatConstants.webpFormatBytes[2] &&
        data[11] == FormatConstants.webpFormatBytes[3]) {
      return 'WebP';
    }
    
    return null;
  }
  
  /// Validate memory usage
  static bool validateMemoryUsage(int imageWidth, int imageHeight, int channels, int maxMemory) {
    final estimatedMemory = imageWidth * imageHeight * channels * 4; // 4 bytes per pixel (worst case)
    return estimatedMemory <= maxMemory;
  }
  
  /// Validate batch input
  static bool validateBatchInput(List<dynamic> inputs) {
    if (inputs.isEmpty) return false;
    
    for (final input in inputs) {
      if (!validateInput(input)) return false;
    }
    
    return true;
  }
  
  /// Validate single input (File, String, or Uint8List)
  static bool validateInput(dynamic input) {
    if (input is File) {
      return validateFilePath(input.path);
    } else if (input is String) {
      return validateFilePath(input);
    } else if (input is Uint8List) {
      return validateByteData(input);
    }
    
    return false;
  }
  
  /// Validate task ID
  static bool validateTaskId(String taskId) {
    return taskId.isNotEmpty && taskId.length <= 100;
  }
  
  /// Validate progress callback parameters
  static bool validateProgressParams(int completed, int total) {
    return completed >= 0 && total > 0 && completed <= total;
  }
}
