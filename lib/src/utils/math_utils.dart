/// Ti<PERSON><PERSON> ích to<PERSON> học cho DCT compression
/// 
/// File này chứa các hàm toán học tối ưu được sử dụng trong
/// quá trình DCT transform và compression.
library;

import 'dart:math' as math;
import 'dart:typed_data';

/// Lớp tiện ích toán học cho DCT operations
class MathUtils {
  /// Clamp giá trị trong khoảng [min, max]
  static T clamp<T extends num>(T value, T min, T max) {
    if (value < min) return min;
    if (value > max) return max;
    return value;
  }
  
  /// Làm tròn double về int gần nhất
  static int roundToInt(double value) {
    return value.round();
  }
  
  /// Làm tròn double về int với clamping [0, 255]
  static int roundToPixel(double value) {
    return clamp(value.round(), 0, 255);
  }
  
  /// Tính mean của một list số
  static double mean(List<num> values) {
    if (values.isEmpty) return 0.0;
    return values.reduce((a, b) => a + b) / values.length;
  }
  
  /// Tính standard deviation của một list số
  static double standardDeviation(List<num> values) {
    if (values.length < 2) return 0.0;
    
    final meanValue = mean(values);
    final variance = values
        .map((x) => math.pow(x - meanValue, 2))
        .reduce((a, b) => a + b) / (values.length - 1);
    
    return math.sqrt(variance);
  }
  
  /// Tính entropy của một khối pixel (đo độ phức tạp)
  static double calculateEntropy(List<List<int>> block) {
    final histogram = <int, int>{};
    final totalPixels = block.length * block[0].length;
    
    // Tạo histogram
    for (final row in block) {
      for (final pixel in row) {
        histogram[pixel] = (histogram[pixel] ?? 0) + 1;
      }
    }
    
    // Tính entropy
    double entropy = 0.0;
    for (final count in histogram.values) {
      if (count > 0) {
        final probability = count / totalPixels;
        entropy -= probability * math.log(probability) / math.ln2;
      }
    }
    
    return entropy;
  }
  
  /// Tính PSNR (Peak Signal-to-Noise Ratio) giữa hai ảnh
  static double calculatePSNR(List<List<List<int>>> original, 
                              List<List<List<int>>> compressed) {
    assert(original.length == compressed.length, 'Kích thước ảnh phải giống nhau');
    assert(original[0].length == compressed[0].length, 'Kích thước ảnh phải giống nhau');
    assert(original[0][0].length == compressed[0][0].length, 'Số channels phải giống nhau');
    
    double mse = 0.0;
    int totalPixels = 0;
    
    for (int y = 0; y < original.length; y++) {
      for (int x = 0; x < original[0].length; x++) {
        for (int c = 0; c < original[0][0].length; c++) {
          final diff = original[y][x][c] - compressed[y][x][c];
          mse += diff * diff;
          totalPixels++;
        }
      }
    }
    
    mse /= totalPixels;
    
    if (mse == 0) return double.infinity; // Perfect match
    
    return 20 * math.log(255) / math.ln10 - 10 * math.log(mse) / math.ln10;
  }
  
  /// Tính SSIM (Structural Similarity Index) cho một channel
  static double calculateSSIM(List<List<int>> original, List<List<int>> compressed) {
    const double k1 = 0.01;
    const double k2 = 0.03;
    const double l = 255.0; // Dynamic range
    const double c1 = (k1 * l) * (k1 * l);
    const double c2 = (k2 * l) * (k2 * l);
    
    // Tính mean
    final mu1 = _calculateMean2D(original);
    final mu2 = _calculateMean2D(compressed);
    
    // Tính variance và covariance
    final sigma1Sq = _calculateVariance2D(original, mu1);
    final sigma2Sq = _calculateVariance2D(compressed, mu2);
    final sigma12 = _calculateCovariance2D(original, compressed, mu1, mu2);
    
    // Tính SSIM
    final numerator = (2 * mu1 * mu2 + c1) * (2 * sigma12 + c2);
    final denominator = (mu1 * mu1 + mu2 * mu2 + c1) * (sigma1Sq + sigma2Sq + c2);
    
    return numerator / denominator;
  }
  
  /// Tính mean của ma trận 2D
  static double _calculateMean2D(List<List<int>> matrix) {
    double sum = 0.0;
    int count = 0;
    
    for (final row in matrix) {
      for (final value in row) {
        sum += value;
        count++;
      }
    }
    
    return sum / count;
  }
  
  /// Tính variance của ma trận 2D
  static double _calculateVariance2D(List<List<int>> matrix, double mean) {
    double sum = 0.0;
    int count = 0;
    
    for (final row in matrix) {
      for (final value in row) {
        final diff = value - mean;
        sum += diff * diff;
        count++;
      }
    }
    
    return sum / (count - 1);
  }
  
  /// Tính covariance giữa hai ma trận 2D
  static double _calculateCovariance2D(List<List<int>> matrix1, 
                                      List<List<int>> matrix2,
                                      double mean1, double mean2) {
    double sum = 0.0;
    int count = 0;
    
    for (int i = 0; i < matrix1.length; i++) {
      for (int j = 0; j < matrix1[0].length; j++) {
        sum += (matrix1[i][j] - mean1) * (matrix2[i][j] - mean2);
        count++;
      }
    }
    
    return sum / (count - 1);
  }
  
  /// Tính compression ratio từ kích thước
  static double calculateCompressionRatio(int originalSize, int compressedSize) {
    if (originalSize == 0) return 0.0;
    return compressedSize / originalSize;
  }
  
  /// Tính space savings percentage
  static double calculateSpaceSavings(int originalSize, int compressedSize) {
    if (originalSize == 0) return 0.0;
    return (1.0 - compressedSize / originalSize) * 100.0;
  }
  
  /// Interpolate giữa hai giá trị
  static double lerp(double a, double b, double t) {
    return a + (b - a) * clamp(t, 0.0, 1.0);
  }
  
  /// Tính next power of 2
  static int nextPowerOfTwo(int value) {
    if (value <= 0) return 1;
    
    int power = 1;
    while (power < value) {
      power <<= 1;
    }
    return power;
  }
  
  /// Kiểm tra xem số có phải power of 2 không
  static bool isPowerOfTwo(int value) {
    return value > 0 && (value & (value - 1)) == 0;
  }
  
  /// Tính log base 2
  static double log2(double value) {
    return math.log(value) / math.ln2;
  }
  
  /// Chuyển đổi bytes thành human readable string
  static String formatBytes(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }
  
  /// Tạo Uint8List từ List<int> với validation
  static Uint8List createUint8List(List<int> data) {
    final result = Uint8List(data.length);
    for (int i = 0; i < data.length; i++) {
      result[i] = clamp(data[i], 0, 255);
    }
    return result;
  }
}
