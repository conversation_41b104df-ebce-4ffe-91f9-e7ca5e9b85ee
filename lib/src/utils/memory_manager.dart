/// <PERSON><PERSON><PERSON><PERSON> lý bộ nhớ cho DCT compression
/// 
/// File này chứa các tiện ích để quản lý bộ nhớ hiệu quả,
/// tránh memory overflow khi xử lý ảnh lớn.
library;

import 'dart:typed_data';
import 'dart:math' as math;
import 'constants.dart';
import 'math_utils.dart';

/// Thông tin về memory usage
class MemoryInfo {
  final int totalAllocated;
  final int currentUsage;
  final int peakUsage;
  final int availableMemory;
  
  const MemoryInfo({
    required this.totalAllocated,
    required this.currentUsage,
    required this.peakUsage,
    required this.availableMemory,
  });
  
  /// Memory usage percentage
  double get usagePercentage => totalAllocated > 0 ? currentUsage / totalAllocated : 0.0;
  
  /// Có đủ memory cho allocation mới không
  bool canAllocate(int bytes) => currentUsage + bytes <= totalAllocated;
  
  @override
  String toString() {
    return 'MemoryInfo(current: ${MathUtils.formatBytes(currentUsage)}, '
           'peak: ${MathUtils.formatBytes(peakUsage)}, '
           'total: ${MathUtils.formatBytes(totalAllocated)}, '
           'usage: ${(usagePercentage * 100).toStringAsFixed(1)}%)';
  }
}

/// Memory pool để tái sử dụng buffers
class MemoryPool {
  final Map<int, List<Uint8List>> _pools = {};
  final int _maxPoolSize;
  int _totalAllocated = 0;
  
  MemoryPool({int maxPoolSize = 10}) : _maxPoolSize = maxPoolSize;
  
  /// Lấy buffer từ pool hoặc tạo mới
  Uint8List getBuffer(int size) {
    final poolKey = _getNearestPoolSize(size);
    final pool = _pools[poolKey];
    
    if (pool != null && pool.isNotEmpty) {
      final buffer = pool.removeLast();
      if (buffer.length >= size) {
        return Uint8List.view(buffer.buffer, 0, size);
      }
    }
    
    // Tạo buffer mới
    _totalAllocated += size;
    return Uint8List(size);
  }
  
  /// Trả buffer về pool
  void returnBuffer(Uint8List buffer) {
    final poolKey = _getNearestPoolSize(buffer.length);
    final pool = _pools.putIfAbsent(poolKey, () => <Uint8List>[]);
    
    if (pool.length < _maxPoolSize) {
      pool.add(buffer);
    } else {
      _totalAllocated -= buffer.length;
    }
  }
  
  /// Lấy pool size gần nhất (power of 2)
  int _getNearestPoolSize(int size) {
    return MathUtils.nextPowerOfTwo(size);
  }
  
  /// Clear tất cả pools
  void clear() {
    _pools.clear();
    _totalAllocated = 0;
  }
  
  /// Thống kê memory pool
  Map<String, dynamic> getStats() {
    final stats = <String, dynamic>{
      'totalPools': _pools.length,
      'totalAllocated': _totalAllocated,
      'totalBuffers': _pools.values.fold(0, (sum, pool) => sum + pool.length),
    };
    
    for (final entry in _pools.entries) {
      stats['pool_${entry.key}'] = entry.value.length;
    }
    
    return stats;
  }
}

/// Quản lý bộ nhớ chính
class MemoryManager {
  static final MemoryManager _instance = MemoryManager._internal();
  factory MemoryManager() => _instance;
  MemoryManager._internal();
  
  final MemoryPool _memoryPool = MemoryPool();
  int _maxMemoryLimit = CompressionConstants.defaultMaxMemorySize;
  int _currentUsage = 0;
  int _peakUsage = 0;
  final List<int> _allocationHistory = [];
  
  /// Set memory limit
  void setMemoryLimit(int bytes) {
    _maxMemoryLimit = bytes;
  }
  
  /// Lấy thông tin memory hiện tại
  MemoryInfo getMemoryInfo() {
    return MemoryInfo(
      totalAllocated: _maxMemoryLimit,
      currentUsage: _currentUsage,
      peakUsage: _peakUsage,
      availableMemory: _maxMemoryLimit - _currentUsage,
    );
  }
  
  /// Allocate memory với tracking
  Uint8List allocate(int size) {
    if (_currentUsage + size > _maxMemoryLimit) {
      throw OutOfMemoryError('Vượt quá giới hạn bộ nhớ: ${MathUtils.formatBytes(_maxMemoryLimit)}');
    }
    
    _currentUsage += size;
    _peakUsage = math.max(_peakUsage, _currentUsage);
    _allocationHistory.add(size);
    
    return _memoryPool.getBuffer(size);
  }
  
  /// Deallocate memory
  void deallocate(Uint8List buffer) {
    _currentUsage -= buffer.length;
    _memoryPool.returnBuffer(buffer);
  }
  
  /// Ước tính memory cần thiết cho image
  int estimateImageMemory(int width, int height, int channels) {
    // Ước tính memory cho:
    // - Original pixel data
    // - DCT coefficients (double precision)
    // - Quantized coefficients
    // - Temporary buffers
    
    final pixelMemory = width * height * channels; // 1 byte per pixel
    final dctMemory = width * height * channels * 8; // 8 bytes per double
    final tempMemory = pixelMemory * 2; // Temporary buffers
    
    return pixelMemory + dctMemory + tempMemory;
  }
  
  /// Kiểm tra có đủ memory cho operation không
  bool canAllocateForImage(int width, int height, int channels) {
    final requiredMemory = estimateImageMemory(width, height, channels);
    return _currentUsage + requiredMemory <= _maxMemoryLimit;
  }
  
  /// Tính toán optimal chunk size cho streaming
  int calculateOptimalChunkSize(int imageWidth, int imageHeight, int channels) {
    final availableMemory = _maxMemoryLimit - _currentUsage;
    final pixelsPerChunk = availableMemory ~/ (channels * 8); // 8 bytes per pixel (worst case)
    
    // Đảm bảo chunk size là bội số của block size
    final blocksPerChunk = math.max(1, pixelsPerChunk ~/ (DctConstants.blockSize * DctConstants.blockSize));
    final optimalChunkSize = blocksPerChunk * DctConstants.blockSize * DctConstants.blockSize;
    
    return math.min(optimalChunkSize, imageWidth * imageHeight);
  }
  
  /// Force garbage collection và cleanup
  void forceCleanup() {
    _memoryPool.clear();
    _currentUsage = 0;
    _allocationHistory.clear();
    
    // Trigger garbage collection hint
    // Note: Dart không có explicit GC, nhưng clearing references giúp GC
  }
  
  /// Lấy memory statistics
  Map<String, dynamic> getStatistics() {
    final info = getMemoryInfo();
    final poolStats = _memoryPool.getStats();
    
    return {
      'memoryInfo': {
        'current': info.currentUsage,
        'peak': info.peakUsage,
        'total': info.totalAllocated,
        'available': info.availableMemory,
        'usagePercentage': info.usagePercentage,
      },
      'poolStats': poolStats,
      'allocationHistory': List.from(_allocationHistory),
      'averageAllocation': _allocationHistory.isNotEmpty 
          ? _allocationHistory.reduce((a, b) => a + b) / _allocationHistory.length 
          : 0,
    };
  }
  
  /// Reset tất cả statistics
  void resetStatistics() {
    _peakUsage = _currentUsage;
    _allocationHistory.clear();
  }
  
  /// Kiểm tra memory health
  bool isMemoryHealthy() {
    final info = getMemoryInfo();
    return info.usagePercentage < 0.9; // Dưới 90% là healthy
  }
  
  /// Tự động cleanup khi memory usage cao
  void autoCleanupIfNeeded() {
    final info = getMemoryInfo();
    if (info.usagePercentage > 0.8) { // Trên 80% thì cleanup
      _memoryPool.clear();
      // Có thể thêm các cleanup strategies khác
    }
  }
}

/// Exception khi hết memory
class OutOfMemoryError implements Exception {
  final String message;
  
  const OutOfMemoryError(this.message);
  
  @override
  String toString() => 'OutOfMemoryError: $message';
}

/// Utility functions cho memory management
class MemoryUtils {
  /// Tính memory footprint của pixel data
  static int calculatePixelDataSize(int width, int height, int channels) {
    return width * height * channels;
  }
  
  /// Tính memory footprint của DCT coefficients
  static int calculateDctDataSize(int width, int height, int channels) {
    return width * height * channels * 8; // 8 bytes per double
  }
  
  /// Ước tính compression memory overhead
  static int estimateCompressionOverhead(int imageSize) {
    // Ước tính 50% overhead cho temporary buffers, lookup tables, etc.
    return (imageSize * 0.5).round();
  }
  
  /// Kiểm tra có nên sử dụng streaming processing không
  static bool shouldUseStreaming(int imageWidth, int imageHeight, int channels, int memoryLimit) {
    final estimatedMemory = MemoryManager().estimateImageMemory(imageWidth, imageHeight, channels);
    return estimatedMemory > memoryLimit * 0.7; // Nếu dùng >70% memory thì streaming
  }
}
