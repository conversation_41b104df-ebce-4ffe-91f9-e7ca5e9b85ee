/// Progress tracking system cho isolate-based compression
/// 
/// File này implement hệ thống theo dõi tiến trình cho các tác vụ
/// compression chạy trong background isolates.
library;

import 'dart:async';
import 'dart:math' as math;

/// Callback cho progress updates
typedef ProgressCallback = void Function(ProgressInfo progress);

/// Thông tin tiến trình
class ProgressInfo {
  /// ID của tác vụ
  final String taskId;
  
  /// Số lượng đã hoàn thành
  final int completed;
  
  /// Tổng số lượng
  final int total;
  
  /// Item hiện tại đang xử lý
  final String? currentItem;
  
  /// Thời gian bắt đầu
  final DateTime startTime;
  
  /// Thời gian cập nhật cuối
  final DateTime lastUpdate;
  
  /// Tốc độ xử lý (items/second)
  final double processingRate;
  
  /// Thời gian ước tính còn lại
  final Duration? estimatedTimeRemaining;
  
  /// Trạng thái
  final ProgressStatus status;
  
  /// Thông tin bổ sung
  final Map<String, dynamic>? metadata;
  
  const ProgressInfo({
    required this.taskId,
    required this.completed,
    required this.total,
    this.currentItem,
    required this.startTime,
    required this.lastUpdate,
    required this.processingRate,
    this.estimatedTimeRemaining,
    required this.status,
    this.metadata,
  });
  
  /// Tỷ lệ hoàn thành (0.0 - 1.0)
  double get completionRatio => total > 0 ? completed / total : 0.0;
  
  /// Phần trăm hoàn thành (0 - 100)
  double get completionPercentage => completionRatio * 100;
  
  /// Có hoàn thành không
  bool get isCompleted => completed >= total;
  
  /// Thời gian đã trôi qua
  Duration get elapsedTime => lastUpdate.difference(startTime);
  
  /// Copy với các thay đổi
  ProgressInfo copyWith({
    String? taskId,
    int? completed,
    int? total,
    String? currentItem,
    DateTime? startTime,
    DateTime? lastUpdate,
    double? processingRate,
    Duration? estimatedTimeRemaining,
    ProgressStatus? status,
    Map<String, dynamic>? metadata,
  }) {
    return ProgressInfo(
      taskId: taskId ?? this.taskId,
      completed: completed ?? this.completed,
      total: total ?? this.total,
      currentItem: currentItem ?? this.currentItem,
      startTime: startTime ?? this.startTime,
      lastUpdate: lastUpdate ?? this.lastUpdate,
      processingRate: processingRate ?? this.processingRate,
      estimatedTimeRemaining: estimatedTimeRemaining ?? this.estimatedTimeRemaining,
      status: status ?? this.status,
      metadata: metadata ?? this.metadata,
    );
  }
  
  @override
  String toString() {
    return 'ProgressInfo(taskId: $taskId, completed: $completed/$total, '
           'percentage: ${completionPercentage.toStringAsFixed(1)}%, '
           'rate: ${processingRate.toStringAsFixed(2)} items/s, '
           'status: $status)';
  }
}

/// Trạng thái tiến trình
enum ProgressStatus {
  /// Chưa bắt đầu
  notStarted,
  
  /// Đang chạy
  running,
  
  /// Tạm dừng
  paused,
  
  /// Hoàn thành
  completed,
  
  /// Bị hủy
  cancelled,
  
  /// Lỗi
  error,
}

/// Progress tracker cho một tác vụ
class TaskProgressTracker {
  final String taskId;
  final int totalItems;
  final DateTime startTime;
  
  int _completed = 0;
  String? _currentItem;
  ProgressStatus _status = ProgressStatus.notStarted;
  DateTime _lastUpdate;
  final List<DateTime> _completionTimes = [];
  Map<String, dynamic>? _metadata;
  
  TaskProgressTracker({
    required this.taskId,
    required this.totalItems,
    DateTime? startTime,
  }) : startTime = startTime ?? DateTime.now(),
       _lastUpdate = startTime ?? DateTime.now();
  
  /// Cập nhật tiến trình
  void updateProgress({
    int? completed,
    String? currentItem,
    ProgressStatus? status,
    Map<String, dynamic>? metadata,
  }) {
    final now = DateTime.now();
    
    if (completed != null && completed > _completed) {
      // Record completion times for rate calculation
      for (int i = _completed; i < completed; i++) {
        _completionTimes.add(now);
      }
      _completed = completed;
    }
    
    if (currentItem != null) {
      _currentItem = currentItem;
    }
    
    if (status != null) {
      _status = status;
    }
    
    if (metadata != null) {
      _metadata = {...?_metadata, ...metadata};
    }
    
    _lastUpdate = now;
  }
  
  /// Lấy thông tin tiến trình hiện tại
  ProgressInfo getCurrentProgress() {
    final processingRate = _calculateProcessingRate();
    final estimatedTimeRemaining = _calculateEstimatedTimeRemaining(processingRate);
    
    return ProgressInfo(
      taskId: taskId,
      completed: _completed,
      total: totalItems,
      currentItem: _currentItem,
      startTime: startTime,
      lastUpdate: _lastUpdate,
      processingRate: processingRate,
      estimatedTimeRemaining: estimatedTimeRemaining,
      status: _status,
      metadata: _metadata,
    );
  }
  
  /// Tính tốc độ xử lý
  double _calculateProcessingRate() {
    if (_completionTimes.isEmpty) return 0.0;
    
    final now = DateTime.now();
    final recentTimes = _completionTimes.where(
      (time) => now.difference(time).inSeconds <= 30, // Last 30 seconds
    ).toList();
    
    if (recentTimes.isEmpty) return 0.0;
    
    final timeSpan = now.difference(recentTimes.first).inMilliseconds / 1000.0;
    return timeSpan > 0 ? recentTimes.length / timeSpan : 0.0;
  }
  
  /// Tính thời gian ước tính còn lại
  Duration? _calculateEstimatedTimeRemaining(double processingRate) {
    if (processingRate <= 0 || _completed >= totalItems) return null;
    
    final remaining = totalItems - _completed;
    final secondsRemaining = remaining / processingRate;
    
    return Duration(seconds: secondsRemaining.round());
  }
  
  /// Đánh dấu hoàn thành
  void markCompleted() {
    _status = ProgressStatus.completed;
    _completed = totalItems;
    _lastUpdate = DateTime.now();
  }
  
  /// Đánh dấu bị hủy
  void markCancelled() {
    _status = ProgressStatus.cancelled;
    _lastUpdate = DateTime.now();
  }
  
  /// Đánh dấu lỗi
  void markError() {
    _status = ProgressStatus.error;
    _lastUpdate = DateTime.now();
  }
}

/// Progress tracker manager
class ProgressTracker {
  final Map<String, TaskProgressTracker> _trackers = {};
  final Map<String, ProgressCallback> _callbacks = {};
  final StreamController<ProgressInfo> _progressController = StreamController.broadcast();
  
  /// Stream của tất cả progress updates
  Stream<ProgressInfo> get progressStream => _progressController.stream;
  
  /// Tạo tracker cho tác vụ mới
  TaskProgressTracker createTracker({
    required String taskId,
    required int totalItems,
    ProgressCallback? callback,
  }) {
    final tracker = TaskProgressTracker(
      taskId: taskId,
      totalItems: totalItems,
    );
    
    _trackers[taskId] = tracker;
    
    if (callback != null) {
      _callbacks[taskId] = callback;
    }
    
    return tracker;
  }
  
  /// Cập nhật tiến trình cho tác vụ
  void updateProgress(
    String taskId, {
    int? completed,
    String? currentItem,
    ProgressStatus? status,
    Map<String, dynamic>? metadata,
  }) {
    final tracker = _trackers[taskId];
    if (tracker == null) return;
    
    tracker.updateProgress(
      completed: completed,
      currentItem: currentItem,
      status: status,
      metadata: metadata,
    );
    
    final progress = tracker.getCurrentProgress();
    
    // Notify callback
    _callbacks[taskId]?.call(progress);
    
    // Notify stream
    _progressController.add(progress);
  }
  
  /// Lấy thông tin tiến trình của tác vụ
  ProgressInfo? getProgress(String taskId) {
    return _trackers[taskId]?.getCurrentProgress();
  }
  
  /// Lấy tất cả tiến trình đang active
  List<ProgressInfo> getAllActiveProgress() {
    return _trackers.values
        .where((tracker) => tracker._status == ProgressStatus.running)
        .map((tracker) => tracker.getCurrentProgress())
        .toList();
  }
  
  /// Đánh dấu tác vụ hoàn thành
  void markCompleted(String taskId) {
    final tracker = _trackers[taskId];
    if (tracker == null) return;
    
    tracker.markCompleted();
    final progress = tracker.getCurrentProgress();
    
    _callbacks[taskId]?.call(progress);
    _progressController.add(progress);
    
    // Cleanup sau một thời gian
    Timer(const Duration(minutes: 5), () => _cleanup(taskId));
  }
  
  /// Đánh dấu tác vụ bị hủy
  void markCancelled(String taskId) {
    final tracker = _trackers[taskId];
    if (tracker == null) return;
    
    tracker.markCancelled();
    final progress = tracker.getCurrentProgress();
    
    _callbacks[taskId]?.call(progress);
    _progressController.add(progress);
    
    Timer(const Duration(minutes: 1), () => _cleanup(taskId));
  }
  
  /// Đánh dấu tác vụ lỗi
  void markError(String taskId) {
    final tracker = _trackers[taskId];
    if (tracker == null) return;
    
    tracker.markError();
    final progress = tracker.getCurrentProgress();
    
    _callbacks[taskId]?.call(progress);
    _progressController.add(progress);
    
    Timer(const Duration(minutes: 1), () => _cleanup(taskId));
  }
  
  /// Cleanup tracker
  void _cleanup(String taskId) {
    _trackers.remove(taskId);
    _callbacks.remove(taskId);
  }
  
  /// Cleanup tất cả
  void dispose() {
    _trackers.clear();
    _callbacks.clear();
    _progressController.close();
  }
}

/// Global progress tracker instance
final ProgressTracker globalProgressTracker = ProgressTracker();
