/// Isolate communication utilities
/// 
/// File này chứa các tiện ích để giao tiếp giữa main isolate và worker isolates,
/// bao gồm message serialization, error handling, và resource management.
library;

import 'dart:async';
import 'dart:collection' show Queue;
import 'dart:isolate';
import 'dart:typed_data';
import 'dart:convert';
import 'dart:math' as math;

/// Loại message giữa isolates
enum IsolateMessageType {
  /// Yêu cầu khởi tạo
  initialize,
  
  /// Xác nhận khởi tạo
  initializeAck,
  
  /// Yêu cầu xử lý
  processRequest,
  
  /// Kết quả xử lý
  processResult,
  
  /// Cập nhật tiến trình
  progressUpdate,
  
  /// Yêu cầu hủy
  cancelRequest,
  
  /// Xác nhận hủy
  cancelAck,
  
  /// Lỗi
  error,
  
  /// Ping để kiểm tra kết nối
  ping,
  
  /// Pong response
  pong,
  
  /// Yêu cầu shutdown
  shutdown,
  
  /// <PERSON>ác nhận shutdown
  shutdownAck,
}

/// Message giữa isolates
class IsolateMessage {
  /// Loại message
  final IsolateMessageType type;
  
  /// ID của message (để tracking)
  final String messageId;
  
  /// ID của tác vụ liên quan
  final String? taskId;
  
  /// Timestamp
  final DateTime timestamp;
  
  /// Dữ liệu payload
  final Map<String, dynamic>? data;
  
  /// Metadata bổ sung
  final Map<String, dynamic>? metadata;
  
  IsolateMessage({
    required this.type,
    String? messageId,
    this.taskId,
    DateTime? timestamp,
    this.data,
    this.metadata,
  }) : messageId = messageId ?? _generateMessageId(),
       timestamp = timestamp ?? DateTime.now();
  
  /// Tạo từ Map
  factory IsolateMessage.fromMap(Map<String, dynamic> map) {
    return IsolateMessage(
      type: IsolateMessageType.values[map['type']],
      messageId: map['messageId'],
      taskId: map['taskId'],
      timestamp: DateTime.fromMillisecondsSinceEpoch(map['timestamp']),
      data: map['data']?.cast<String, dynamic>(),
      metadata: map['metadata']?.cast<String, dynamic>(),
    );
  }
  
  /// Chuyển thành Map
  Map<String, dynamic> toMap() {
    return {
      'type': type.index,
      'messageId': messageId,
      'taskId': taskId,
      'timestamp': timestamp.millisecondsSinceEpoch,
      'data': data,
      'metadata': metadata,
    };
  }
  
  /// Tạo message ID duy nhất
  static String _generateMessageId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = math.Random().nextInt(10000);
    return 'msg_${timestamp}_$random';
  }
  
  @override
  String toString() {
    return 'IsolateMessage(type: $type, messageId: $messageId, taskId: $taskId)';
  }
}

/// Isolate communication channel
class IsolateChannel {
  final SendPort sendPort;
  final ReceivePort receivePort;
  final StreamController<IsolateMessage> _messageController = StreamController.broadcast();
  final Map<String, Completer<IsolateMessage>> _pendingRequests = {};
  
  late final StreamSubscription _subscription;
  bool _isDisposed = false;
  
  IsolateChannel({
    required this.sendPort,
    required this.receivePort,
  }) {
    _subscription = receivePort.listen(_handleMessage);
  }
  
  /// Stream của incoming messages
  Stream<IsolateMessage> get messageStream => _messageController.stream;
  
  /// Gửi message
  void sendMessage(IsolateMessage message) {
    if (_isDisposed) throw StateError('Channel is disposed');
    sendPort.send(message.toMap());
  }
  
  /// Gửi request và chờ response
  Future<IsolateMessage> sendRequest(
    IsolateMessage request, {
    Duration timeout = const Duration(seconds: 30),
  }) async {
    if (_isDisposed) throw StateError('Channel is disposed');
    
    final completer = Completer<IsolateMessage>();
    _pendingRequests[request.messageId] = completer;
    
    // Set timeout
    Timer(timeout, () {
      final pendingCompleter = _pendingRequests.remove(request.messageId);
      if (pendingCompleter != null && !pendingCompleter.isCompleted) {
        pendingCompleter.completeError(
          TimeoutException('Request timeout', timeout),
        );
      }
    });
    
    sendMessage(request);
    return completer.future;
  }
  
  /// Gửi response cho request
  void sendResponse(IsolateMessage originalRequest, IsolateMessage response) {
    final responseMessage = IsolateMessage(
      type: response.type,
      messageId: response.messageId,
      taskId: originalRequest.taskId,
      data: response.data,
      metadata: {
        ...?response.metadata,
        'responseToMessageId': originalRequest.messageId,
      },
    );
    
    sendMessage(responseMessage);
  }
  
  /// Ping để kiểm tra kết nối
  Future<Duration> ping({Duration timeout = const Duration(seconds: 5)}) async {
    final stopwatch = Stopwatch()..start();
    
    final pingMessage = IsolateMessage(type: IsolateMessageType.ping);
    
    try {
      final response = await sendRequest(pingMessage, timeout: timeout);
      stopwatch.stop();
      
      if (response.type == IsolateMessageType.pong) {
        return stopwatch.elapsed;
      } else {
        throw StateError('Unexpected response type: ${response.type}');
      }
    } catch (e) {
      stopwatch.stop();
      rethrow;
    }
  }
  
  /// Xử lý incoming message
  void _handleMessage(dynamic rawMessage) {
    try {
      final messageMap = rawMessage as Map<String, dynamic>;
      final message = IsolateMessage.fromMap(messageMap);
      
      // Kiểm tra nếu là response cho pending request
      final responseToMessageId = message.metadata?['responseToMessageId'];
      if (responseToMessageId != null) {
        final completer = _pendingRequests.remove(responseToMessageId);
        if (completer != null && !completer.isCompleted) {
          completer.complete(message);
          return;
        }
      }
      
      // Xử lý ping/pong
      if (message.type == IsolateMessageType.ping) {
        final pongMessage = IsolateMessage(
          type: IsolateMessageType.pong,
          taskId: message.taskId,
        );
        sendResponse(message, pongMessage);
        return;
      }
      
      // Broadcast message
      _messageController.add(message);
      
    } catch (e) {
      // Log error và continue
      print('Error handling isolate message: $e');
    }
  }
  
  /// Dispose channel
  void dispose() {
    if (_isDisposed) return;
    _isDisposed = true;
    
    _subscription.cancel();
    receivePort.close();
    _messageController.close();
    
    // Complete pending requests với error
    for (final completer in _pendingRequests.values) {
      if (!completer.isCompleted) {
        completer.completeError(StateError('Channel disposed'));
      }
    }
    _pendingRequests.clear();
  }
}

/// Isolate pool manager
class IsolatePool {
  final int maxIsolates;
  final List<IsolateWorker> _workers = [];
  final Queue<WorkItem> _workQueue = Queue<WorkItem>();
  
  bool _isDisposed = false;
  
  IsolatePool({this.maxIsolates = 3});
  
  /// Khởi tạo pool
  Future<void> initialize() async {
    if (_isDisposed) throw StateError('Pool is disposed');
    
    for (int i = 0; i < maxIsolates; i++) {
      final worker = IsolateWorker(id: 'worker_$i');
      await worker.initialize();
      _workers.add(worker);
    }
  }
  
  /// Submit work item
  Future<T> submit<T>(WorkItem<T> workItem) async {
    if (_isDisposed) throw StateError('Pool is disposed');
    
    final completer = Completer<T>();
    workItem._completer = completer;
    
    _workQueue.add(workItem);
    _processQueue();
    
    return completer.future;
  }
  
  /// Xử lý work queue
  void _processQueue() {
    if (_workQueue.isEmpty) return;
    
    // Tìm worker available
    IsolateWorker? availableWorker;
    try {
      availableWorker = _workers.firstWhere((worker) => worker.isAvailable);
    } catch (e) {
      // No available worker
      return;
    }

    final workItem = _workQueue.removeFirst();
    availableWorker.executeWork(workItem);
      
      // Continue processing
      if (_workQueue.isNotEmpty) {
        Timer.run(_processQueue);
      }
    }
  }

/// Work item cho isolate pool
abstract class WorkItem<T> {
  final String id;
  final Map<String, dynamic> data;
  Completer<T>? _completer;
  
  WorkItem({
    required this.id,
    required this.data,
  });
  
  /// Xử lý work trong isolate
  Future<T> process(IsolateChannel channel);
}

/// Isolate worker
class IsolateWorker {
  final String id;
  Isolate? _isolate;
  IsolateChannel? _channel;
  bool _isAvailable = true;
  
  IsolateWorker({required this.id});
  
  bool get isAvailable => _isAvailable && _channel != null;
  
  /// Khởi tạo worker
  Future<void> initialize() async {
    final receivePort = ReceivePort();
    
    _isolate = await Isolate.spawn(
      _isolateEntryPoint,
      receivePort.sendPort,
    );
    
    // Chờ worker gửi SendPort
    final sendPort = await receivePort.first as SendPort;
    
    _channel = IsolateChannel(
      sendPort: sendPort,
      receivePort: ReceivePort(),
    );
    
    // Initialize worker
    await _channel!.sendRequest(
      IsolateMessage(type: IsolateMessageType.initialize),
    );
  }
  
  /// Thực hiện work
  Future<void> executeWork<T>(WorkItem<T> workItem) async {
    if (!isAvailable) throw StateError('Worker is not available');
    
    _isAvailable = false;
    
    try {
      final result = await workItem.process(_channel!);
      workItem._completer?.complete(result);
    } catch (e) {
      workItem._completer?.completeError(e);
    } finally {
      _isAvailable = true;
    }
  }
  
  /// Dispose worker
  Future<void> dispose() async {
    if (_channel != null) {
      try {
        await _channel!.sendRequest(
          IsolateMessage(type: IsolateMessageType.shutdown),
          timeout: const Duration(seconds: 5),
        );
      } catch (e) {
        // Ignore timeout errors
      }
      
      _channel!.dispose();
      _channel = null;
    }
    
    _isolate?.kill();
    _isolate = null;
  }
  
  /// Entry point cho worker isolate
  static void _isolateEntryPoint(SendPort mainSendPort) {
    final receivePort = ReceivePort();
    mainSendPort.send(receivePort.sendPort);
    
    final channel = IsolateChannel(
      sendPort: mainSendPort,
      receivePort: receivePort,
    );
    
    // Listen for messages
    channel.messageStream.listen((message) {
      switch (message.type) {
        case IsolateMessageType.initialize:
          channel.sendResponse(
            message,
            IsolateMessage(type: IsolateMessageType.initializeAck),
          );
          break;
          
        case IsolateMessageType.shutdown:
          channel.sendResponse(
            message,
            IsolateMessage(type: IsolateMessageType.shutdownAck),
          );
          channel.dispose();
          break;
          
        default:
          // Handle other message types
          break;
      }
    });
  }
}

/// Statistics cho isolate pool
class IsolatePoolStats {
  final int totalWorkers;
  final int availableWorkers;
  final int busyWorkers;
  final int queuedItems;
  
  const IsolatePoolStats({
    required this.totalWorkers,
    required this.availableWorkers,
    required this.busyWorkers,
    required this.queuedItems,
  });
  
  double get utilizationRate {
    return totalWorkers > 0 ? busyWorkers / totalWorkers : 0.0;
  }
  
  @override
  String toString() {
    return 'IsolatePoolStats(total: $totalWorkers, available: $availableWorkers, '
           'busy: $busyWorkers, queued: $queuedItems, '
           'utilization: ${(utilizationRate * 100).toStringAsFixed(1)}%)';
  }
}

/// Utility functions
class IsolateUtils {
  /// Serialize Uint8List cho isolate communication
  static List<int> serializeUint8List(Uint8List data) {
    return data.toList();
  }
  
  /// Deserialize Uint8List từ isolate communication
  static Uint8List deserializeUint8List(List<int> data) {
    return Uint8List.fromList(data);
  }
  
  /// Serialize complex object
  static String serializeObject(dynamic object) {
    return jsonEncode(object);
  }
  
  /// Deserialize complex object
  static dynamic deserializeObject(String json) {
    return jsonDecode(json);
  }
  
  /// Kiểm tra isolate có còn sống không
  static Future<bool> isIsolateAlive(IsolateChannel channel) async {
    try {
      await channel.ping(timeout: const Duration(seconds: 2));
      return true;
    } catch (e) {
      return false;
    }
  }
  
  /// Estimate memory usage của data
  static int estimateMemoryUsage(dynamic data) {
    if (data is Uint8List) {
      return data.length;
    } else if (data is List) {
      return data.length * 8; // Rough estimate
    } else if (data is Map) {
      return data.length * 16; // Rough estimate
    } else if (data is String) {
      return data.length * 2; // UTF-16
    } else {
      return 64; // Default estimate
    }
  }
}


