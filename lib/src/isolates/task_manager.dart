/// Task management system cho isolate-based compression
/// 
/// File này implement hệ thống quản lý tác vụ với cancellation support,
/// priority queuing, và resource management.
library;

import 'dart:async';
import 'dart:collection';
import 'dart:math' as math;
import 'progress_tracker.dart';

/// Độ ưu tiên của tác vụ
enum TaskPriority {
  low(1),
  normal(2),
  high(3),
  urgent(4);
  
  const TaskPriority(this.value);
  final int value;
}

/// Trạng thái của tác vụ
enum TaskStatus {
  /// Đang chờ trong queue
  queued,
  
  /// Đang chạy
  running,
  
  /// Ho<PERSON>n thành
  completed,
  
  /// Bị hủy
  cancelled,
  
  /// Lỗi
  failed,
  
  /// Tạm dừng
  paused,
}

/// Thông tin tác vụ
class TaskInfo {
  /// ID duy nhất của tác vụ
  final String id;
  
  /// Tên mô tả tác vụ
  final String name;
  
  /// Độ ưu tiên
  final TaskPriority priority;
  
  /// Thời gian tạo
  final DateTime createdAt;
  
  /// Thời gian bắt đầu
  DateTime? startedAt;
  
  /// Thời gian hoàn thành
  DateTime? completedAt;
  
  /// Trạng thái hiện tại
  TaskStatus status;
  
  /// Thông tin lỗi nếu có
  String? errorMessage;
  
  /// Metadata bổ sung
  final Map<String, dynamic> metadata;
  
  /// Cancellation token
  final CancellationToken cancellationToken;
  
  TaskInfo({
    required this.id,
    required this.name,
    this.priority = TaskPriority.normal,
    DateTime? createdAt,
    this.status = TaskStatus.queued,
    Map<String, dynamic>? metadata,
    CancellationToken? cancellationToken,
  }) : createdAt = createdAt ?? DateTime.now(),
       metadata = metadata ?? {},
       cancellationToken = cancellationToken ?? CancellationToken();
  
  /// Thời gian chờ trong queue
  Duration get queueTime {
    final start = startedAt ?? DateTime.now();
    return start.difference(createdAt);
  }
  
  /// Thời gian xử lý
  Duration? get processingTime {
    if (startedAt == null) return null;
    final end = completedAt ?? DateTime.now();
    return end.difference(startedAt!);
  }
  
  /// Tổng thời gian
  Duration get totalTime {
    final end = completedAt ?? DateTime.now();
    return end.difference(createdAt);
  }
  
  /// Có bị cancel không
  bool get isCancelled => cancellationToken.isCancelled;
  
  @override
  String toString() {
    return 'TaskInfo(id: $id, name: $name, status: $status, priority: $priority)';
  }
}

/// Cancellation token cho tác vụ
class CancellationToken {
  bool _isCancelled = false;
  final Completer<void> _completer = Completer<void>();
  String? _reason;
  
  /// Có bị cancel không
  bool get isCancelled => _isCancelled;
  
  /// Lý do cancel
  String? get reason => _reason;
  
  /// Future hoàn thành khi bị cancel
  Future<void> get cancelled => _completer.future;
  
  /// Cancel với lý do
  void cancel([String? reason]) {
    if (!_isCancelled) {
      _isCancelled = true;
      _reason = reason ?? 'Cancelled by user';
      _completer.complete();
    }
  }
  
  /// Kiểm tra và throw nếu bị cancel
  void throwIfCancelled() {
    if (_isCancelled) {
      throw TaskCancelledException(_reason ?? 'Task was cancelled');
    }
  }
}

/// Exception khi tác vụ bị cancel
class TaskCancelledException implements Exception {
  final String message;
  const TaskCancelledException(this.message);
  
  @override
  String toString() => 'TaskCancelledException: $message';
}

/// Task manager
class TaskManager {
  final PriorityQueue<TaskInfo> _queue = PriorityQueue<TaskInfo>(_taskComparator);
  final Map<String, TaskInfo> _tasks = {};
  final Map<String, TaskInfo> _runningTasks = {};
  final Set<String> _pausedTasks = {};
  
  int _maxConcurrentTasks = 3;
  bool _isProcessing = false;
  
  final StreamController<TaskInfo> _taskStatusController = StreamController.broadcast();
  final StreamController<TaskManagerStats> _statsController = StreamController.broadcast();
  
  /// Stream của task status changes
  Stream<TaskInfo> get taskStatusStream => _taskStatusController.stream;
  
  /// Stream của manager statistics
  Stream<TaskManagerStats> get statsStream => _statsController.stream;
  
  /// Số lượng tác vụ đồng thời tối đa
  int get maxConcurrentTasks => _maxConcurrentTasks;
  set maxConcurrentTasks(int value) {
    _maxConcurrentTasks = math.max(1, value);
    _processQueue(); // Có thể chạy thêm tasks
  }
  
  /// Thêm tác vụ vào queue
  String addTask({
    required String name,
    TaskPriority priority = TaskPriority.normal,
    Map<String, dynamic>? metadata,
    CancellationToken? cancellationToken,
  }) {
    final taskId = _generateTaskId();
    final task = TaskInfo(
      id: taskId,
      name: name,
      priority: priority,
      metadata: metadata,
      cancellationToken: cancellationToken,
    );
    
    _tasks[taskId] = task;
    _queue.add(task);
    
    _notifyTaskStatusChange(task);
    _updateStats();
    
    // Bắt đầu xử lý queue
    _processQueue();
    
    return taskId;
  }
  
  /// Hủy tác vụ
  bool cancelTask(String taskId, [String? reason]) {
    final task = _tasks[taskId];
    if (task == null) return false;
    
    task.cancellationToken.cancel(reason);
    
    if (task.status == TaskStatus.queued) {
      // Remove from queue
      _queue.remove(task);
      task.status = TaskStatus.cancelled;
      task.completedAt = DateTime.now();
      
      _notifyTaskStatusChange(task);
      _updateStats();
    } else if (task.status == TaskStatus.running) {
      // Will be handled by the running task
      task.status = TaskStatus.cancelled;
      task.completedAt = DateTime.now();
      _runningTasks.remove(taskId);
      
      _notifyTaskStatusChange(task);
      _updateStats();
      _processQueue(); // Start next task
    }
    
    return true;
  }
  
  /// Tạm dừng tác vụ
  bool pauseTask(String taskId) {
    final task = _tasks[taskId];
    if (task == null || task.status != TaskStatus.running) return false;
    
    _pausedTasks.add(taskId);
    task.status = TaskStatus.paused;
    
    _notifyTaskStatusChange(task);
    _updateStats();
    
    return true;
  }
  
  /// Tiếp tục tác vụ đã tạm dừng
  bool resumeTask(String taskId) {
    final task = _tasks[taskId];
    if (task == null || !_pausedTasks.contains(taskId)) return false;
    
    _pausedTasks.remove(taskId);
    task.status = TaskStatus.running;
    
    _notifyTaskStatusChange(task);
    _updateStats();
    
    return true;
  }
  
  /// Lấy thông tin tác vụ
  TaskInfo? getTask(String taskId) => _tasks[taskId];
  
  /// Lấy tất cả tác vụ
  List<TaskInfo> getAllTasks() => _tasks.values.toList();
  
  /// Lấy tác vụ theo trạng thái
  List<TaskInfo> getTasksByStatus(TaskStatus status) {
    return _tasks.values.where((task) => task.status == status).toList();
  }
  
  /// Lấy statistics
  TaskManagerStats getStats() {
    final queued = _queue.length;
    final running = _runningTasks.length;
    final completed = getTasksByStatus(TaskStatus.completed).length;
    final cancelled = getTasksByStatus(TaskStatus.cancelled).length;
    final failed = getTasksByStatus(TaskStatus.failed).length;
    final paused = _pausedTasks.length;
    
    return TaskManagerStats(
      totalTasks: _tasks.length,
      queuedTasks: queued,
      runningTasks: running,
      completedTasks: completed,
      cancelledTasks: cancelled,
      failedTasks: failed,
      pausedTasks: paused,
      maxConcurrentTasks: _maxConcurrentTasks,
    );
  }
  
  /// Xử lý queue
  void _processQueue() {
    if (_isProcessing) return;
    _isProcessing = true;
    
    try {
      while (_runningTasks.length < _maxConcurrentTasks && _queue.isNotEmpty) {
        final task = _queue.removeFirst();
        
        // Kiểm tra nếu task đã bị cancel
        if (task.isCancelled) {
          task.status = TaskStatus.cancelled;
          task.completedAt = DateTime.now();
          _notifyTaskStatusChange(task);
          continue;
        }
        
        // Bắt đầu task
        _startTask(task);
      }
    } finally {
      _isProcessing = false;
      _updateStats();
    }
  }
  
  /// Bắt đầu một tác vụ
  void _startTask(TaskInfo task) {
    task.status = TaskStatus.running;
    task.startedAt = DateTime.now();
    _runningTasks[task.id] = task;
    
    _notifyTaskStatusChange(task);
    
    // Simulate task execution
    // Trong thực tế, đây sẽ là nơi gọi compression isolate
    Timer(Duration(seconds: math.Random().nextInt(10) + 1), () {
      _completeTask(task.id, success: !task.isCancelled);
    });
  }
  
  /// Hoàn thành tác vụ
  void _completeTask(String taskId, {required bool success}) {
    final task = _runningTasks.remove(taskId);
    if (task == null) return;
    
    task.completedAt = DateTime.now();
    
    if (task.isCancelled) {
      task.status = TaskStatus.cancelled;
    } else if (success) {
      task.status = TaskStatus.completed;
    } else {
      task.status = TaskStatus.failed;
    }
    
    _notifyTaskStatusChange(task);
    _updateStats();
    
    // Xử lý task tiếp theo
    _processQueue();
  }
  
  /// Thông báo thay đổi trạng thái
  void _notifyTaskStatusChange(TaskInfo task) {
    _taskStatusController.add(task);
  }
  
  /// Cập nhật statistics
  void _updateStats() {
    _statsController.add(getStats());
  }
  
  /// Tạo task ID duy nhất
  String _generateTaskId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = math.Random().nextInt(10000);
    return 'task_${timestamp}_$random';
  }
  
  /// Cleanup
  void dispose() {
    // Cancel tất cả running tasks
    for (final task in _runningTasks.values) {
      task.cancellationToken.cancel('Task manager disposed');
    }
    
    _taskStatusController.close();
    _statsController.close();
  }
  
  /// Comparator cho priority queue
  static int _taskComparator(TaskInfo a, TaskInfo b) {
    // Priority cao hơn trước
    final priorityCompare = b.priority.value.compareTo(a.priority.value);
    if (priorityCompare != 0) return priorityCompare;
    
    // Cùng priority thì FIFO
    return a.createdAt.compareTo(b.createdAt);
  }
}

/// Statistics của task manager
class TaskManagerStats {
  final int totalTasks;
  final int queuedTasks;
  final int runningTasks;
  final int completedTasks;
  final int cancelledTasks;
  final int failedTasks;
  final int pausedTasks;
  final int maxConcurrentTasks;
  
  const TaskManagerStats({
    required this.totalTasks,
    required this.queuedTasks,
    required this.runningTasks,
    required this.completedTasks,
    required this.cancelledTasks,
    required this.failedTasks,
    required this.pausedTasks,
    required this.maxConcurrentTasks,
  });
  
  /// Tỷ lệ thành công
  double get successRate {
    final finished = completedTasks + cancelledTasks + failedTasks;
    return finished > 0 ? completedTasks / finished : 0.0;
  }
  
  /// Utilization rate
  double get utilizationRate {
    return maxConcurrentTasks > 0 ? runningTasks / maxConcurrentTasks : 0.0;
  }
  
  @override
  String toString() {
    return 'TaskManagerStats(total: $totalTasks, queued: $queuedTasks, '
           'running: $runningTasks, completed: $completedTasks, '
           'success rate: ${(successRate * 100).toStringAsFixed(1)}%)';
  }
}

/// Priority queue implementation
class PriorityQueue<T> {
  final List<T> _heap = [];
  final Comparator<T> _compare;
  
  PriorityQueue(this._compare);
  
  bool get isEmpty => _heap.isEmpty;
  bool get isNotEmpty => _heap.isNotEmpty;
  int get length => _heap.length;
  
  void add(T element) {
    _heap.add(element);
    _bubbleUp(_heap.length - 1);
  }
  
  T removeFirst() {
    if (_heap.isEmpty) throw StateError('Queue is empty');
    
    final result = _heap[0];
    final last = _heap.removeLast();
    
    if (_heap.isNotEmpty) {
      _heap[0] = last;
      _bubbleDown(0);
    }
    
    return result;
  }
  
  bool remove(T element) {
    final index = _heap.indexOf(element);
    if (index == -1) return false;
    
    final last = _heap.removeLast();
    if (index < _heap.length) {
      _heap[index] = last;
      _bubbleUp(index);
      _bubbleDown(index);
    }
    
    return true;
  }
  
  void _bubbleUp(int index) {
    while (index > 0) {
      final parentIndex = (index - 1) ~/ 2;
      if (_compare(_heap[index], _heap[parentIndex]) >= 0) break;
      
      _swap(index, parentIndex);
      index = parentIndex;
    }
  }
  
  void _bubbleDown(int index) {
    while (true) {
      final leftChild = 2 * index + 1;
      final rightChild = 2 * index + 2;
      int smallest = index;
      
      if (leftChild < _heap.length && 
          _compare(_heap[leftChild], _heap[smallest]) < 0) {
        smallest = leftChild;
      }
      
      if (rightChild < _heap.length && 
          _compare(_heap[rightChild], _heap[smallest]) < 0) {
        smallest = rightChild;
      }
      
      if (smallest == index) break;
      
      _swap(index, smallest);
      index = smallest;
    }
  }
  
  void _swap(int i, int j) {
    final temp = _heap[i];
    _heap[i] = _heap[j];
    _heap[j] = temp;
  }
}

/// Global task manager instance
final TaskManager globalTaskManager = TaskManager();
