import 'dart:isolate';
import 'dart:typed_data';
import 'dart:async';
import '../compression/compression_options.dart';
import '../compression/compression_result.dart';
import '../image/pixel_data.dart';

/// Message types cho isolate communication
enum IsolateMessageType {
  /// <PERSON><PERSON><PERSON> cầu nén ảnh
  compressRequest,
  
  /// Kết quả nén
  compressResult,
  
  /// Cập nhật progress
  progressUpdate,
  
  /// Yêu cầu hủy task
  cancelRequest,
  
  /// Xác nhận hủy
  cancelConfirm,
  
  /// Lỗi xảy ra
  error,
  
  /// Ping để kiểm tra isolate còn sống
  ping,
  
  /// Pong response
  pong,
}

/// Message được gửi giữa main isolate và worker isolate
class IsolateMessage {
  final IsolateMessageType type;
  final String taskId;
  final Map<String, dynamic> data;
  
  const IsolateMessage({
    required this.type,
    required this.taskId,
    required this.data,
  });
  
  /// Serialize message để gửi qua isolate
  Map<String, dynamic> toMap() {
    return {
      'type': type.name,
      'taskId': taskId,
      'data': data,
    };
  }
  
  /// Deserialize message từ isolate
  factory IsolateMessage.fromMap(Map<String, dynamic> map) {
    return IsolateMessage(
      type: IsolateMessageType.values.firstWhere(
        (e) => e.name == map['type'],
      ),
      taskId: map['taskId'],
      data: Map<String, dynamic>.from(map['data']),
    );
  }
}

/// Worker isolate cho compression tasks
class CompressionIsolate {
  static SendPort? _workerSendPort;
  static ReceivePort? _mainReceivePort;
  static Isolate? _isolate;
  static bool _isInitialized = false;
  
  /// Callbacks cho các events
  static final Map<String, Function(CompressionResult)> _resultCallbacks = {};
  static final Map<String, ProgressCallback> _progressCallbacks = {};
  static final Map<String, Function(String)> _errorCallbacks = {};
  
  /// Initialize isolate worker
  static Future<void> initialize() async {
    if (_isInitialized) return;
    
    _mainReceivePort = ReceivePort();
    
    // Spawn worker isolate
    _isolate = await Isolate.spawn(
      _isolateEntryPoint,
      _mainReceivePort!.sendPort,
    );
    
    // Listen for messages từ worker
    _mainReceivePort!.listen(_handleMessageFromWorker);
    
    // Đợi worker gửi SendPort
    final completer = Completer<void>();
    late StreamSubscription subscription;
    
    subscription = _mainReceivePort!.listen((message) {
      if (message is SendPort) {
        _workerSendPort = message;
        _isInitialized = true;
        subscription.cancel();
        completer.complete();
      }
    });
    
    await completer.future;
  }
  
  /// Entry point cho worker isolate
  static void _isolateEntryPoint(SendPort mainSendPort) {
    final workerReceivePort = ReceivePort();
    
    // Gửi SendPort về main isolate
    mainSendPort.send(workerReceivePort.sendPort);
    
    // Listen for messages từ main isolate
    workerReceivePort.listen((message) {
      _handleMessageInWorker(message, mainSendPort);
    });
  }
  
  /// Handle message trong worker isolate
  static void _handleMessageInWorker(dynamic rawMessage, SendPort mainSendPort) {
    try {
      final message = IsolateMessage.fromMap(rawMessage);
      
      switch (message.type) {
        case IsolateMessageType.compressRequest:
          _processCompressionRequest(message, mainSendPort);
          break;
          
        case IsolateMessageType.cancelRequest:
          _processCancelRequest(message, mainSendPort);
          break;
          
        case IsolateMessageType.ping:
          _sendPong(message.taskId, mainSendPort);
          break;
          
        default:
          break;
      }
    } catch (e) {
      // Gửi error về main isolate
      final errorMessage = IsolateMessage(
        type: IsolateMessageType.error,
        taskId: 'unknown',
        data: {'error': e.toString()},
      );
      mainSendPort.send(errorMessage.toMap());
    }
  }
  
  /// Xử lý compression request trong worker
  static void _processCompressionRequest(IsolateMessage message, SendPort mainSendPort) {
    final taskId = message.taskId;
    
    try {
      // Extract data từ message
      final imageBytes = Uint8List.fromList(List<int>.from(message.data['imageBytes']));
      final optionsMap = Map<String, dynamic>.from(message.data['options']);
      final options = CompressionOptions.fromMap(optionsMap);
      
      // Thực hiện compression
      final result = _performCompression(taskId, imageBytes, options, mainSendPort);
      
      // Gửi kết quả về main isolate
      final resultMessage = IsolateMessage(
        type: IsolateMessageType.compressResult,
        taskId: taskId,
        data: result.toMap(),
      );
      mainSendPort.send(resultMessage.toMap());
      
    } catch (e) {
      // Gửi error về main isolate
      final errorMessage = IsolateMessage(
        type: IsolateMessageType.error,
        taskId: taskId,
        data: {'error': e.toString()},
      );
      mainSendPort.send(errorMessage.toMap());
    }
  }
  
  /// Thực hiện compression trong worker isolate
  static CompressionResult _performCompression(
    String taskId,
    Uint8List imageBytes,
    CompressionOptions options,
    SendPort mainSendPort,
  ) {
    final stopwatch = Stopwatch()..start();
    
    // Load và process image
    // Note: Đây là simplified version, sẽ cần integrate với ImageProcessor
    
    // Tạo mock result cho demo
    // Trong implementation thực tế sẽ thực hiện DCT compression
    final compressedData = _mockCompression(imageBytes, options, taskId, mainSendPort);
    
    stopwatch.stop();
    
    // Tạo mock ImageInfo
    final imageInfo = ImageInfo(
      width: 800, // Mock values
      height: 600,
      channels: 3,
      format: 'JPEG',
      bitDepth: 8,
      hasAlpha: false,
      colorSpace: 'RGB',
    );
    
    return CompressionResult(
      compressedData: compressedData,
      compressionRatio: compressedData.length / imageBytes.length,
      originalSize: imageBytes.length,
      compressedSize: compressedData.length,
      processingTime: stopwatch.elapsed,
      imageInfo: imageInfo,
      taskId: taskId,
      qualityUsed: options.quality,
      usedAdaptiveQuantization: options.useAdaptiveQuantization,
      processedBlocks: 100, // Mock value
    );
  }
  
  /// Mock compression cho demo
  static Uint8List _mockCompression(
    Uint8List imageBytes,
    CompressionOptions options,
    String taskId,
    SendPort mainSendPort,
  ) {
    // Simulate compression với progress updates
    final totalSteps = 10;
    
    for (int step = 0; step < totalSteps; step++) {
      // Simulate work
      final start = DateTime.now().millisecondsSinceEpoch;
      while (DateTime.now().millisecondsSinceEpoch - start < 50) {
        // Busy wait để simulate work
      }
      
      // Send progress update
      final progressMessage = IsolateMessage(
        type: IsolateMessageType.progressUpdate,
        taskId: taskId,
        data: {
          'completed': step + 1,
          'total': totalSteps,
          'currentItem': 'Processing block ${step + 1}',
        },
      );
      mainSendPort.send(progressMessage.toMap());
    }
    
    // Return compressed data (mock)
    final compressionRatio = 1.0 - (options.quality / 100.0 * 0.5);
    final targetSize = (imageBytes.length * compressionRatio).round();
    return Uint8List(targetSize);
  }
  
  /// Xử lý cancel request
  static void _processCancelRequest(IsolateMessage message, SendPort mainSendPort) {
    final taskId = message.taskId;
    
    // Trong implementation thực tế, sẽ set flag để stop compression
    // Hiện tại chỉ gửi confirm
    final confirmMessage = IsolateMessage(
      type: IsolateMessageType.cancelConfirm,
      taskId: taskId,
      data: {'cancelled': true},
    );
    mainSendPort.send(confirmMessage.toMap());
  }
  
  /// Gửi pong response
  static void _sendPong(String taskId, SendPort mainSendPort) {
    final pongMessage = IsolateMessage(
      type: IsolateMessageType.pong,
      taskId: taskId,
      data: {'timestamp': DateTime.now().millisecondsSinceEpoch},
    );
    mainSendPort.send(pongMessage.toMap());
  }
  
  /// Handle message từ worker isolate
  static void _handleMessageFromWorker(dynamic rawMessage) {
    try {
      final message = IsolateMessage.fromMap(rawMessage);
      
      switch (message.type) {
        case IsolateMessageType.compressResult:
          _handleCompressionResult(message);
          break;
          
        case IsolateMessageType.progressUpdate:
          _handleProgressUpdate(message);
          break;
          
        case IsolateMessageType.error:
          _handleError(message);
          break;
          
        case IsolateMessageType.cancelConfirm:
          _handleCancelConfirm(message);
          break;
          
        case IsolateMessageType.pong:
          // Handle pong if needed
          break;
          
        default:
          break;
      }
    } catch (e) {
      print('Error handling message from worker: $e');
    }
  }
  
  /// Handle compression result
  static void _handleCompressionResult(IsolateMessage message) {
    final taskId = message.taskId;
    final callback = _resultCallbacks.remove(taskId);
    
    if (callback != null) {
      // Reconstruct CompressionResult từ map
      final resultData = message.data;
      final imageInfoData = Map<String, dynamic>.from(resultData['imageInfo']);
      
      final imageInfo = ImageInfo(
        width: imageInfoData['width'],
        height: imageInfoData['height'],
        channels: imageInfoData['channels'],
        format: imageInfoData['format'],
        bitDepth: imageInfoData['bitDepth'],
        hasAlpha: imageInfoData['hasAlpha'],
        colorSpace: imageInfoData['colorSpace'],
      );
      
      final result = CompressionResult(
        compressedData: Uint8List(resultData['compressedSize']), // Mock data
        compressionRatio: resultData['compressionRatio'],
        originalSize: resultData['originalSize'],
        compressedSize: resultData['compressedSize'],
        processingTime: Duration(milliseconds: resultData['processingTimeMs']),
        imageInfo: imageInfo,
        taskId: resultData['taskId'],
        qualityUsed: resultData['qualityUsed'],
        usedAdaptiveQuantization: resultData['usedAdaptiveQuantization'],
        processedBlocks: resultData['processedBlocks'],
        psnr: resultData['psnr'],
        ssim: resultData['ssim'],
        errorMessage: resultData['errorMessage'],
        isSuccess: resultData['isSuccess'],
      );
      
      callback(result);
    }
  }
  
  /// Handle progress update
  static void _handleProgressUpdate(IsolateMessage message) {
    final taskId = message.taskId;
    final callback = _progressCallbacks[taskId];
    
    if (callback != null) {
      final data = message.data;
      callback(
        data['completed'],
        data['total'],
        data['currentItem'],
      );
    }
  }
  
  /// Handle error
  static void _handleError(IsolateMessage message) {
    final taskId = message.taskId;
    final callback = _errorCallbacks.remove(taskId);
    
    if (callback != null) {
      callback(message.data['error']);
    }
  }
  
  /// Handle cancel confirmation
  static void _handleCancelConfirm(IsolateMessage message) {
    final taskId = message.taskId;
    
    // Cleanup callbacks
    _resultCallbacks.remove(taskId);
    _progressCallbacks.remove(taskId);
    _errorCallbacks.remove(taskId);
  }
  
  /// Submit compression task
  static Future<void> submitCompressionTask(
    String taskId,
    Uint8List imageBytes,
    CompressionOptions options, {
    required Function(CompressionResult) onResult,
    ProgressCallback? onProgress,
    Function(String)? onError,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }
    
    // Register callbacks
    _resultCallbacks[taskId] = onResult;
    if (onProgress != null) {
      _progressCallbacks[taskId] = onProgress;
    }
    if (onError != null) {
      _errorCallbacks[taskId] = onError;
    }
    
    // Send compression request
    final message = IsolateMessage(
      type: IsolateMessageType.compressRequest,
      taskId: taskId,
      data: {
        'imageBytes': imageBytes,
        'options': options.toMap(),
      },
    );
    
    _workerSendPort!.send(message.toMap());
  }
  
  /// Cancel compression task
  static void cancelTask(String taskId) {
    if (!_isInitialized || _workerSendPort == null) return;
    
    final message = IsolateMessage(
      type: IsolateMessageType.cancelRequest,
      taskId: taskId,
      data: {},
    );
    
    _workerSendPort!.send(message.toMap());
  }
  
  /// Ping worker isolate
  static void ping(String taskId) {
    if (!_isInitialized || _workerSendPort == null) return;
    
    final message = IsolateMessage(
      type: IsolateMessageType.ping,
      taskId: taskId,
      data: {},
    );
    
    _workerSendPort!.send(message.toMap());
  }
  
  /// Shutdown isolate
  static void shutdown() {
    _isolate?.kill();
    _mainReceivePort?.close();
    
    _isolate = null;
    _mainReceivePort = null;
    _workerSendPort = null;
    _isInitialized = false;
    
    // Clear callbacks
    _resultCallbacks.clear();
    _progressCallbacks.clear();
    _errorCallbacks.clear();
  }
  
  /// Check if isolate is initialized
  static bool get isInitialized => _isInitialized;
  
  /// Get number of pending tasks
  static int get pendingTasksCount => _resultCallbacks.length;
}
