/// <PERSON><PERSON> lý khối 8x8 cho DCT compression
/// 
/// File này chứa logic để chia ảnh thành các khối 8x8,
/// xử lý từng khối qua DCT transform và quantization,
/// sau đó ghép lại thành ảnh hoàn chỉnh.
library;

import 'dart:typed_data';
import 'dart:math' as math;
import 'dct_transform.dart';
import 'quantization.dart';
import '../utils/constants.dart';
import '../utils/math_utils.dart';
import '../utils/memory_manager.dart';

/// Thông tin về một khối 8x8
class BlockInfo {
  final int x;
  final int y;
  final int channel;
  final List<List<int>> pixelData;
  final List<List<double>>? dctData;
  final List<List<int>>? quantizedData;
  
  const BlockInfo({
    required this.x,
    required this.y,
    required this.channel,
    required this.pixelData,
    this.dctData,
    this.quantizedData,
  });
  
  /// Tạo copy với dữ liệu mới
  BlockInfo copyWith({
    List<List<double>>? dctData,
    List<List<int>>? quantizedData,
  }) {
    return BlockInfo(
      x: x,
      y: y,
      channel: channel,
      pixelData: pixelData,
      dctData: dctData ?? this.dctData,
      quantizedData: quantizedData ?? this.quantizedData,
    );
  }
}

/// Kết quả xử lý khối
class BlockProcessingResult {
  final List<BlockInfo> processedBlocks;
  final int totalBlocks;
  final Duration processingTime;
  final Map<String, dynamic> statistics;
  
  const BlockProcessingResult({
    required this.processedBlocks,
    required this.totalBlocks,
    required this.processingTime,
    required this.statistics,
  });
}

/// Xử lý khối 8x8 cho DCT compression
class BlockProcessor {
  final MemoryManager _memoryManager = MemoryManager();
  
  /// Chia ảnh thành các khối 8x8 cho một channel
  /// 
  /// [pixelData]: Dữ liệu pixel 3D [height][width][channels]
  /// [channel]: Channel index (0=R, 1=G, 2=B)
  /// Returns: List các khối 8x8
  List<BlockInfo> splitIntoBlocks(List<List<List<int>>> pixelData, int channel) {
    final height = pixelData.length;
    final width = pixelData[0].length;
    final blocks = <BlockInfo>[];
    
    // Tính số khối theo chiều ngang và dọc
    final blocksHorizontal = (width + DctConstants.blockSize - 1) ~/ DctConstants.blockSize;
    final blocksVertical = (height + DctConstants.blockSize - 1) ~/ DctConstants.blockSize;
    
    for (int blockY = 0; blockY < blocksVertical; blockY++) {
      for (int blockX = 0; blockX < blocksHorizontal; blockX++) {
        final block = _extractBlock(pixelData, blockX, blockY, channel);
        blocks.add(BlockInfo(
          x: blockX,
          y: blockY,
          channel: channel,
          pixelData: block,
        ));
      }
    }
    
    return blocks;
  }
  
  /// Trích xuất một khối 8x8 từ vị trí cụ thể
  List<List<int>> _extractBlock(List<List<List<int>>> pixelData, 
                                int blockX, int blockY, int channel) {
    final height = pixelData.length;
    final width = pixelData[0].length;
    final block = List.generate(DctConstants.blockSize, 
                               (_) => List<int>.filled(DctConstants.blockSize, 0));
    
    final startX = blockX * DctConstants.blockSize;
    final startY = blockY * DctConstants.blockSize;
    
    for (int y = 0; y < DctConstants.blockSize; y++) {
      for (int x = 0; x < DctConstants.blockSize; x++) {
        final pixelX = startX + x;
        final pixelY = startY + y;
        
        // Padding với pixel cuối cùng nếu vượt quá biên
        final actualX = math.min(pixelX, width - 1);
        final actualY = math.min(pixelY, height - 1);
        
        block[y][x] = pixelData[actualY][actualX][channel];
      }
    }
    
    return block;
  }
  
  /// Xử lý một khối qua DCT và quantization
  /// 
  /// [block]: Khối pixel 8x8
  /// [quantTable]: Bảng lượng tử hóa
  /// Returns: Khối đã được xử lý
  BlockInfo processBlock(BlockInfo block, List<List<int>> quantTable) {
    // Chuyển pixel về range [-128, 127] cho DCT
    final centeredBlock = _centerPixelValues(block.pixelData);
    
    // Thực hiện DCT
    final dctBlock = DctTransform.forwardDct(centeredBlock);
    
    // Quantization
    final quantizedBlock = Quantization.quantize(dctBlock, quantTable);
    
    return block.copyWith(
      dctData: dctBlock,
      quantizedData: quantizedBlock,
    );
  }
  
  /// Xử lý ngược một khối (dequantization + IDCT)
  /// 
  /// [block]: Khối đã được quantized
  /// [quantTable]: Bảng lượng tử hóa
  /// Returns: Khối pixel đã được khôi phục
  BlockInfo deprocessBlock(BlockInfo block, List<List<int>> quantTable) {
    assert(block.quantizedData != null, 'Block phải có quantized data');
    
    // Dequantization
    final dequantizedBlock = Quantization.dequantize(block.quantizedData!, quantTable);
    
    // Inverse DCT
    final pixelBlock = DctTransform.inverseDct(dequantizedBlock);
    
    // Chuyển về range [0, 255]
    final restoredBlock = _uncenterPixelValues(pixelBlock);
    
    return BlockInfo(
      x: block.x,
      y: block.y,
      channel: block.channel,
      pixelData: restoredBlock,
      dctData: dequantizedBlock.map((row) => row.map((val) => val.toDouble()).toList()).toList(),
      quantizedData: block.quantizedData,
    );
  }
  
  /// Chuyển pixel values từ [0,255] sang [-128,127]
  List<List<double>> _centerPixelValues(List<List<int>> block) {
    return block.map((row) => 
      row.map((pixel) => (pixel - DctConstants.pixelOffset).toDouble()).toList()
    ).toList();
  }
  
  /// Chuyển pixel values từ [-128,127] về [0,255]
  List<List<int>> _uncenterPixelValues(List<List<int>> block) {
    return block.map((row) => 
      row.map((pixel) => MathUtils.clamp(pixel + DctConstants.pixelOffset, 0, 255)).toList()
    ).toList();
  }
  
  /// Ghép các khối lại thành ảnh hoàn chỉnh
  /// 
  /// [blocks]: List các khối đã xử lý
  /// [originalWidth]: Chiều rộng ảnh gốc
  /// [originalHeight]: Chiều cao ảnh gốc
  /// [channel]: Channel index
  /// Returns: Dữ liệu pixel 2D cho channel này
  List<List<int>> combineBlocks(List<BlockInfo> blocks, 
                               int originalWidth, int originalHeight, int channel) {
    final result = List.generate(originalHeight, 
                                (_) => List<int>.filled(originalWidth, 0));
    
    for (final block in blocks) {
      if (block.channel != channel) continue;
      
      final startX = block.x * DctConstants.blockSize;
      final startY = block.y * DctConstants.blockSize;
      
      for (int y = 0; y < DctConstants.blockSize; y++) {
        for (int x = 0; x < DctConstants.blockSize; x++) {
          final pixelX = startX + x;
          final pixelY = startY + y;
          
          // Chỉ copy pixel trong bounds của ảnh gốc
          if (pixelX < originalWidth && pixelY < originalHeight) {
            result[pixelY][pixelX] = block.pixelData[y][x];
          }
        }
      }
    }
    
    return result;
  }
  
  /// Xử lý batch các khối với memory management
  /// 
  /// [blocks]: List các khối cần xử lý
  /// [quantTable]: Bảng lượng tử hóa
  /// [onProgress]: Callback báo tiến trình
  /// Returns: Kết quả xử lý batch
  Future<BlockProcessingResult> processBatch(
    List<BlockInfo> blocks,
    List<List<int>> quantTable, {
    void Function(int completed, int total)? onProgress,
  }) async {
    final stopwatch = Stopwatch()..start();
    final processedBlocks = <BlockInfo>[];
    final statistics = <String, dynamic>{
      'totalBlocks': blocks.length,
      'memoryUsagePeak': 0,
      'averageProcessingTimePerBlock': 0.0,
    };
    
    int memoryUsagePeak = 0;
    final blockTimes = <int>[];
    
    for (int i = 0; i < blocks.length; i++) {
      final blockStopwatch = Stopwatch()..start();
      
      // Xử lý khối
      final processedBlock = processBlock(blocks[i], quantTable);
      processedBlocks.add(processedBlock);
      
      blockStopwatch.stop();
      blockTimes.add(blockStopwatch.elapsedMicroseconds);
      
      // Theo dõi memory usage
      final currentMemory = _memoryManager.getMemoryInfo().currentUsage;
      memoryUsagePeak = math.max(memoryUsagePeak, currentMemory);
      
      // Báo tiến trình
      onProgress?.call(i + 1, blocks.length);
      
      // Auto cleanup nếu cần
      _memoryManager.autoCleanupIfNeeded();
      
      // Yield để không block UI
      if (i % PerformanceConstants.maxBlocksPerBatch == 0) {
        await Future.delayed(Duration.zero);
      }
    }
    
    stopwatch.stop();
    
    // Tính statistics
    statistics['memoryUsagePeak'] = memoryUsagePeak;
    statistics['averageProcessingTimePerBlock'] = 
        blockTimes.isNotEmpty ? blockTimes.reduce((a, b) => a + b) / blockTimes.length : 0.0;
    statistics['totalProcessingTime'] = stopwatch.elapsedMicroseconds;
    
    return BlockProcessingResult(
      processedBlocks: processedBlocks,
      totalBlocks: blocks.length,
      processingTime: stopwatch.elapsed,
      statistics: statistics,
    );
  }
  
  /// Tính toán entropy trung bình của các khối (đo độ phức tạp)
  double calculateAverageEntropy(List<BlockInfo> blocks) {
    if (blocks.isEmpty) return 0.0;
    
    final entropies = blocks.map((block) => 
      MathUtils.calculateEntropy(block.pixelData)
    ).toList();
    
    return MathUtils.mean(entropies);
  }
  
  /// Phân tích chất lượng khối sau compression
  Map<String, dynamic> analyzeBlockQuality(BlockInfo original, BlockInfo compressed) {
    // Convert 2D block to 3D format (height x width x channels) for PSNR calculation
    final original3D = original.pixelData.map((row) =>
      row.map((p) => [p]).toList()
    ).toList();
    final compressed3D = compressed.pixelData.map((row) =>
      row.map((p) => [p]).toList()
    ).toList();

    final psnr = MathUtils.calculatePSNR(original3D, compressed3D);
    
    final ssim = MathUtils.calculateSSIM(original.pixelData, compressed.pixelData);
    
    return {
      'psnr': psnr,
      'ssim': ssim,
      'entropy_original': MathUtils.calculateEntropy(original.pixelData),
      'entropy_compressed': MathUtils.calculateEntropy(compressed.pixelData),
    };
  }
}
