/// Adaptive quantization system cho DCT compression
/// 
/// File này implement các thuật toán adaptive quantization để tối ưu
/// chất lượng nén dựa trên nội dung ảnh và yêu cầu compression ratio.
library;

import 'dart:math' as math;
import 'dart:typed_data';
import '../utils/math_utils.dart';
import '../utils/constants.dart';
import '../image/pixel_data.dart';
import 'quantization.dart';

/// Loại adaptive quantization
enum AdaptiveQuantizationType {
  /// Dựa trên complexity của ảnh
  contentBased,
  
  /// Dựa trên target compression ratio
  ratioBased,
  
  /// Dựa trên perceptual quality
  perceptualBased,
  
  /// Kết hợp nhiều yếu tố
  hybrid,
}

/// Thông tin phân tích complexity của ảnh
class ImageComplexityInfo {
  /// <PERSON><PERSON> phức tạp tổng thể (0.0 - 1.0)
  final double overallComplexity;
  
  /// <PERSON><PERSON> phức tạp theo vùng
  final List<List<double>> regionComplexity;
  
  /// Entropy của ảnh
  final double entropy;
  
  /// Variance trung bình
  final double averageVariance;
  
  /// Số lượng edge pixels
  final int edgePixelCount;
  
  /// Histogram distribution
  final List<int> histogram;
  
  const ImageComplexityInfo({
    required this.overallComplexity,
    required this.regionComplexity,
    required this.entropy,
    required this.averageVariance,
    required this.edgePixelCount,
    required this.histogram,
  });
}

/// Adaptive quantization engine
class AdaptiveQuantization {
  static const int _blockSize = 8;
  static const double _complexityThreshold = 0.5;
  static const double _edgeThreshold = 30.0;
  
  /// Tạo adaptive quantization table dựa trên image complexity
  /// 
  /// [pixelData]: Dữ liệu pixel của ảnh
  /// [baseQuality]: Quality cơ bản (1-100)
  /// [targetRatio]: Target compression ratio (optional)
  /// [type]: Loại adaptive quantization
  /// Returns: Bảng quantization được tối ưu
  static List<List<int>> createAdaptiveQuantizationTable(
    PixelData pixelData,
    int baseQuality, {
    double? targetRatio,
    AdaptiveQuantizationType type = AdaptiveQuantizationType.hybrid,
    bool isLuminance = true,
  }) {
    assert(baseQuality >= 1 && baseQuality <= 100, 'Quality phải trong khoảng 1-100');
    
    // Phân tích complexity của ảnh
    final complexityInfo = _analyzeImageComplexity(pixelData);
    
    // Tính adjusted quality dựa trên type
    final adjustedQuality = _calculateAdaptiveQuality(
      baseQuality,
      complexityInfo,
      targetRatio,
      type,
    );
    
    // Tạo base quantization table
    final baseTable = Quantization.createQuantizationTable(
      adjustedQuality,
      isLuminance: isLuminance,
    );
    
    // Apply adaptive adjustments
    return _applyAdaptiveAdjustments(
      baseTable,
      complexityInfo,
      type,
    );
  }
  
  /// Phân tích complexity của ảnh
  static ImageComplexityInfo _analyzeImageComplexity(PixelData pixelData) {
    final width = pixelData.info.width;
    final height = pixelData.info.height;
    final channels = pixelData.info.channels;
    
    // Tính entropy
    final entropy = _calculateEntropy(pixelData);
    
    // Tính variance trung bình
    final averageVariance = _calculateAverageVariance(pixelData);
    
    // Đếm edge pixels
    final edgePixelCount = _countEdgePixels(pixelData);
    
    // Tạo histogram
    final histogram = _createHistogram(pixelData);
    
    // Tính complexity theo vùng (8x8 blocks)
    final regionComplexity = _calculateRegionComplexity(pixelData);
    
    // Tính overall complexity
    final overallComplexity = _calculateOverallComplexity(
      entropy,
      averageVariance,
      edgePixelCount,
      width * height,
    );
    
    return ImageComplexityInfo(
      overallComplexity: overallComplexity,
      regionComplexity: regionComplexity,
      entropy: entropy,
      averageVariance: averageVariance,
      edgePixelCount: edgePixelCount,
      histogram: histogram,
    );
  }
  
  /// Tính entropy của ảnh
  static double _calculateEntropy(PixelData pixelData) {
    final histogram = _createHistogram(pixelData);
    final totalPixels = pixelData.info.width * pixelData.info.height;
    
    double entropy = 0.0;
    for (int count in histogram) {
      if (count > 0) {
        final probability = count / totalPixels;
        entropy -= probability * math.log(probability) / math.ln2;
      }
    }
    
    return entropy;
  }
  
  /// Tính variance trung bình
  static double _calculateAverageVariance(PixelData pixelData) {
    final width = pixelData.info.width;
    final height = pixelData.info.height;
    final channels = pixelData.info.channels;
    
    // Tính mean cho mỗi channel
    final means = List<double>.filled(channels, 0.0);
    for (int y = 0; y < height; y++) {
      for (int x = 0; x < width; x++) {
        final pixel = pixelData.getPixel(x, y);
        for (int c = 0; c < channels; c++) {
          means[c] += pixel[c];
        }
      }
    }
    
    final totalPixels = width * height;
    for (int c = 0; c < channels; c++) {
      means[c] /= totalPixels;
    }
    
    // Tính variance cho mỗi channel
    final variances = List<double>.filled(channels, 0.0);
    for (int y = 0; y < height; y++) {
      for (int x = 0; x < width; x++) {
        final pixel = pixelData.getPixel(x, y);
        for (int c = 0; c < channels; c++) {
          final diff = pixel[c] - means[c];
          variances[c] += diff * diff;
        }
      }
    }
    
    for (int c = 0; c < channels; c++) {
      variances[c] /= totalPixels;
    }
    
    // Return average variance across channels
    return variances.reduce((a, b) => a + b) / channels;
  }
  
  /// Đếm edge pixels sử dụng Sobel operator
  static int _countEdgePixels(PixelData pixelData) {
    final width = pixelData.info.width;
    final height = pixelData.info.height;
    int edgeCount = 0;
    
    // Sobel kernels
    const sobelX = [
      [-1, 0, 1],
      [-2, 0, 2],
      [-1, 0, 1],
    ];
    
    const sobelY = [
      [-1, -2, -1],
      [0, 0, 0],
      [1, 2, 1],
    ];
    
    for (int y = 1; y < height - 1; y++) {
      for (int x = 1; x < width - 1; x++) {
        double gx = 0.0, gy = 0.0;
        
        // Apply Sobel operators
        for (int ky = -1; ky <= 1; ky++) {
          for (int kx = -1; kx <= 1; kx++) {
            final pixel = pixelData.getPixel(x + kx, y + ky);
            final intensity = _getGrayscaleIntensity(pixel);
            
            gx += intensity * sobelX[ky + 1][kx + 1];
            gy += intensity * sobelY[ky + 1][kx + 1];
          }
        }
        
        final magnitude = math.sqrt(gx * gx + gy * gy);
        if (magnitude > _edgeThreshold) {
          edgeCount++;
        }
      }
    }
    
    return edgeCount;
  }
  
  /// Tạo histogram cho ảnh
  static List<int> _createHistogram(PixelData pixelData) {
    final histogram = List<int>.filled(256, 0);
    final width = pixelData.info.width;
    final height = pixelData.info.height;
    
    for (int y = 0; y < height; y++) {
      for (int x = 0; x < width; x++) {
        final pixel = pixelData.getPixel(x, y);
        final intensity = _getGrayscaleIntensity(pixel);
        histogram[intensity.clamp(0, 255)]++;
      }
    }
    
    return histogram;
  }
  
  /// Tính complexity theo vùng 8x8
  static List<List<double>> _calculateRegionComplexity(PixelData pixelData) {
    final width = pixelData.info.width;
    final height = pixelData.info.height;
    final blocksX = (width / _blockSize).ceil();
    final blocksY = (height / _blockSize).ceil();
    
    final regionComplexity = List.generate(
      blocksY,
      (_) => List<double>.filled(blocksX, 0.0),
    );
    
    for (int by = 0; by < blocksY; by++) {
      for (int bx = 0; bx < blocksX; bx++) {
        regionComplexity[by][bx] = _calculateBlockComplexity(
          pixelData,
          bx * _blockSize,
          by * _blockSize,
        );
      }
    }
    
    return regionComplexity;
  }
  
  /// Tính complexity của một block 8x8
  static double _calculateBlockComplexity(PixelData pixelData, int startX, int startY) {
    final width = pixelData.info.width;
    final height = pixelData.info.height;
    final endX = math.min(startX + _blockSize, width);
    final endY = math.min(startY + _blockSize, height);
    
    // Tính variance trong block
    double mean = 0.0;
    int pixelCount = 0;
    
    for (int y = startY; y < endY; y++) {
      for (int x = startX; x < endX; x++) {
        final pixel = pixelData.getPixel(x, y);
        mean += _getGrayscaleIntensity(pixel);
        pixelCount++;
      }
    }
    
    if (pixelCount == 0) return 0.0;
    mean /= pixelCount;
    
    double variance = 0.0;
    for (int y = startY; y < endY; y++) {
      for (int x = startX; x < endX; x++) {
        final pixel = pixelData.getPixel(x, y);
        final intensity = _getGrayscaleIntensity(pixel);
        final diff = intensity - mean;
        variance += diff * diff;
      }
    }
    
    variance /= pixelCount;
    
    // Normalize complexity to 0-1 range
    return math.min(variance / 10000.0, 1.0);
  }
  
  /// Tính overall complexity
  static double _calculateOverallComplexity(
    double entropy,
    double averageVariance,
    int edgePixelCount,
    int totalPixels,
  ) {
    // Normalize các metrics
    final normalizedEntropy = math.min(entropy / 8.0, 1.0); // Max entropy ~8 for 8-bit
    final normalizedVariance = math.min(averageVariance / 10000.0, 1.0);
    final normalizedEdgeRatio = math.min(edgePixelCount / totalPixels, 1.0);
    
    // Weighted combination
    return (normalizedEntropy * 0.4 + normalizedVariance * 0.4 + normalizedEdgeRatio * 0.2);
  }
  
  /// Tính adjusted quality dựa trên adaptive type
  static int _calculateAdaptiveQuality(
    int baseQuality,
    ImageComplexityInfo complexityInfo,
    double? targetRatio,
    AdaptiveQuantizationType type,
  ) {
    switch (type) {
      case AdaptiveQuantizationType.contentBased:
        return _calculateContentBasedQuality(baseQuality, complexityInfo);
      case AdaptiveQuantizationType.ratioBased:
        return _calculateRatioBasedQuality(baseQuality, targetRatio);
      case AdaptiveQuantizationType.perceptualBased:
        return _calculatePerceptualBasedQuality(baseQuality, complexityInfo);
      case AdaptiveQuantizationType.hybrid:
        return _calculateHybridQuality(baseQuality, complexityInfo, targetRatio);
    }
  }
  
  /// Content-based quality adjustment
  static int _calculateContentBasedQuality(int baseQuality, ImageComplexityInfo complexityInfo) {
    // Ảnh phức tạp cần quality cao hơn để duy trì chất lượng
    final complexityAdjustment = complexityInfo.overallComplexity * 15; // Max +15 quality
    final adjustedQuality = baseQuality + complexityAdjustment;
    return adjustedQuality.round().clamp(1, 100);
  }
  
  /// Ratio-based quality adjustment
  static int _calculateRatioBasedQuality(int baseQuality, double? targetRatio) {
    if (targetRatio == null) return baseQuality;
    
    // Tính quality cần thiết để đạt target ratio
    final estimatedQuality = (1.0 - targetRatio) * 100;
    return estimatedQuality.round().clamp(1, 100);
  }
  
  /// Perceptual-based quality adjustment
  static int _calculatePerceptualBasedQuality(int baseQuality, ImageComplexityInfo complexityInfo) {
    // Dựa trên entropy và edge content để điều chỉnh quality
    final entropyFactor = complexityInfo.entropy / 8.0; // Normalize
    final edgeFactor = complexityInfo.edgePixelCount / (complexityInfo.regionComplexity.length * 
                      complexityInfo.regionComplexity[0].length * 64); // Normalize
    
    final perceptualAdjustment = (entropyFactor + edgeFactor) * 10; // Max +20 quality
    final adjustedQuality = baseQuality + perceptualAdjustment;
    return adjustedQuality.round().clamp(1, 100);
  }
  
  /// Hybrid quality adjustment
  static int _calculateHybridQuality(
    int baseQuality,
    ImageComplexityInfo complexityInfo,
    double? targetRatio,
  ) {
    final contentQuality = _calculateContentBasedQuality(baseQuality, complexityInfo);
    final ratioQuality = _calculateRatioBasedQuality(baseQuality, targetRatio);
    final perceptualQuality = _calculatePerceptualBasedQuality(baseQuality, complexityInfo);
    
    // Weighted average
    if (targetRatio != null) {
      return ((contentQuality * 0.4 + ratioQuality * 0.4 + perceptualQuality * 0.2)).round().clamp(1, 100);
    } else {
      return ((contentQuality * 0.6 + perceptualQuality * 0.4)).round().clamp(1, 100);
    }
  }
  
  /// Apply adaptive adjustments to quantization table
  static List<List<int>> _applyAdaptiveAdjustments(
    List<List<int>> baseTable,
    ImageComplexityInfo complexityInfo,
    AdaptiveQuantizationType type,
  ) {
    final adjustedTable = List.generate(8, (i) => List<int>.from(baseTable[i]));
    
    // Apply frequency-specific adjustments
    for (int i = 0; i < 8; i++) {
      for (int j = 0; j < 8; j++) {
        final frequency = i + j; // DC = 0, highest AC = 14
        double adjustmentFactor = 1.0;
        
        if (type == AdaptiveQuantizationType.contentBased || 
            type == AdaptiveQuantizationType.hybrid) {
          // Preserve high frequencies in complex regions
          if (complexityInfo.overallComplexity > _complexityThreshold) {
            if (frequency > 4) { // High frequency components
              adjustmentFactor *= 0.8; // Reduce quantization (preserve detail)
            }
          }
        }
        
        if (type == AdaptiveQuantizationType.perceptualBased || 
            type == AdaptiveQuantizationType.hybrid) {
          // Adjust based on perceptual importance
          if (frequency <= 2) { // Low frequency (more important)
            adjustmentFactor *= 0.9;
          } else if (frequency >= 10) { // Very high frequency (less important)
            adjustmentFactor *= 1.2;
          }
        }
        
        adjustedTable[i][j] = (adjustedTable[i][j] * adjustmentFactor).round().clamp(1, 255);
      }
    }
    
    return adjustedTable;
  }
  
  /// Helper: Tính grayscale intensity từ pixel
  static int _getGrayscaleIntensity(List<int> pixel) {
    if (pixel.length == 1) {
      return pixel[0];
    } else if (pixel.length >= 3) {
      // RGB to grayscale using standard weights
      return (0.299 * pixel[0] + 0.587 * pixel[1] + 0.114 * pixel[2]).round();
    } else {
      return pixel[0]; // Fallback
    }
  }
}
