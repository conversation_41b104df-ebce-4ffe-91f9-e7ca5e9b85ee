import 'dart:math' as math;

/// Lớp quản lý lượng tử hóa (quantization) cho DCT coefficients
/// Hỗ trợ bảng lượng tử hóa chuẩn JPEG và custom tables
class Quantization {
  // Bảng lượng tử hóa chuẩn JPEG cho luminance (Y channel)
  static const List<List<int>> standardLuminanceTable = [
    [16, 11, 10, 16, 24, 40, 51, 61],
    [12, 12, 14, 19, 26, 58, 60, 55],
    [14, 13, 16, 24, 40, 57, 69, 56],
    [14, 17, 22, 29, 51, 87, 80, 62],
    [18, 22, 37, 56, 68, 109, 103, 77],
    [24, 35, 55, 64, 81, 104, 113, 92],
    [49, 64, 78, 87, 103, 121, 120, 101],
    [72, 92, 95, 98, 112, 100, 103, 99],
  ];
  
  // Bảng lượng tử hóa chuẩn JPEG cho chrominance (Cb, Cr channels)
  static const List<List<int>> standardChrominanceTable = [
    [17, 18, 24, 47, 99, 99, 99, 99],
    [18, 21, 26, 66, 99, 99, 99, 99],
    [24, 26, 56, 99, 99, 99, 99, 99],
    [47, 66, 99, 99, 99, 99, 99, 99],
    [99, 99, 99, 99, 99, 99, 99, 99],
    [99, 99, 99, 99, 99, 99, 99, 99],
    [99, 99, 99, 99, 99, 99, 99, 99],
    [99, 99, 99, 99, 99, 99, 99, 99],
  ];
  
  /// Tạo bảng lượng tử hóa dựa trên quality factor (1-100)
  /// Quality càng cao thì compression càng ít (chất lượng cao hơn)
  /// 
  /// [quality]: Chất lượng từ 1-100 (100 = chất lượng cao nhất)
  /// [isLuminance]: true cho Y channel, false cho Cb/Cr channels
  /// Returns: Bảng lượng tử hóa 8x8
  static List<List<int>> createQuantizationTable(int quality, {bool isLuminance = true}) {
    assert(quality >= 1 && quality <= 100, 'Quality phải trong khoảng 1-100');
    
    // Chọn bảng chuẩn phù hợp
    final baseTable = isLuminance ? standardLuminanceTable : standardChrominanceTable;
    
    // Tính scaling factor dựa trên quality
    double scaleFactor;
    if (quality < 50) {
      scaleFactor = 5000.0 / quality;
    } else {
      scaleFactor = 200.0 - 2.0 * quality;
    }
    
    // Tạo bảng mới với scaling
    final result = List.generate(8, (_) => List<int>.filled(8, 0));
    
    for (int i = 0; i < 8; i++) {
      for (int j = 0; j < 8; j++) {
        int value = ((baseTable[i][j] * scaleFactor + 50.0) / 100.0).floor();
        // Đảm bảo giá trị trong khoảng hợp lệ [1, 255]
        result[i][j] = value.clamp(1, 255);
      }
    }
    
    return result;
  }
  
  /// Thực hiện lượng tử hóa DCT coefficients
  /// Chia mỗi coefficient cho giá trị tương ứng trong quantization table
  /// 
  /// [dctBlock]: Ma trận 8x8 DCT coefficients
  /// [quantTable]: Bảng lượng tử hóa 8x8
  /// Returns: Ma trận 8x8 quantized coefficients
  static List<List<int>> quantize(List<List<double>> dctBlock, List<List<int>> quantTable) {
    assert(dctBlock.length == 8 && dctBlock[0].length == 8, 
           'DCT block phải có kích thước 8x8');
    assert(quantTable.length == 8 && quantTable[0].length == 8, 
           'Quantization table phải có kích thước 8x8');
    
    final result = List.generate(8, (_) => List<int>.filled(8, 0));
    
    for (int i = 0; i < 8; i++) {
      for (int j = 0; j < 8; j++) {
        // Chia và làm tròn về số nguyên gần nhất
        result[i][j] = (dctBlock[i][j] / quantTable[i][j]).round();
      }
    }
    
    return result;
  }
  
  /// Thực hiện dequantization (khôi phục DCT coefficients)
  /// Nhân mỗi quantized coefficient với giá trị tương ứng trong quantization table
  /// 
  /// [quantizedBlock]: Ma trận 8x8 quantized coefficients
  /// [quantTable]: Bảng lượng tử hóa 8x8
  /// Returns: Ma trận 8x8 DCT coefficients đã khôi phục
  static List<List<double>> dequantize(List<List<int>> quantizedBlock, List<List<int>> quantTable) {
    assert(quantizedBlock.length == 8 && quantizedBlock[0].length == 8, 
           'Quantized block phải có kích thước 8x8');
    assert(quantTable.length == 8 && quantTable[0].length == 8, 
           'Quantization table phải có kích thước 8x8');
    
    final result = List.generate(8, (_) => List<double>.filled(8, 0.0));
    
    for (int i = 0; i < 8; i++) {
      for (int j = 0; j < 8; j++) {
        result[i][j] = quantizedBlock[i][j].toDouble() * quantTable[i][j];
      }
    }
    
    return result;
  }
  
  /// Tạo bảng lượng tử hóa tùy chỉnh với adaptive scaling
  /// Điều chỉnh quantization dựa trên nội dung ảnh và yêu cầu compression ratio
  /// 
  /// [targetCompressionRatio]: Tỷ lệ nén mục tiêu (0.1 - 0.9)
  /// [imageComplexity]: Độ phức tạp của ảnh (0.0 - 1.0)
  /// [isLuminance]: true cho Y channel, false cho Cb/Cr channels
  /// Returns: Bảng lượng tử hóa adaptive
  static List<List<int>> createAdaptiveQuantizationTable(
    double targetCompressionRatio,
    double imageComplexity, {
    bool isLuminance = true,
  }) {
    assert(targetCompressionRatio >= 0.1 && targetCompressionRatio <= 0.9,
           'Target compression ratio phải trong khoảng 0.1-0.9');
    assert(imageComplexity >= 0.0 && imageComplexity <= 1.0,
           'Image complexity phải trong khoảng 0.0-1.0');
    
    // Tính quality dựa trên compression ratio và complexity
    // Ảnh phức tạp cần quality cao hơn để duy trì chất lượng
    final baseQuality = (1.0 - targetCompressionRatio) * 100;
    final complexityAdjustment = imageComplexity * 20; // Tối đa +20 quality
    final adjustedQuality = (baseQuality + complexityAdjustment).clamp(1.0, 100.0);
    
    return createQuantizationTable(adjustedQuality.round(), isLuminance: isLuminance);
  }
  
  /// Tính toán độ phức tạp của ảnh dựa trên variance của DCT coefficients
  /// Sử dụng để điều chỉnh adaptive quantization
  /// 
  /// [dctBlocks]: Danh sách các DCT blocks của ảnh
  /// Returns: Độ phức tạp từ 0.0 (đơn giản) đến 1.0 (phức tạp)
  static double calculateImageComplexity(List<List<List<double>>> dctBlocks) {
    if (dctBlocks.isEmpty) return 0.0;
    
    double totalVariance = 0.0;
    int blockCount = 0;
    
    for (final block in dctBlocks) {
      // Tính variance của AC coefficients (bỏ qua DC coefficient [0][0])
      final acCoefficients = <double>[];
      
      for (int i = 0; i < 8; i++) {
        for (int j = 0; j < 8; j++) {
          if (i != 0 || j != 0) { // Bỏ qua DC coefficient
            acCoefficients.add(block[i][j].abs());
          }
        }
      }
      
      // Tính variance
      if (acCoefficients.isNotEmpty) {
        final mean = acCoefficients.reduce((a, b) => a + b) / acCoefficients.length;
        final variance = acCoefficients
            .map((x) => math.pow(x - mean, 2))
            .reduce((a, b) => a + b) / acCoefficients.length;
        
        totalVariance += variance;
        blockCount++;
      }
    }
    
    if (blockCount == 0) return 0.0;
    
    final avgVariance = totalVariance / blockCount;
    
    // Normalize variance to [0, 1] range
    // Giá trị 1000 là threshold thực nghiệm cho high complexity
    return (avgVariance / 1000.0).clamp(0.0, 1.0);
  }
  
  /// Tối ưu quantization table để đạt compression ratio mục tiêu
  /// Sử dụng binary search để tìm quality phù hợp
  /// 
  /// [targetRatio]: Tỷ lệ nén mục tiêu
  /// [testFunction]: Function để test compression ratio với quality cho trước
  /// [isLuminance]: true cho Y channel, false cho Cb/Cr channels
  /// Returns: Bảng lượng tử hóa tối ưu
  static List<List<int>> optimizeQuantizationTable(
    double targetRatio,
    double Function(int quality) testFunction, {
    bool isLuminance = true,
  }) {
    int minQuality = 1;
    int maxQuality = 100;
    int bestQuality = 50;
    double bestDiff = double.infinity;
    
    // Binary search để tìm quality tối ưu
    while (minQuality <= maxQuality) {
      final midQuality = (minQuality + maxQuality) ~/ 2;
      final actualRatio = testFunction(midQuality);
      final diff = (actualRatio - targetRatio).abs();
      
      if (diff < bestDiff) {
        bestDiff = diff;
        bestQuality = midQuality;
      }
      
      if (actualRatio < targetRatio) {
        // Cần compression nhiều hơn (quality thấp hơn)
        maxQuality = midQuality - 1;
      } else {
        // Cần compression ít hơn (quality cao hơn)
        minQuality = midQuality + 1;
      }
    }
    
    return createQuantizationTable(bestQuality, isLuminance: isLuminance);
  }
  
  /// Validate quantization table
  /// Kiểm tra tính hợp lệ của bảng lượng tử hóa
  /// 
  /// [quantTable]: Bảng lượng tử hóa cần kiểm tra
  /// Returns: true nếu bảng hợp lệ
  static bool validateQuantizationTable(List<List<int>> quantTable) {
    if (quantTable.length != 8 || quantTable[0].length != 8) {
      return false;
    }
    
    for (int i = 0; i < 8; i++) {
      for (int j = 0; j < 8; j++) {
        final value = quantTable[i][j];
        if (value < 1 || value > 255) {
          return false;
        }
      }
    }
    
    return true;
  }
}
