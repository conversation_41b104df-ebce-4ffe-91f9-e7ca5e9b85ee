import 'dart:math' as math;
import 'dart:typed_data';

/// <PERSON>ớ<PERSON> triển khai thuật toán Discrete Cosine Transform (DCT) 2D
/// Implementation của DCT cho khối 8x8 pixel với tối ưu hiệu năng
class DctTransform {
  // Bảng tra cứu cosine được tính trước để tối ưu hiệu năng
  static final List<List<double>> _cosineTable = _initializeCosineTable();
  
  // Hằng số C(u) và C(v) cho công thức DCT
  static const double _c0 = 1.0 / math.sqrt2; // C(0) = 1/√2
  static const double _c1 = 1.0; // C(u) = 1 với u ≠ 0
  
  /// Khởi tạo bảng tra cứu cosine để tối ưu tính toán DCT
  /// Tính trước các giá trị cos((2x+1)uπ/16) cho u,x = 0..7
  static List<List<double>> _initializeCosineTable() {
    final table = List.generate(8, (_) => List<double>.filled(8, 0.0));
    
    for (int u = 0; u < 8; u++) {
      for (int x = 0; x < 8; x++) {
        // Công thức: cos((2x+1)uπ/16)
        table[u][x] = math.cos((2 * x + 1) * u * math.pi / 16.0);
      }
    }
    
    return table;
  }
  
  /// Thực hiện Forward DCT 2D cho khối 8x8
  /// Chuyển đổi từ spatial domain sang frequency domain
  /// 
  /// [block]: Ma trận 8x8 pixel values (0-255)
  /// Returns: Ma trận 8x8 DCT coefficients
  static List<List<double>> forwardDct(List<List<int>> block) {
    // Kiểm tra kích thước đầu vào
    assert(block.length == 8 && block[0].length == 8, 
           'Block phải có kích thước 8x8');
    
    final result = List.generate(8, (_) => List<double>.filled(8, 0.0));
    
    // Áp dụng công thức DCT 2D:
    // F(u,v) = (1/4) * C(u) * C(v) * Σ Σ f(x,y) * cos((2x+1)uπ/16) * cos((2y+1)vπ/16)
    for (int u = 0; u < 8; u++) {
      for (int v = 0; v < 8; v++) {
        double sum = 0.0;
        
        // Tính tổng cho tất cả pixel trong khối
        for (int x = 0; x < 8; x++) {
          for (int y = 0; y < 8; y++) {
            sum += block[x][y] * _cosineTable[u][x] * _cosineTable[v][y];
          }
        }
        
        // Áp dụng hệ số C(u) và C(v)
        final cu = (u == 0) ? _c0 : _c1;
        final cv = (v == 0) ? _c0 : _c1;
        
        result[u][v] = 0.25 * cu * cv * sum;
      }
    }
    
    return result;
  }
  
  /// Thực hiện Inverse DCT 2D cho khối 8x8
  /// Chuyển đổi từ frequency domain về spatial domain
  /// 
  /// [dctBlock]: Ma trận 8x8 DCT coefficients
  /// Returns: Ma trận 8x8 pixel values
  static List<List<int>> inverseDct(List<List<double>> dctBlock) {
    // Kiểm tra kích thước đầu vào
    assert(dctBlock.length == 8 && dctBlock[0].length == 8, 
           'DCT block phải có kích thước 8x8');
    
    final result = List.generate(8, (_) => List<int>.filled(8, 0));
    
    // Áp dụng công thức Inverse DCT 2D:
    // f(x,y) = Σ Σ C(u) * C(v) * F(u,v) * cos((2x+1)uπ/16) * cos((2y+1)vπ/16)
    for (int x = 0; x < 8; x++) {
      for (int y = 0; y < 8; y++) {
        double sum = 0.0;
        
        // Tính tổng cho tất cả DCT coefficients
        for (int u = 0; u < 8; u++) {
          for (int v = 0; v < 8; v++) {
            final cu = (u == 0) ? _c0 : _c1;
            final cv = (v == 0) ? _c0 : _c1;
            
            sum += cu * cv * dctBlock[u][v] * _cosineTable[u][x] * _cosineTable[v][y];
          }
        }
        
        // Làm tròn và giới hạn giá trị pixel trong khoảng [0, 255]
        final pixelValue = (0.25 * sum).round();
        result[x][y] = pixelValue.clamp(0, 255);
      }
    }
    
    return result;
  }
  
  /// Thực hiện Fast DCT sử dụng thuật toán separable
  /// Tối ưu hiệu năng bằng cách thực hiện DCT 1D theo hàng rồi theo cột
  /// 
  /// [block]: Ma trận 8x8 pixel values
  /// Returns: Ma trận 8x8 DCT coefficients
  static List<List<double>> fastForwardDct(List<List<int>> block) {
    // Bước 1: DCT 1D theo hàng
    final rowDct = List.generate(8, (_) => List<double>.filled(8, 0.0));
    
    for (int row = 0; row < 8; row++) {
      final rowData = block[row].map((e) => e.toDouble()).toList();
      rowDct[row] = _dct1D(rowData);
    }
    
    // Bước 2: DCT 1D theo cột
    final result = List.generate(8, (_) => List<double>.filled(8, 0.0));
    
    for (int col = 0; col < 8; col++) {
      final colData = List.generate(8, (row) => rowDct[row][col]);
      final colDct = _dct1D(colData);
      
      for (int row = 0; row < 8; row++) {
        result[row][col] = colDct[row];
      }
    }
    
    return result;
  }
  
  /// Thực hiện Fast Inverse DCT sử dụng thuật toán separable
  /// 
  /// [dctBlock]: Ma trận 8x8 DCT coefficients
  /// Returns: Ma trận 8x8 pixel values
  static List<List<int>> fastInverseDct(List<List<double>> dctBlock) {
    // Bước 1: IDCT 1D theo cột
    final colIdct = List.generate(8, (_) => List<double>.filled(8, 0.0));
    
    for (int col = 0; col < 8; col++) {
      final colData = List.generate(8, (row) => dctBlock[row][col]);
      final colResult = _idct1D(colData);
      
      for (int row = 0; row < 8; row++) {
        colIdct[row][col] = colResult[row];
      }
    }
    
    // Bước 2: IDCT 1D theo hàng
    final result = List.generate(8, (_) => List<int>.filled(8, 0));
    
    for (int row = 0; row < 8; row++) {
      final rowResult = _idct1D(colIdct[row]);
      
      for (int col = 0; col < 8; col++) {
        final pixelValue = rowResult[col].round();
        result[row][col] = pixelValue.clamp(0, 255);
      }
    }
    
    return result;
  }
  
  /// DCT 1D cho vector 8 phần tử
  /// Sử dụng trong fast DCT algorithm
  static List<double> _dct1D(List<double> input) {
    assert(input.length == 8, 'Input phải có 8 phần tử');
    
    final result = List<double>.filled(8, 0.0);
    
    for (int u = 0; u < 8; u++) {
      double sum = 0.0;
      
      for (int x = 0; x < 8; x++) {
        sum += input[x] * _cosineTable[u][x];
      }
      
      final cu = (u == 0) ? _c0 : _c1;
      result[u] = cu * sum / 2.0;
    }
    
    return result;
  }
  
  /// Inverse DCT 1D cho vector 8 phần tử
  /// Sử dụng trong fast inverse DCT algorithm
  static List<double> _idct1D(List<double> input) {
    assert(input.length == 8, 'Input phải có 8 phần tử');
    
    final result = List<double>.filled(8, 0.0);
    
    for (int x = 0; x < 8; x++) {
      double sum = 0.0;
      
      for (int u = 0; u < 8; u++) {
        final cu = (u == 0) ? _c0 : _c1;
        sum += cu * input[u] * _cosineTable[u][x];
      }
      
      result[x] = sum / 2.0;
    }
    
    return result;
  }
  
  /// Kiểm tra tính chính xác của DCT transform
  /// Thực hiện forward DCT rồi inverse DCT và so sánh với input gốc
  /// 
  /// [block]: Ma trận 8x8 pixel values để test
  /// Returns: true nếu error < threshold
  static bool validateTransform(List<List<int>> block, {double threshold = 1.0}) {
    final dctResult = forwardDct(block);
    final reconstructed = inverseDct(dctResult);
    
    // Tính Mean Square Error
    double mse = 0.0;
    for (int i = 0; i < 8; i++) {
      for (int j = 0; j < 8; j++) {
        final diff = block[i][j] - reconstructed[i][j];
        mse += diff * diff;
      }
    }
    mse /= 64; // Chia cho số pixel
    
    return mse < threshold;
  }
}
