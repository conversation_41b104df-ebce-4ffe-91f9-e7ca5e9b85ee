/// <PERSON><PERSON><PERSON> soát chất lượng tinh vi cho DCT compression
/// 
/// File này chứa logic để quản lý chất lượng nén một cách tinh vi,
/// bao gồm adaptive quality, perceptual quality metrics và quality optimization.
library;

import 'dart:math' as math;
import '../utils/constants.dart';
import '../utils/math_utils.dart';
import '../image/pixel_data.dart';
import '../dct/quantization.dart';
import 'compression_options.dart';

/// Metrics chất lượng ảnh
class QualityMetrics {
  final double psnr;
  final double ssim;
  final double entropy;
  final double variance;
  final double edgeStrength;
  final double colorfulness;
  
  const QualityMetrics({
    required this.psnr,
    required this.ssim,
    required this.entropy,
    required this.variance,
    required this.edgeStrength,
    required this.colorfulness,
  });
  
  /// Tính overall quality score (0-100)
  double get overallScore {
    // Weighted combination của các metrics
    final psnrScore = math.min(100, psnr * 2); // PSNR thường 20-50
    final ssimScore = ssim * 100; // SSIM 0-1
    final entropyScore = math.min(100, entropy * 12.5); // Entropy thường 0-8
    
    return (psnrScore * 0.4 + ssimScore * 0.4 + entropyScore * 0.2);
  }
  
  @override
  String toString() {
    return 'QualityMetrics(PSNR: ${psnr.toStringAsFixed(2)}, '
           'SSIM: ${ssim.toStringAsFixed(3)}, '
           'Entropy: ${entropy.toStringAsFixed(2)}, '
           'Overall: ${overallScore.toStringAsFixed(1)})';
  }
}

/// Kết quả phân tích chất lượng
class QualityAnalysis {
  final QualityMetrics originalMetrics;
  final QualityMetrics compressedMetrics;
  final double qualityLoss;
  final bool meetsThreshold;
  final Map<String, dynamic> recommendations;
  
  const QualityAnalysis({
    required this.originalMetrics,
    required this.compressedMetrics,
    required this.qualityLoss,
    required this.meetsThreshold,
    required this.recommendations,
  });
  
  @override
  String toString() {
    return 'QualityAnalysis(qualityLoss: ${qualityLoss.toStringAsFixed(1)}%, '
           'meetsThreshold: $meetsThreshold)';
  }
}

/// Adaptive quality configuration
class AdaptiveQualityConfig {
  final double complexityWeight;
  final double edgeWeight;
  final double colorWeight;
  final double textureWeight;
  final bool enablePerceptualOptimization;
  final double qualityThreshold;
  
  const AdaptiveQualityConfig({
    this.complexityWeight = 0.3,
    this.edgeWeight = 0.25,
    this.colorWeight = 0.2,
    this.textureWeight = 0.25,
    this.enablePerceptualOptimization = true,
    this.qualityThreshold = 80.0,
  });
}

/// Kiểm soát chất lượng nén
class QualityController {
  final AdaptiveQualityConfig _config;
  
  const QualityController({AdaptiveQualityConfig? config})
      : _config = config ?? const AdaptiveQualityConfig();
  
  /// Tính toán quality metrics cho ảnh
  QualityMetrics calculateMetrics(PixelData pixelData) {
    final data3D = pixelData.to3DArray();
    
    // Tính PSNR (so với ảnh perfect white)
    final perfectWhite = List.generate(
      pixelData.info.height,
      (_) => List.generate(
        pixelData.info.width,
        (_) => List.filled(pixelData.info.channels, 255),
      ),
    );
    final psnr = MathUtils.calculatePSNR(data3D, perfectWhite);
    
    // Tính SSIM cho channel đầu tiên
    final channel0 = pixelData.extractChannel(0);
    final ssim = MathUtils.calculateSSIM(channel0, channel0); // Self-SSIM = 1.0
    
    // Tính entropy
    final entropy = MathUtils.calculateEntropy(channel0);
    
    // Tính variance
    final stats = pixelData.calculateChannelStatistics(0);
    final variance = stats['standardDeviation']! * stats['standardDeviation']!;
    
    // Tính edge strength
    final edgeStrength = _calculateEdgeStrength(channel0);
    
    // Tính colorfulness
    final colorfulness = _calculateColorfulness(data3D);
    
    return QualityMetrics(
      psnr: psnr.isFinite ? psnr : 100.0,
      ssim: ssim,
      entropy: entropy,
      variance: variance,
      edgeStrength: edgeStrength,
      colorfulness: colorfulness,
    );
  }
  
  /// Tính edge strength của ảnh
  double _calculateEdgeStrength(List<List<int>> channel) {
    final height = channel.length;
    final width = channel[0].length;
    double totalEdgeStrength = 0.0;
    int edgeCount = 0;
    
    // Sobel edge detection
    for (int y = 1; y < height - 1; y++) {
      for (int x = 1; x < width - 1; x++) {
        // Sobel X kernel
        final gx = (-1 * channel[y-1][x-1]) + (1 * channel[y-1][x+1]) +
                   (-2 * channel[y][x-1]) + (2 * channel[y][x+1]) +
                   (-1 * channel[y+1][x-1]) + (1 * channel[y+1][x+1]);
        
        // Sobel Y kernel
        final gy = (-1 * channel[y-1][x-1]) + (-2 * channel[y-1][x]) + (-1 * channel[y-1][x+1]) +
                   (1 * channel[y+1][x-1]) + (2 * channel[y+1][x]) + (1 * channel[y+1][x+1]);
        
        final magnitude = math.sqrt(gx * gx + gy * gy);
        totalEdgeStrength += magnitude;
        edgeCount++;
      }
    }
    
    return edgeCount > 0 ? totalEdgeStrength / edgeCount : 0.0;
  }
  
  /// Tính colorfulness của ảnh
  double _calculateColorfulness(List<List<List<int>>> data3D) {
    if (data3D[0][0].length < 3) return 0.0; // Grayscale
    
    final height = data3D.length;
    final width = data3D[0].length;
    double totalColorfulness = 0.0;
    
    for (int y = 0; y < height; y++) {
      for (int x = 0; x < width; x++) {
        final r = data3D[y][x][0];
        final g = data3D[y][x][1];
        final b = data3D[y][x][2];
        
        // Tính color saturation
        final max = math.max(r, math.max(g, b));
        final min = math.min(r, math.min(g, b));
        final saturation = max > 0 ? (max - min) / max : 0.0;
        
        totalColorfulness += saturation;
      }
    }
    
    return totalColorfulness / (height * width);
  }
  
  /// Phân tích chất lượng so sánh giữa original và compressed
  QualityAnalysis analyzeQuality(PixelData original, PixelData compressed, {
    double qualityThreshold = 80.0,
  }) {
    final originalMetrics = calculateMetrics(original);
    final compressedMetrics = calculateMetrics(compressed);
    
    // Tính quality loss
    final qualityLoss = ((originalMetrics.overallScore - compressedMetrics.overallScore) / 
                        originalMetrics.overallScore) * 100;
    
    // Kiểm tra threshold
    final meetsThreshold = compressedMetrics.overallScore >= qualityThreshold;
    
    // Tạo recommendations
    final recommendations = _generateRecommendations(
      originalMetrics, 
      compressedMetrics, 
      qualityLoss,
    );
    
    return QualityAnalysis(
      originalMetrics: originalMetrics,
      compressedMetrics: compressedMetrics,
      qualityLoss: qualityLoss,
      meetsThreshold: meetsThreshold,
      recommendations: recommendations,
    );
  }
  
  /// Tạo recommendations dựa trên quality analysis
  Map<String, dynamic> _generateRecommendations(
    QualityMetrics original,
    QualityMetrics compressed,
    double qualityLoss,
  ) {
    final recommendations = <String, dynamic>{};
    
    // PSNR recommendations
    if (compressed.psnr < 25) {
      recommendations['psnr'] = 'PSNR quá thấp, cần tăng quality';
    } else if (compressed.psnr > 40) {
      recommendations['psnr'] = 'PSNR cao, có thể giảm quality để tiết kiệm dung lượng';
    }
    
    // SSIM recommendations
    if (compressed.ssim < 0.8) {
      recommendations['ssim'] = 'SSIM thấp, cấu trúc ảnh bị ảnh hưởng nhiều';
    }
    
    // Overall quality loss
    if (qualityLoss > 20) {
      recommendations['overall'] = 'Mất chất lượng quá nhiều, cần tăng quality';
    } else if (qualityLoss < 5) {
      recommendations['overall'] = 'Chất lượng tốt, có thể tối ưu thêm compression';
    }
    
    // Edge preservation
    final edgeLoss = (original.edgeStrength - compressed.edgeStrength) / original.edgeStrength;
    if (edgeLoss > 0.3) {
      recommendations['edges'] = 'Mất nhiều detail edges, cần quantization nhẹ hơn';
    }
    
    return recommendations;
  }
  
  /// Tính adaptive quality dựa trên image characteristics
  int calculateAdaptiveQuality(PixelData pixelData, int baseQuality) {
    final metrics = calculateMetrics(pixelData);
    
    // Tính complexity score
    final complexityScore = _calculateComplexityScore(metrics);
    
    // Điều chỉnh quality dựa trên complexity
    double qualityAdjustment = 0;
    
    // Ảnh phức tạp cần quality cao hơn
    if (complexityScore > 0.7) {
      qualityAdjustment += 10; // Tăng quality cho ảnh phức tạp
    } else if (complexityScore < 0.3) {
      qualityAdjustment -= 5; // Giảm quality cho ảnh đơn giản
    }
    
    // Điều chỉnh dựa trên edge content
    if (metrics.edgeStrength > 50) {
      qualityAdjustment += 5; // Tăng quality cho ảnh có nhiều edges
    }
    
    // Điều chỉnh dựa trên colorfulness
    if (metrics.colorfulness > 0.5) {
      qualityAdjustment += 3; // Tăng quality cho ảnh màu sắc phong phú
    }
    
    final adaptiveQuality = baseQuality + qualityAdjustment.round();
    return MathUtils.clamp(adaptiveQuality, 1, 100);
  }
  
  /// Tính complexity score của ảnh (0-1)
  double _calculateComplexityScore(QualityMetrics metrics) {
    // Normalize các metrics về scale 0-1
    final normalizedEntropy = math.min(1.0, metrics.entropy / 8.0);
    final normalizedVariance = math.min(1.0, metrics.variance / 10000.0);
    final normalizedEdgeStrength = math.min(1.0, metrics.edgeStrength / 100.0);
    final normalizedColorfulness = metrics.colorfulness;
    
    // Weighted combination
    return (normalizedEntropy * _config.complexityWeight +
            normalizedVariance * _config.textureWeight +
            normalizedEdgeStrength * _config.edgeWeight +
            normalizedColorfulness * _config.colorWeight);
  }
  
  /// Tối ưu quality để đạt target quality score
  int optimizeQualityForTarget(PixelData pixelData, double targetQualityScore) {
    int bestQuality = CompressionConstants.defaultQuality;
    double bestScore = 0;
    
    // Binary search để tìm quality tối ưu
    int low = 1;
    int high = 100;
    
    while (low <= high) {
      final mid = (low + high) ~/ 2;
      
      // Ước tính quality score với quality này
      final estimatedScore = _estimateQualityScore(pixelData, mid);
      
      if ((estimatedScore - targetQualityScore).abs() < 
          (bestScore - targetQualityScore).abs()) {
        bestQuality = mid;
        bestScore = estimatedScore;
      }
      
      if (estimatedScore < targetQualityScore) {
        low = mid + 1;
      } else {
        high = mid - 1;
      }
    }
    
    return bestQuality;
  }
  
  /// Ước tính quality score với quality setting cho trước
  double _estimateQualityScore(PixelData pixelData, int quality) {
    // Simplified estimation dựa trên quality và image characteristics
    final metrics = calculateMetrics(pixelData);
    final complexityScore = _calculateComplexityScore(metrics);
    
    // Base score từ quality setting
    double baseScore = quality * 0.8; // Quality 100 -> score 80
    
    // Điều chỉnh dựa trên image complexity
    // Ảnh phức tạp sẽ có score thấp hơn với cùng quality setting
    final complexityPenalty = complexityScore * 15;
    
    return math.max(0, baseScore - complexityPenalty);
  }
  
  /// Tạo custom quantization table dựa trên quality analysis
  List<List<int>> createQualityOptimizedQuantTable(
    PixelData pixelData,
    int baseQuality, {
    bool isLuminance = true,
  }) {
    final metrics = calculateMetrics(pixelData);
    
    // Tạo base table
    var quantTable = Quantization.createQuantizationTable(baseQuality, isLuminance: isLuminance);
    
    // Điều chỉnh table dựa trên image characteristics
    if (metrics.edgeStrength > 50) {
      // Giảm quantization cho high-frequency components để preserve edges
      quantTable = _adjustTableForEdgePreservation(quantTable);
    }
    
    if (metrics.colorfulness > 0.5 && !isLuminance) {
      // Điều chỉnh chrominance table cho ảnh màu sắc phong phú
      quantTable = _adjustTableForColorPreservation(quantTable);
    }
    
    return quantTable;
  }
  
  /// Điều chỉnh quantization table để preserve edges
  List<List<int>> _adjustTableForEdgePreservation(List<List<int>> table) {
    final adjusted = table.map((row) => List<int>.from(row)).toList();
    
    // Giảm quantization cho high-frequency components (góc phải dưới)
    for (int i = 4; i < 8; i++) {
      for (int j = 4; j < 8; j++) {
        adjusted[i][j] = math.max(1, (adjusted[i][j] * 0.8).round());
      }
    }
    
    return adjusted;
  }
  
  /// Điều chỉnh quantization table để preserve colors
  List<List<int>> _adjustTableForColorPreservation(List<List<int>> table) {
    final adjusted = table.map((row) => List<int>.from(row)).toList();
    
    // Giảm quantization cho DC component và low-frequency
    for (int i = 0; i < 4; i++) {
      for (int j = 0; j < 4; j++) {
        adjusted[i][j] = math.max(1, (adjusted[i][j] * 0.9).round());
      }
    }
    
    return adjusted;
  }
}
