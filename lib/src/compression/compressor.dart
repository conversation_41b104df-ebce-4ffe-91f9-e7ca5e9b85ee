/// Main compression engine cho DCT compression
/// 
/// File này chứa logic ch<PERSON>h để orchestrate to<PERSON>n bộ quá trình
/// DCT compression từ input đến output.
library;

import 'dart:typed_data';
import 'dart:math' as math;
import '../utils/constants.dart';
import '../utils/math_utils.dart';
import '../utils/memory_manager.dart';
import '../utils/validation.dart';
import '../image/pixel_data.dart';
import '../image/format_handler.dart';
import '../image/color_space.dart';
import '../dct/block_processor.dart';
import '../dct/quantization.dart';
import 'compression_options.dart';
import 'compression_result.dart';
import 'compression_bounds.dart';
import 'quality_controller.dart';
import 'encoder.dart';
import 'decoder.dart';

/// Exception cho compression errors
class CompressionException implements Exception {
  final String message;
  final String? errorCode;
  
  const CompressionException(this.message, [this.errorCode]);
  
  @override
  String toString() => 'CompressionException: $message';
}

/// Main compression engine
class DctCompressor {
  final MemoryManager _memoryManager = MemoryManager();
  final BlockProcessor _blockProcessor = BlockProcessor();
  final DctEncoder _encoder = DctEncoder();
  final DctDecoder _decoder = DctDecoder();
  
  /// Nén ảnh với các tùy chọn nâng cao
  Future<CompressionResult> compressImage(
    dynamic input, {
    CompressionOptions? options,
  }) async {
    options ??= const CompressionOptions();
    
    // Validate options
    ValidationUtils.validateCompressionOptions(options);
    
    final stopwatch = Stopwatch()..start();
    
    try {
      // Load và decode input
      final pixelData = await _loadInput(input);
      
      // Validate memory requirements
      if (!_memoryManager.canAllocateForImage(
        pixelData.info.width,
        pixelData.info.height,
        pixelData.info.channels,
      )) {
        throw CompressionException(
          'Ảnh quá lớn cho memory limit hiện tại',
          ErrorConstants.memoryLimitExceeded,
        );
      }
      
      // Color space conversion nếu cần
      final convertedPixelData = await _convertColorSpace(pixelData, options);
      
      // Adaptive quality adjustment
      final adjustedOptions = await _adjustQualityIfNeeded(convertedPixelData, options);
      
      // Thực hiện compression
      final compressedData = await _performCompression(convertedPixelData, adjustedOptions);
      
      // Kiểm tra compression bounds
      final boundsResult = await _checkCompressionBounds(
        pixelData.memoryUsage,
        compressedData.length,
        adjustedOptions,
      );
      
      stopwatch.stop();
      
      // Tạo compression result
      return _createCompressionResult(
        pixelData,
        compressedData,
        adjustedOptions,
        stopwatch.elapsed,
        boundsResult,
      );
    } catch (e) {
      stopwatch.stop();
      if (e is CompressionException) rethrow;
      throw CompressionException('Compression thất bại: $e');
    } finally {
      // Cleanup memory
      _memoryManager.autoCleanupIfNeeded();
    }
  }
  
  /// Giải nén dữ liệu đã nén DCT
  Future<PixelData> decompress(Uint8List compressedData) async {
    try {
      final decodingResult = _decoder.decode(compressedData);
      return decodingResult.pixelData;
    } catch (e) {
      throw CompressionException('Decompression thất bại: $e');
    }
  }
  
  /// Load input thành PixelData
  Future<PixelData> _loadInput(dynamic input) async {
    if (input is PixelData) {
      return input;
    } else if (input is Uint8List) {
      final decodeResult = FormatHandler.decodeImage(input);
      return decodeResult.pixelData;
    } else if (input is String) {
      final decodeResult = await FormatHandler.decodeImageFromFile(input);
      return decodeResult.pixelData;
    } else {
      throw CompressionException(
        'Input type không được hỗ trợ: ${input.runtimeType}',
        ErrorConstants.invalidInput,
      );
    }
  }
  
  /// Convert color space nếu cần
  Future<PixelData> _convertColorSpace(PixelData pixelData, CompressionOptions options) async {
    if (options.colorSpace == ColorSpace.rgb) {
      // Đã là RGB, không cần convert
      return pixelData;
    } else if (options.colorSpace == ColorSpace.yuv) {
      // Convert RGB to YUV
      final rgbData = pixelData.to3DArray();
      final yuvData = ColorSpaceConverter.convertRgbToYuv(rgbData);
      return PixelData.from3DArray(yuvData);
    } else {
      throw CompressionException('Color space không được hỗ trợ: ${options.colorSpace}');
    }
  }
  
  /// Điều chỉnh quality nếu cần (adaptive quality)
  Future<CompressionOptions> _adjustQualityIfNeeded(
    PixelData pixelData,
    CompressionOptions options,
  ) async {
    if (!options.useAdaptiveQuantization) {
      return options;
    }
    
    final qualityController = QualityController();
    final adaptiveQuality = qualityController.calculateAdaptiveQuality(
      pixelData,
      options.quality,
    );
    
    if (adaptiveQuality != options.quality) {
      return CompressionOptions(
        quality: adaptiveQuality,
        minCompressionRatio: options.minCompressionRatio,
        maxCompressionRatio: options.maxCompressionRatio,
        preserveMetadata: options.preserveMetadata,
        colorSpace: options.colorSpace,
        outputFormat: options.outputFormat,
        customLuminanceQuantizationTable: options.customLuminanceQuantizationTable,
        customChrominanceQuantizationTable: options.customChrominanceQuantizationTable,
        useIsolate: options.useIsolate,
        progressive: options.progressive,
        useAdaptiveQuantization: options.useAdaptiveQuantization,
        maxImageSizeInMemory: options.maxImageSizeInMemory,
      );
    }
    
    return options;
  }
  
  /// Thực hiện compression chính
  Future<Uint8List> _performCompression(
    PixelData pixelData,
    CompressionOptions options,
  ) async {
    final allBlocks = <BlockInfo>[];
    
    // Process từng channel
    for (int channel = 0; channel < pixelData.info.channels; channel++) {
      // Split thành blocks
      final channelBlocks = _blockProcessor.splitIntoBlocks(
        pixelData.to3DArray(),
        channel,
      );
      
      // Chọn quantization table
      final quantTable = _getQuantizationTable(options, channel);
      
      // Process blocks
      final processedBlocks = <BlockInfo>[];
      for (final block in channelBlocks) {
        final processed = _blockProcessor.processBlock(block, quantTable);
        processedBlocks.add(processed);
      }
      
      allBlocks.addAll(processedBlocks);
    }
    
    // Encode blocks
    final encodingResult = _encoder.encodeBlocks(
      allBlocks,
      pixelData,
      options.quality,
      options.customLuminanceQuantizationTable,
      options.customChrominanceQuantizationTable,
    );
    
    return encodingResult.encodedData;
  }
  
  /// Lấy quantization table phù hợp
  List<List<int>> _getQuantizationTable(CompressionOptions options, int channel) {
    // Custom tables có priority cao nhất
    if (channel == 0 && options.customLuminanceQuantizationTable != null) {
      return options.customLuminanceQuantizationTable!;
    }
    
    if (channel > 0 && options.customChrominanceQuantizationTable != null) {
      return options.customChrominanceQuantizationTable!;
    }
    
    // Sử dụng standard tables
    return Quantization.createQuantizationTable(
      options.quality,
      isLuminance: channel == 0,
    );
  }
  
  /// Kiểm tra compression bounds
  Future<BoundsCheckResult> _checkCompressionBounds(
    int originalSize,
    int compressedSize,
    CompressionOptions options,
  ) async {
    return CompressionBounds.checkBounds(originalSize, compressedSize, options);
  }
  
  /// Tạo compression result
  CompressionResult _createCompressionResult(
    PixelData originalPixelData,
    Uint8List compressedData,
    CompressionOptions options,
    Duration processingTime,
    BoundsCheckResult boundsResult,
  ) {
    final compressionRatio = MathUtils.calculateCompressionRatio(
      originalPixelData.memoryUsage,
      compressedData.length,
    );
    
    return CompressionResult(
      compressedData: compressedData,
      compressionRatio: compressionRatio,
      originalSize: originalPixelData.memoryUsage,
      compressedSize: compressedData.length,
      processingTime: processingTime,
      imageInfo: originalPixelData.info,
      taskId: _generateTaskId(),
      qualityUsed: options.quality,
    );
  }
  
  /// Generate unique task ID
  String _generateTaskId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = math.Random().nextInt(10000);
    return 'dct_${timestamp}_$random';
  }
  
  /// Ước tính kích thước sau nén
  Future<int> estimateCompressedSize(dynamic input, int quality) async {
    try {
      final pixelData = await _loadInput(input);
      
      // Ước tính dựa trên quality và image characteristics
      final baseSize = pixelData.memoryUsage;
      
      // Quality factor (1-100 -> compression factor)
      final qualityFactor = (100 - quality) / 100.0;
      final baseCompressionRatio = 0.1 + (qualityFactor * 0.6); // 0.1 - 0.7
      
      // Điều chỉnh dựa trên image complexity
      final qualityController = QualityController();
      final metrics = qualityController.calculateMetrics(pixelData);
      final complexityFactor = metrics.entropy / 8.0; // 0-1
      
      // Ảnh phức tạp compress ít hơn
      final adjustedRatio = baseCompressionRatio + (complexityFactor * 0.2);
      
      return (baseSize * adjustedRatio).round();
    } catch (e) {
      throw CompressionException('Estimate size thất bại: $e');
    }
  }
  
  /// Lấy thông tin ảnh mà không nén
  Future<ImageInfo> getImageInfo(dynamic input) async {
    try {
      final pixelData = await _loadInput(input);
      return pixelData.info;
    } catch (e) {
      throw CompressionException('Get image info thất bại: $e');
    }
  }
  
  /// Validate input trước khi compression
  Future<bool> validateInput(dynamic input) async {
    try {
      return ValidationUtils.validateInput(input);
    } catch (e) {
      return false;
    }
  }
  
  /// Get memory statistics
  Map<String, dynamic> getMemoryStatistics() {
    return _memoryManager.getStatistics();
  }
  
  /// Force cleanup memory
  void forceMemoryCleanup() {
    _memoryManager.forceCleanup();
  }
}
