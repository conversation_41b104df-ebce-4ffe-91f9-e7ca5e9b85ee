/// Encoder cho DCT compressed data
///
/// File này chứa logic để encode dữ liệu DCT đã được quantized
/// thành format binary compact để lưu trữ hoặc truyền tải.
library;

import 'dart:typed_data';
import 'dart:convert';
import 'dart:math' as math;
import '../utils/constants.dart';
import '../utils/math_utils.dart';
import '../image/pixel_data.dart';
import '../dct/block_processor.dart';

/// Exception cho encoding errors
class EncodingException implements Exception {
  final String message;

  const EncodingException(this.message);

  @override
  String toString() => 'EncodingException: $message';
}

/// Header cho DCT compressed data
class DctHeader {
  final int version;
  final int width;
  final int height;
  final int channels;
  final int quality;
  final String colorSpace;
  final bool hasCustomQuantTables;
  final Map<String, dynamic>? metadata;
  
  const DctHeader({
    required this.version,
    required this.width,
    required this.height,
    required this.channels,
    required this.quality,
    required this.colorSpace,
    required this.hasCustomQuantTables,
    this.metadata,
  });
  
  /// Serialize header to bytes
  Uint8List toBytes() {
    final data = <String, dynamic>{
      'version': version,
      'width': width,
      'height': height,
      'channels': channels,
      'quality': quality,
      'colorSpace': colorSpace,
      'hasCustomQuantTables': hasCustomQuantTables,
      'metadata': metadata,
    };
    
    final jsonString = jsonEncode(data);
    final jsonBytes = utf8.encode(jsonString);
    
    // Header format: [length:4][json_data:length]
    final result = BytesBuilder();
    result.add(_intToBytes(jsonBytes.length, 4));
    result.add(jsonBytes);
    
    return result.toBytes();
  }
  
  /// Deserialize header from bytes
  static DctHeader fromBytes(Uint8List bytes) {
    if (bytes.length < 4) {
      throw const FormatException('Invalid header: too short');
    }
    
    final length = _bytesToInt(bytes.sublist(0, 4));
    if (bytes.length < 4 + length) {
      throw const FormatException('Invalid header: incomplete data');
    }
    
    final jsonBytes = bytes.sublist(4, 4 + length);
    final jsonString = utf8.decode(jsonBytes);
    final data = jsonDecode(jsonString) as Map<String, dynamic>;
    
    return DctHeader(
      version: data['version'] as int,
      width: data['width'] as int,
      height: data['height'] as int,
      channels: data['channels'] as int,
      quality: data['quality'] as int,
      colorSpace: data['colorSpace'] as String,
      hasCustomQuantTables: data['hasCustomQuantTables'] as bool,
      metadata: data['metadata'] as Map<String, dynamic>?,
    );
  }
  
  /// Get header size in bytes
  int get sizeInBytes => toBytes().length;
}

/// Kết quả encoding
class EncodingResult {
  final Uint8List encodedData;
  final DctHeader header;
  final int originalSize;
  final int compressedSize;
  final double compressionRatio;
  final Duration encodingTime;
  final Map<String, dynamic> statistics;
  
  const EncodingResult({
    required this.encodedData,
    required this.header,
    required this.originalSize,
    required this.compressedSize,
    required this.compressionRatio,
    required this.encodingTime,
    required this.statistics,
  });
  
  @override
  String toString() {
    return 'EncodingResult(${MathUtils.formatBytes(originalSize)} -> '
           '${MathUtils.formatBytes(compressedSize)}, '
           'ratio: ${compressionRatio.toStringAsFixed(3)})';
  }
}

/// DCT Encoder
class DctEncoder {
  /// Encode processed blocks thành compressed data
  EncodingResult encodeBlocks(
    List<BlockInfo> processedBlocks,
    PixelData originalPixelData,
    int quality,
    List<List<int>>? customLuminanceTable,
    List<List<int>>? customChrominanceTable,
  ) {
    final stopwatch = Stopwatch()..start();
    
    // Tạo header
    final header = DctHeader(
      version: 1,
      width: originalPixelData.info.width,
      height: originalPixelData.info.height,
      channels: originalPixelData.info.channels,
      quality: quality,
      colorSpace: originalPixelData.info.colorSpace,
      hasCustomQuantTables: customLuminanceTable != null || customChrominanceTable != null,
      metadata: originalPixelData.info.metadata,
    );
    
    final builder = BytesBuilder();
    
    // Add header
    final headerBytes = header.toBytes();
    builder.add(headerBytes);
    
    // Add custom quantization tables nếu có
    if (header.hasCustomQuantTables) {
      builder.add(_encodeQuantizationTables(customLuminanceTable, customChrominanceTable));
    }
    
    // Encode blocks data
    final blocksData = _encodeBlocksData(processedBlocks);
    builder.add(blocksData);
    
    stopwatch.stop();
    
    final encodedData = builder.toBytes();
    final originalSize = originalPixelData.memoryUsage;
    final compressedSize = encodedData.length;
    
    // Tính statistics
    final statistics = _calculateEncodingStatistics(processedBlocks, encodedData);
    
    return EncodingResult(
      encodedData: encodedData,
      header: header,
      originalSize: originalSize,
      compressedSize: compressedSize,
      compressionRatio: MathUtils.calculateCompressionRatio(originalSize, compressedSize),
      encodingTime: stopwatch.elapsed,
      statistics: statistics,
    );
  }
  
  /// Encode quantization tables
  Uint8List _encodeQuantizationTables(
    List<List<int>>? luminanceTable,
    List<List<int>>? chrominanceTable,
  ) {
    final builder = BytesBuilder();
    
    // Luminance table
    if (luminanceTable != null) {
      builder.addByte(1); // Has luminance table
      builder.add(_encodeQuantizationTable(luminanceTable));
    } else {
      builder.addByte(0); // No luminance table
    }
    
    // Chrominance table
    if (chrominanceTable != null) {
      builder.addByte(1); // Has chrominance table
      builder.add(_encodeQuantizationTable(chrominanceTable));
    } else {
      builder.addByte(0); // No chrominance table
    }
    
    return builder.toBytes();
  }
  
  /// Encode một quantization table
  Uint8List _encodeQuantizationTable(List<List<int>> table) {
    final data = Uint8List(64); // 8x8 = 64 values
    int index = 0;
    
    // Zigzag order để tối ưu compression
    final zigzagOrder = _getZigzagOrder();
    
    for (final pos in zigzagOrder) {
      data[index++] = table[pos[0]][pos[1]];
    }
    
    return data;
  }
  
  /// Encode blocks data
  Uint8List _encodeBlocksData(List<BlockInfo> blocks) {
    final builder = BytesBuilder();
    
    // Group blocks theo channel
    final blocksByChannel = <int, List<BlockInfo>>{};
    for (final block in blocks) {
      blocksByChannel.putIfAbsent(block.channel, () => []).add(block);
    }
    
    // Encode từng channel
    for (int channel = 0; channel < blocksByChannel.length; channel++) {
      final channelBlocks = blocksByChannel[channel] ?? [];
      builder.add(_encodeChannelBlocks(channelBlocks));
    }
    
    return builder.toBytes();
  }
  
  /// Encode blocks của một channel
  Uint8List _encodeChannelBlocks(List<BlockInfo> blocks) {
    final builder = BytesBuilder();
    
    // Sort blocks theo vị trí để đảm bảo thứ tự
    blocks.sort((a, b) {
      final yCompare = a.y.compareTo(b.y);
      return yCompare != 0 ? yCompare : a.x.compareTo(b.x);
    });
    
    // Encode số lượng blocks
    builder.add(_intToBytes(blocks.length, 4));
    
    // Encode từng block
    for (final block in blocks) {
      builder.add(_encodeBlock(block));
    }
    
    return builder.toBytes();
  }
  
  /// Encode một block
  Uint8List _encodeBlock(BlockInfo block) {
    assert(block.quantizedData != null, 'Block phải có quantized data');
    
    final builder = BytesBuilder();
    
    // Block position (2 bytes each)
    builder.add(_intToBytes(block.x, 2));
    builder.add(_intToBytes(block.y, 2));
    
    // Quantized data trong zigzag order
    final zigzagOrder = _getZigzagOrder();
    final quantizedValues = <int>[];
    
    for (final pos in zigzagOrder) {
      quantizedValues.add(block.quantizedData![pos[0]][pos[1]]);
    }
    
    // Run-length encoding cho quantized values
    final rleData = _runLengthEncode(quantizedValues);
    builder.add(rleData);
    
    return builder.toBytes();
  }
  
  /// Run-length encoding cho quantized coefficients
  Uint8List _runLengthEncode(List<int> values) {
    final builder = BytesBuilder();
    
    int i = 0;
    while (i < values.length) {
      final value = values[i];
      int count = 1;
      
      // Đếm số lượng values giống nhau liên tiếp
      while (i + count < values.length && values[i + count] == value && count < 255) {
        count++;
      }
      
      // Encode: [count:1][value:2] (signed 16-bit)
      builder.addByte(count);
      builder.add(_intToBytes(value, 2, signed: true));
      
      i += count;
    }
    
    return builder.toBytes();
  }
  
  /// Lấy zigzag order cho 8x8 block
  List<List<int>> _getZigzagOrder() {
    return [
      [0, 0], [0, 1], [1, 0], [2, 0], [1, 1], [0, 2], [0, 3], [1, 2],
      [2, 1], [3, 0], [4, 0], [3, 1], [2, 2], [1, 3], [0, 4], [0, 5],
      [1, 4], [2, 3], [3, 2], [4, 1], [5, 0], [6, 0], [5, 1], [4, 2],
      [3, 3], [2, 4], [1, 5], [0, 6], [0, 7], [1, 6], [2, 5], [3, 4],
      [4, 3], [5, 2], [6, 1], [7, 0], [7, 1], [6, 2], [5, 3], [4, 4],
      [3, 5], [2, 6], [1, 7], [2, 7], [3, 6], [4, 5], [5, 4], [6, 3],
      [7, 2], [7, 3], [6, 4], [5, 5], [4, 6], [3, 7], [4, 7], [5, 6],
      [6, 5], [7, 4], [7, 5], [6, 6], [5, 7], [6, 7], [7, 6], [7, 7],
    ];
  }
  
  /// Tính encoding statistics
  Map<String, dynamic> _calculateEncodingStatistics(
    List<BlockInfo> blocks,
    Uint8List encodedData,
  ) {
    final stats = <String, dynamic>{
      'totalBlocks': blocks.length,
      'encodedSize': encodedData.length,
      'averageBlockSize': blocks.isNotEmpty ? encodedData.length / blocks.length : 0,
    };
    
    // Phân tích distribution của quantized coefficients
    final coefficients = <int>[];
    for (final block in blocks) {
      if (block.quantizedData != null) {
        for (final row in block.quantizedData!) {
          coefficients.addAll(row);
        }
      }
    }
    
    if (coefficients.isNotEmpty) {
      stats['totalCoefficients'] = coefficients.length;
      stats['zeroCoefficients'] = coefficients.where((c) => c == 0).length;
      stats['nonZeroCoefficients'] = coefficients.where((c) => c != 0).length;
      stats['zeroRatio'] = stats['zeroCoefficients'] / stats['totalCoefficients'];
      stats['averageCoefficient'] = MathUtils.mean(coefficients);
      stats['coefficientStdDev'] = MathUtils.standardDeviation(coefficients);
    }
    
    return stats;
  }
}

/// Utility functions
int _bytesToInt(Uint8List bytes, {bool signed = false}) {
  int result = 0;
  for (int i = 0; i < bytes.length; i++) {
    result = (result << 8) | bytes[i];
  }
  
  if (signed && bytes.isNotEmpty) {
    final signBit = 1 << (bytes.length * 8 - 1);
    if (result & signBit != 0) {
      result -= 1 << (bytes.length * 8);
    }
  }
  
  return result;
}

Uint8List _intToBytes(int value, int byteCount, {bool signed = false}) {
  final bytes = Uint8List(byteCount);
  
  if (signed && value < 0) {
    value += 1 << (byteCount * 8);
  }
  
  for (int i = byteCount - 1; i >= 0; i--) {
    bytes[i] = value & 0xFF;
    value >>= 8;
  }
  
  return bytes;
}
