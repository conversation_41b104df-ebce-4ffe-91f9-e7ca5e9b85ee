/// Batch processing engine cho DCT compression
/// 
/// File này implement x<PERSON> lý hàng loạt nhiều ảnh với progress tracking,
/// cancellation support, và memory management hiệu quả.
library;

import 'dart:async';
import 'dart:collection' show Queue;
import 'dart:typed_data';
import 'dart:isolate';
import 'dart:math' as math;
import '../utils/constants.dart';
import '../utils/memory_manager.dart';
import '../utils/validation.dart';
import '../image/pixel_data.dart';
import '../isolates/compression_isolate.dart';
import 'compression_options.dart';
import 'compression_result.dart';
import 'compressor.dart';

/// Callback cho progress updates
typedef BatchProgressCallback = void Function(int completed, int total, String? currentItem);

/// Callback cho individual item completion
typedef ItemCompletionCallback = void Function(int index, CompressionResult result);

/// Callback cho errors
typedef BatchErrorCallback = void Function(int index, String error);

/// Token để cancel batch operation
class CancellationToken {
  bool _isCancelled = false;
  final Completer<void> _completer = Completer<void>();
  
  /// C<PERSON> bị cancel không
  bool get isCancelled => _isCancelled;
  
  /// Future hoàn thành khi bị cancel
  Future<void> get cancelled => _completer.future;
  
  /// Cancel operation
  void cancel() {
    if (!_isCancelled) {
      _isCancelled = true;
      _completer.complete();
    }
  }
}

/// Kết quả batch processing
class BatchProcessingResult {
  /// Danh sách kết quả (null nếu item bị lỗi hoặc cancel)
  final List<CompressionResult?> results;
  
  /// Số lượng thành công
  final int successCount;
  
  /// Số lượng thất bại
  final int failureCount;
  
  /// Số lượng bị cancel
  final int cancelledCount;
  
  /// Thời gian xử lý tổng
  final Duration totalProcessingTime;
  
  /// Có bị cancel không
  final bool wasCancelled;
  
  /// Danh sách lỗi (index -> error message)
  final Map<int, String> errors;
  
  const BatchProcessingResult({
    required this.results,
    required this.successCount,
    required this.failureCount,
    required this.cancelledCount,
    required this.totalProcessingTime,
    required this.wasCancelled,
    required this.errors,
  });
  
  /// Tổng số items
  int get totalCount => results.length;
  
  /// Tỷ lệ thành công
  double get successRate => totalCount > 0 ? successCount / totalCount : 0.0;
  
  /// Có thành công hoàn toàn không
  bool get isCompleteSuccess => successCount == totalCount && !wasCancelled;
}

/// Batch processing engine
class BatchProcessor {
  final MemoryManager _memoryManager = MemoryManager();
  final DctCompressor _compressor = DctCompressor();
  
  /// Xử lý batch compression với isolate
  /// 
  /// [inputs]: Danh sách input (File, String path, hoặc Uint8List)
  /// [options]: Tùy chọn compression
  /// [onProgress]: Callback progress
  /// [onItemComplete]: Callback khi hoàn thành từng item
  /// [onError]: Callback khi có lỗi
  /// [cancellationToken]: Token để cancel
  /// [maxConcurrency]: Số lượng tác vụ đồng thời tối đa
  /// Returns: Kết quả batch processing
  Future<BatchProcessingResult> processBatch(
    List<dynamic> inputs, {
    CompressionOptions? options,
    BatchProgressCallback? onProgress,
    ItemCompletionCallback? onItemComplete,
    BatchErrorCallback? onError,
    CancellationToken? cancellationToken,
    int maxConcurrency = 3,
  }) async {
    options ??= const CompressionOptions();
    cancellationToken ??= CancellationToken();
    
    // Validate inputs
    _validateBatchInputs(inputs);
    
    final stopwatch = Stopwatch()..start();
    final results = List<CompressionResult?>.filled(inputs.length, null);
    final errors = <int, String>{};
    
    int completed = 0;
    int successCount = 0;
    int failureCount = 0;
    int cancelledCount = 0;
    
    try {
      // Initialize isolate pool nếu sử dụng isolate
      if (options.useIsolate) {
        await CompressionIsolate.initialize();
      }
      
      // Process items với concurrency control
      await _processWithConcurrencyControl(
        inputs,
        options,
        maxConcurrency,
        cancellationToken,
        (index, result, error) {
          if (cancellationToken!.isCancelled) {
            cancelledCount++;
            return;
          }
          
          if (error != null) {
            errors[index] = error;
            failureCount++;
            onError?.call(index, error);
          } else if (result != null) {
            results[index] = result;
            successCount++;
            onItemComplete?.call(index, result);
          }
          
          completed++;
          onProgress?.call(completed, inputs.length, 'Item ${index + 1}');
        },
      );
      
    } catch (e) {
      // Handle unexpected errors
      for (int i = completed; i < inputs.length; i++) {
        if (results[i] == null) {
          errors[i] = 'Batch processing interrupted: $e';
          failureCount++;
        }
      }
    } finally {
      stopwatch.stop();
      
      // Cleanup
      _memoryManager.autoCleanupIfNeeded();
    }
    
    return BatchProcessingResult(
      results: results,
      successCount: successCount,
      failureCount: failureCount,
      cancelledCount: cancelledCount,
      totalProcessingTime: stopwatch.elapsed,
      wasCancelled: cancellationToken.isCancelled,
      errors: errors,
    );
  }
  
  /// Xử lý với concurrency control
  Future<void> _processWithConcurrencyControl(
    List<dynamic> inputs,
    CompressionOptions options,
    int maxConcurrency,
    CancellationToken cancellationToken,
    void Function(int index, CompressionResult? result, String? error) onItemComplete,
  ) async {
    final semaphore = Semaphore(maxConcurrency);
    final futures = <Future<void>>[];
    
    for (int i = 0; i < inputs.length; i++) {
      if (cancellationToken.isCancelled) break;
      
      final future = semaphore.acquire().then((_) async {
        try {
          if (cancellationToken.isCancelled) {
            onItemComplete(i, null, 'Cancelled');
            return;
          }
          
          // Check memory before processing
          if (!_memoryManager.canAllocateForImage(800, 600, 3)) { // Default size check
            onItemComplete(i, null, 'Insufficient memory');
            return;
          }
          
          // Process single item
          final result = await _processSingleItem(inputs[i], options, cancellationToken);
          onItemComplete(i, result, null);
          
        } catch (e) {
          onItemComplete(i, null, e.toString());
        } finally {
          semaphore.release();
        }
      });
      
      futures.add(future);
    }
    
    // Wait for all tasks to complete or cancellation
    await Future.wait([
      Future.wait(futures),
      cancellationToken.cancelled,
    ].where((f) => f != cancellationToken.cancelled || !cancellationToken.isCancelled));
  }
  
  /// Xử lý một item đơn lẻ
  Future<CompressionResult> _processSingleItem(
    dynamic input,
    CompressionOptions options,
    CancellationToken cancellationToken,
  ) async {
    if (options.useIsolate) {
      return await _processWithIsolate(input, options, cancellationToken);
    } else {
      return await _processInMainThread(input, options, cancellationToken);
    }
  }
  
  /// Xử lý với isolate
  Future<CompressionResult> _processWithIsolate(
    dynamic input,
    CompressionOptions options,
    CancellationToken cancellationToken,
  ) async {
    final completer = Completer<CompressionResult>();
    final taskId = 'batch_${DateTime.now().millisecondsSinceEpoch}_${math.Random().nextInt(10000)}';
    
    // Convert input to bytes
    Uint8List inputBytes;
    if (input is Uint8List) {
      inputBytes = input;
    } else if (input is String) {
      // For isolate processing, we need the original bytes, so read file directly
      throw ArgumentError('String path input not supported for isolate processing yet');
    } else {
      throw ArgumentError('Unsupported input type: ${input.runtimeType}');
    }
    
    // Submit to isolate
    await CompressionIsolate.submitCompressionTask(
      taskId,
      inputBytes,
      options,
      onResult: (result) {
        if (!completer.isCompleted) {
          completer.complete(result);
        }
      },
      onError: (error) {
        if (!completer.isCompleted) {
          completer.completeError(error);
        }
      },
    );
    
    // Wait for result or cancellation
    final result = await Future.any([
      completer.future,
      cancellationToken.cancelled.then((_) => throw 'Cancelled'),
    ]);
    
    return result;
  }
  
  /// Xử lý trong main thread
  Future<CompressionResult> _processInMainThread(
    dynamic input,
    CompressionOptions options,
    CancellationToken cancellationToken,
  ) async {
    // Check cancellation periodically
    if (cancellationToken.isCancelled) {
      throw 'Operation was cancelled';
    }
    
    return await _compressor.compressImage(input, options: options);
  }
  
  /// Estimate total processing time
  Future<Duration> estimateBatchProcessingTime(
    List<dynamic> inputs,
    CompressionOptions options,
  ) async {
    if (inputs.isEmpty) return Duration.zero;
    
    // Sample first few items để estimate
    final sampleSize = math.min(3, inputs.length);
    final sampleTimes = <Duration>[];
    
    for (int i = 0; i < sampleSize; i++) {
      final stopwatch = Stopwatch()..start();
      try {
        await _compressor.compressImage(inputs[i], options: options);
        stopwatch.stop();
        sampleTimes.add(stopwatch.elapsed);
      } catch (e) {
        stopwatch.stop();
        // Use average time for failed samples
        if (sampleTimes.isNotEmpty) {
          sampleTimes.add(sampleTimes.reduce((a, b) => a + b) ~/ sampleTimes.length);
        } else {
          sampleTimes.add(const Duration(seconds: 5)); // Default estimate
        }
      }
    }
    
    // Calculate average time per item
    final averageTime = sampleTimes.reduce((a, b) => a + b) ~/ sampleTimes.length;
    
    // Estimate total time với concurrency factor
    final concurrencyFactor = options.useIsolate ? 0.4 : 1.0; // Isolate processing is faster
    final estimatedTotal = averageTime * inputs.length * concurrencyFactor;
    
    return Duration(microseconds: estimatedTotal.inMicroseconds);
  }

  /// Validate batch inputs
  void _validateBatchInputs(List<dynamic> inputs) {
    if (inputs.isEmpty) {
      throw ArgumentError('Batch inputs cannot be empty');
    }

    for (int i = 0; i < inputs.length; i++) {
      final input = inputs[i];
      if (input is! String && input is! Uint8List && input is! PixelData) {
        throw ArgumentError('Invalid input type at index $i: ${input.runtimeType}');
      }
    }
  }
}

/// Semaphore để control concurrency
class Semaphore {
  final int maxCount;
  int _currentCount;
  final Queue<Completer<void>> _waitQueue = Queue<Completer<void>>();
  
  Semaphore(this.maxCount) : _currentCount = maxCount;
  
  Future<void> acquire() async {
    if (_currentCount > 0) {
      _currentCount--;
      return;
    }
    
    final completer = Completer<void>();
    _waitQueue.add(completer);
    return completer.future;
  }
  
  void release() {
    if (_waitQueue.isNotEmpty) {
      final completer = _waitQueue.removeFirst();
      completer.complete();
    } else {
      _currentCount++;
    }
  }
}


