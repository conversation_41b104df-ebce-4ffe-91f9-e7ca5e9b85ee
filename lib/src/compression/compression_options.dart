import 'dart:typed_data';

/// Enum định nghĩa các không gian màu hỗ trợ
enum ColorSpace {
  /// RGB color space - Red, Green, Blue
  rgb,
  
  /// YUV color space - Luminance, Chrominance U, Chrominance V
  /// Thường được sử dụng trong JPEG compression
  yuv,
}

/// Enum định nghĩa các định dạng output hỗ trợ
enum OutputFormat {
  /// JPEG format với DCT compression
  jpeg,
  
  /// PNG format với DCT compression applied
  png,
}

/// Enum định nghĩa các input format hỗ trợ
enum InputFormat {
  /// JPEG format
  jpeg,
  
  /// PNG format
  png,
  
  /// BMP format
  bmp,
  
  /// TIFF format
  tiff,
  
  /// WebP format
  webp,
  
  /// Auto-detect format từ file header
  auto,
}

/// Callback function cho progress tracking
/// [completed]: Số lượng items đã hoàn thành
/// [total]: Tổng số items cần xử lý
/// [currentItem]: Tên hoặc ID của item hiện tại đang xử lý
typedef ProgressCallback = void Function(int completed, int total, String currentItem);

/// Token để hủy tác vụ compression đang chạy
class CancellationToken {
  bool _isCancelled = false;
  
  /// Kiểm tra xem tác vụ có bị hủy không
  bool get isCancelled => _isCancelled;
  
  /// Hủy tác vụ
  void cancel() {
    _isCancelled = true;
  }
  
  /// Reset token để sử dụng lại
  void reset() {
    _isCancelled = false;
  }
}

/// Lớp chứa các tùy chọn nén nâng cao
class CompressionOptions {
  /// Chất lượng nén từ 1-100 (100 = chất lượng cao nhất, ít nén nhất)
  final int quality;
  
  /// Tỷ lệ nén tối thiểu (0.1 = 10% kích thước gốc)
  /// null = không giới hạn
  final double? minCompressionRatio;
  
  /// Tỷ lệ nén tối đa (0.9 = 90% kích thước gốc)
  /// null = không giới hạn
  final double? maxCompressionRatio;
  
  /// Có giữ lại metadata EXIF không
  final bool preserveMetadata;
  
  /// Không gian màu sử dụng
  final ColorSpace colorSpace;
  
  /// Định dạng output
  final OutputFormat outputFormat;
  
  /// Bảng lượng tử hóa tùy chỉnh cho luminance channel
  /// null = sử dụng bảng chuẩn
  final List<List<int>>? customLuminanceQuantizationTable;
  
  /// Bảng lượng tử hóa tùy chỉnh cho chrominance channels
  /// null = sử dụng bảng chuẩn
  final List<List<int>>? customChrominanceQuantizationTable;
  
  /// Có sử dụng isolate cho xử lý nền không
  final bool useIsolate;
  
  /// Có sử dụng progressive encoding không (cho JPEG)
  final bool progressive;
  
  /// Có sử dụng adaptive quantization không
  /// Điều chỉnh quantization dựa trên nội dung ảnh
  final bool useAdaptiveQuantization;
  
  /// Kích thước tối đa của ảnh để xử lý trong memory
  /// Ảnh lớn hơn sẽ được xử lý theo chunks
  final int maxImageSizeInMemory;
  
  /// Constructor với các giá trị mặc định hợp lý
  const CompressionOptions({
    this.quality = 85,
    this.minCompressionRatio,
    this.maxCompressionRatio,
    this.preserveMetadata = false,
    this.colorSpace = ColorSpace.yuv,
    this.outputFormat = OutputFormat.jpeg,
    this.customLuminanceQuantizationTable,
    this.customChrominanceQuantizationTable,
    this.useIsolate = true,
    this.progressive = false,
    this.useAdaptiveQuantization = true,
    this.maxImageSizeInMemory = 50 * 1024 * 1024, // 50MB
  });
  
  /// Tạo CompressionOptions với chỉ quality
  factory CompressionOptions.quality(int quality) {
    return CompressionOptions(quality: quality);
  }
  
  /// Tạo CompressionOptions với compression ratio bounds
  factory CompressionOptions.withBounds({
    required double minRatio,
    required double maxRatio,
    int quality = 85,
  }) {
    assert(minRatio >= 0.1 && minRatio <= 0.9, 'Min ratio phải trong khoảng 0.1-0.9');
    assert(maxRatio >= 0.1 && maxRatio <= 0.9, 'Max ratio phải trong khoảng 0.1-0.9');
    assert(minRatio <= maxRatio, 'Min ratio phải <= max ratio');
    
    return CompressionOptions(
      quality: quality,
      minCompressionRatio: minRatio,
      maxCompressionRatio: maxRatio,
    );
  }
  
  /// Tạo CompressionOptions cho high quality (ít nén)
  factory CompressionOptions.highQuality() {
    return const CompressionOptions(
      quality: 95,
      colorSpace: ColorSpace.rgb,
      useAdaptiveQuantization: false,
    );
  }
  
  /// Tạo CompressionOptions cho high compression (nén nhiều)
  factory CompressionOptions.highCompression() {
    return const CompressionOptions(
      quality: 50,
      colorSpace: ColorSpace.yuv,
      useAdaptiveQuantization: true,
      progressive: true,
    );
  }
  
  /// Tạo CompressionOptions cho web optimization
  factory CompressionOptions.webOptimized() {
    return const CompressionOptions(
      quality: 75,
      colorSpace: ColorSpace.yuv,
      outputFormat: OutputFormat.jpeg,
      progressive: true,
      preserveMetadata: false,
    );
  }
  
  /// Copy với các thay đổi
  CompressionOptions copyWith({
    int? quality,
    double? minCompressionRatio,
    double? maxCompressionRatio,
    bool? preserveMetadata,
    ColorSpace? colorSpace,
    OutputFormat? outputFormat,
    List<List<int>>? customLuminanceQuantizationTable,
    List<List<int>>? customChrominanceQuantizationTable,
    bool? useIsolate,
    bool? progressive,
    bool? useAdaptiveQuantization,
    int? maxImageSizeInMemory,
  }) {
    return CompressionOptions(
      quality: quality ?? this.quality,
      minCompressionRatio: minCompressionRatio ?? this.minCompressionRatio,
      maxCompressionRatio: maxCompressionRatio ?? this.maxCompressionRatio,
      preserveMetadata: preserveMetadata ?? this.preserveMetadata,
      colorSpace: colorSpace ?? this.colorSpace,
      outputFormat: outputFormat ?? this.outputFormat,
      customLuminanceQuantizationTable: customLuminanceQuantizationTable ?? this.customLuminanceQuantizationTable,
      customChrominanceQuantizationTable: customChrominanceQuantizationTable ?? this.customChrominanceQuantizationTable,
      useIsolate: useIsolate ?? this.useIsolate,
      progressive: progressive ?? this.progressive,
      useAdaptiveQuantization: useAdaptiveQuantization ?? this.useAdaptiveQuantization,
      maxImageSizeInMemory: maxImageSizeInMemory ?? this.maxImageSizeInMemory,
    );
  }
  
  /// Validate các tùy chọn
  bool validate() {
    // Kiểm tra quality
    if (quality < 1 || quality > 100) return false;
    
    // Kiểm tra compression ratios
    if (minCompressionRatio != null) {
      if (minCompressionRatio! < 0.1 || minCompressionRatio! > 0.9) return false;
    }
    
    if (maxCompressionRatio != null) {
      if (maxCompressionRatio! < 0.1 || maxCompressionRatio! > 0.9) return false;
    }
    
    if (minCompressionRatio != null && maxCompressionRatio != null) {
      if (minCompressionRatio! > maxCompressionRatio!) return false;
    }
    
    // Kiểm tra custom quantization tables
    if (customLuminanceQuantizationTable != null) {
      if (!_validateQuantizationTable(customLuminanceQuantizationTable!)) return false;
    }
    
    if (customChrominanceQuantizationTable != null) {
      if (!_validateQuantizationTable(customChrominanceQuantizationTable!)) return false;
    }
    
    // Kiểm tra max image size
    if (maxImageSizeInMemory <= 0) return false;
    
    return true;
  }
  
  /// Validate quantization table
  bool _validateQuantizationTable(List<List<int>> table) {
    if (table.length != 8) return false;
    
    for (final row in table) {
      if (row.length != 8) return false;
      for (final value in row) {
        if (value < 1 || value > 255) return false;
      }
    }
    
    return true;
  }
  
  @override
  String toString() {
    return 'CompressionOptions('
        'quality: $quality, '
        'minRatio: $minCompressionRatio, '
        'maxRatio: $maxCompressionRatio, '
        'colorSpace: $colorSpace, '
        'outputFormat: $outputFormat, '
        'useIsolate: $useIsolate, '
        'progressive: $progressive, '
        'adaptive: $useAdaptiveQuantization'
        ')';
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is CompressionOptions &&
        other.quality == quality &&
        other.minCompressionRatio == minCompressionRatio &&
        other.maxCompressionRatio == maxCompressionRatio &&
        other.preserveMetadata == preserveMetadata &&
        other.colorSpace == colorSpace &&
        other.outputFormat == outputFormat &&
        other.useIsolate == useIsolate &&
        other.progressive == progressive &&
        other.useAdaptiveQuantization == useAdaptiveQuantization &&
        other.maxImageSizeInMemory == maxImageSizeInMemory;
  }
  
  @override
  int get hashCode {
    return Object.hash(
      quality,
      minCompressionRatio,
      maxCompressionRatio,
      preserveMetadata,
      colorSpace,
      outputFormat,
      useIsolate,
      progressive,
      useAdaptiveQuantization,
      maxImageSizeInMemory,
    );
  }

  /// Chuyển đổi sang Map để serialize
  Map<String, dynamic> toMap() {
    return {
      'quality': quality,
      'minCompressionRatio': minCompressionRatio,
      'maxCompressionRatio': maxCompressionRatio,
      'preserveMetadata': preserveMetadata,
      'colorSpace': colorSpace.name,
      'outputFormat': outputFormat.name,
      'customLuminanceQuantizationTable': customLuminanceQuantizationTable,
      'customChrominanceQuantizationTable': customChrominanceQuantizationTable,
      'useIsolate': useIsolate,
      'progressive': progressive,
      'useAdaptiveQuantization': useAdaptiveQuantization,
      'maxImageSizeInMemory': maxImageSizeInMemory,
    };
  }

  /// Tạo CompressionOptions từ Map
  factory CompressionOptions.fromMap(Map<String, dynamic> map) {
    return CompressionOptions(
      quality: map['quality'] ?? 85,
      minCompressionRatio: map['minCompressionRatio'],
      maxCompressionRatio: map['maxCompressionRatio'],
      preserveMetadata: map['preserveMetadata'] ?? false,
      colorSpace: ColorSpace.values.firstWhere(
        (e) => e.name == map['colorSpace'],
        orElse: () => ColorSpace.yuv,
      ),
      outputFormat: OutputFormat.values.firstWhere(
        (e) => e.name == map['outputFormat'],
        orElse: () => OutputFormat.jpeg,
      ),
      customLuminanceQuantizationTable: map['customLuminanceQuantizationTable']?.cast<List<int>>(),
      customChrominanceQuantizationTable: map['customChrominanceQuantizationTable']?.cast<List<int>>(),
      useIsolate: map['useIsolate'] ?? true,
      progressive: map['progressive'] ?? false,
      useAdaptiveQuantization: map['useAdaptiveQuantization'] ?? true,
      maxImageSizeInMemory: map['maxImageSizeInMemory'] ?? 50 * 1024 * 1024,
    );
  }
}
