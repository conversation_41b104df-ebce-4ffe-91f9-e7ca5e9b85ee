/// <PERSON><PERSON><PERSON> soát giới hạn tỷ lệ nén cho DCT compression
/// 
/// File này chứa logic để đảm bảo kết quả nén nằm trong
/// khoảng tỷ lệ nén mong muốn (min/max compression ratio).
library;

import 'dart:math' as math;
import '../utils/constants.dart';
import '../utils/math_utils.dart';
import '../dct/quantization.dart';
import 'compression_options.dart';

/// Kết quả kiểm tra bounds
class BoundsCheckResult {
  final bool withinBounds;
  final double actualRatio;
  final double? targetMinRatio;
  final double? targetMaxRatio;
  final String? adjustmentNeeded;
  
  const BoundsCheckResult({
    required this.withinBounds,
    required this.actualRatio,
    this.targetMinRatio,
    this.targetMaxRatio,
    this.adjustmentNeeded,
  });
  
  @override
  String toString() {
    return 'BoundsCheckResult(withinBounds: $withinBounds, '
           'actualRatio: ${actualRatio.toStringAsFixed(3)}, '
           'target: ${targetMinRatio?.toStringAsFixed(3)}-${targetMaxRatio?.toStringAsFixed(3)})';
  }
}

/// Thông tin điều chỉnh quality
class QualityAdjustment {
  final int originalQuality;
  final int adjustedQuality;
  final double expectedRatio;
  final String reason;
  
  const QualityAdjustment({
    required this.originalQuality,
    required this.adjustedQuality,
    required this.expectedRatio,
    required this.reason,
  });
  
  @override
  String toString() {
    return 'QualityAdjustment(${originalQuality} -> $adjustedQuality, '
           'expectedRatio: ${expectedRatio.toStringAsFixed(3)}, reason: $reason)';
  }
}

/// Kiểm soát giới hạn compression
class CompressionBounds {
  /// Kiểm tra compression ratio có nằm trong bounds không
  static BoundsCheckResult checkBounds(
    int originalSize,
    int compressedSize,
    CompressionOptions options,
  ) {
    final actualRatio = MathUtils.calculateCompressionRatio(originalSize, compressedSize);
    
    final minRatio = options.minCompressionRatio;
    final maxRatio = options.maxCompressionRatio;
    
    // Nếu không có bounds thì luôn pass
    if (minRatio == null && maxRatio == null) {
      return BoundsCheckResult(
        withinBounds: true,
        actualRatio: actualRatio,
      );
    }
    
    bool withinBounds = true;
    String? adjustmentNeeded;
    
    // Kiểm tra min bound
    if (minRatio != null && actualRatio < minRatio) {
      withinBounds = false;
      adjustmentNeeded = 'Cần tăng compression ratio (giảm quality)';
    }
    
    // Kiểm tra max bound
    if (maxRatio != null && actualRatio > maxRatio) {
      withinBounds = false;
      adjustmentNeeded = 'Cần giảm compression ratio (tăng quality)';
    }
    
    return BoundsCheckResult(
      withinBounds: withinBounds,
      actualRatio: actualRatio,
      targetMinRatio: minRatio,
      targetMaxRatio: maxRatio,
      adjustmentNeeded: adjustmentNeeded,
    );
  }
  
  /// Ước tính quality cần thiết để đạt target compression ratio
  static int estimateQualityForRatio(double targetRatio, {
    double imageComplexity = 0.5,
    bool isLuminance = true,
  }) {
    // Công thức empirical dựa trên mối quan hệ quality-compression ratio
    // Ảnh phức tạp hơn cần quality cao hơn để đạt cùng compression ratio
    
    double baseQuality;
    if (targetRatio < 0.1) {
      // Compression rất cao
      baseQuality = 10 + (targetRatio * 200);
    } else if (targetRatio < 0.3) {
      // Compression cao
      baseQuality = 30 + ((targetRatio - 0.1) * 150);
    } else if (targetRatio < 0.7) {
      // Compression trung bình
      baseQuality = 60 + ((targetRatio - 0.3) * 75);
    } else {
      // Compression thấp
      baseQuality = 90 + ((targetRatio - 0.7) * 33);
    }
    
    // Điều chỉnh theo độ phức tạp ảnh
    final complexityAdjustment = imageComplexity * 15; // Tối đa +15 quality
    final adjustedQuality = baseQuality + complexityAdjustment;
    
    return MathUtils.clamp(adjustedQuality.round(), 1, 100);
  }
  
  /// Điều chỉnh quality để fit vào bounds
  static QualityAdjustment adjustQualityForBounds(
    int originalQuality,
    BoundsCheckResult boundsCheck,
    double imageComplexity,
  ) {
    if (boundsCheck.withinBounds) {
      return QualityAdjustment(
        originalQuality: originalQuality,
        adjustedQuality: originalQuality,
        expectedRatio: boundsCheck.actualRatio,
        reason: 'Đã nằm trong bounds',
      );
    }
    
    int adjustedQuality = originalQuality;
    double targetRatio = boundsCheck.actualRatio;
    String reason = '';
    
    // Nếu compression ratio quá thấp (file quá lớn)
    if (boundsCheck.targetMinRatio != null && 
        boundsCheck.actualRatio < boundsCheck.targetMinRatio!) {
      targetRatio = boundsCheck.targetMinRatio!;
      adjustedQuality = estimateQualityForRatio(targetRatio, imageComplexity: imageComplexity);
      
      // Đảm bảo quality giảm để tăng compression
      adjustedQuality = math.min(adjustedQuality, originalQuality - 5);
      reason = 'Giảm quality để tăng compression ratio';
    }
    
    // Nếu compression ratio quá cao (file quá nhỏ, chất lượng kém)
    if (boundsCheck.targetMaxRatio != null && 
        boundsCheck.actualRatio > boundsCheck.targetMaxRatio!) {
      targetRatio = boundsCheck.targetMaxRatio!;
      adjustedQuality = estimateQualityForRatio(targetRatio, imageComplexity: imageComplexity);
      
      // Đảm bảo quality tăng để giảm compression
      adjustedQuality = math.max(adjustedQuality, originalQuality + 5);
      reason = 'Tăng quality để giảm compression ratio';
    }
    
    // Clamp quality trong khoảng hợp lệ
    adjustedQuality = MathUtils.clamp(adjustedQuality, 1, 100);
    
    return QualityAdjustment(
      originalQuality: originalQuality,
      adjustedQuality: adjustedQuality,
      expectedRatio: targetRatio,
      reason: reason,
    );
  }
  
  /// Tạo quantization table được điều chỉnh để đạt target ratio
  static List<List<int>> createBoundedQuantizationTable(
    int quality,
    double targetRatio,
    double imageComplexity, {
    bool isLuminance = true,
  }) {
    // Tạo base quantization table
    var quantTable = Quantization.createQuantizationTable(quality, isLuminance: isLuminance);
    
    // Điều chỉnh table dựa trên target ratio
    final scaleFactor = _calculateScaleFactorForRatio(targetRatio, imageComplexity);
    
    // Apply scale factor
    quantTable = quantTable.map((row) => 
      row.map((value) => MathUtils.clamp((value * scaleFactor).round(), 1, 255)).toList()
    ).toList();
    
    return quantTable;
  }
  
  /// Tính scale factor cho quantization table dựa trên target ratio
  static double _calculateScaleFactorForRatio(double targetRatio, double imageComplexity) {
    // Scale factor để điều chỉnh quantization table
    // Ratio thấp (compression cao) -> scale factor cao (quantization mạnh hơn)
    // Ratio cao (compression thấp) -> scale factor thấp (quantization nhẹ hơn)
    
    double baseFactor;
    if (targetRatio < 0.2) {
      baseFactor = 2.0 + (0.2 - targetRatio) * 5; // 2.0 - 3.0
    } else if (targetRatio < 0.5) {
      baseFactor = 1.0 + (0.5 - targetRatio) * 3.33; // 1.0 - 2.0
    } else if (targetRatio < 0.8) {
      baseFactor = 0.5 + (0.8 - targetRatio) * 1.67; // 0.5 - 1.0
    } else {
      baseFactor = 0.3 + (1.0 - targetRatio) * 1.0; // 0.3 - 0.5
    }
    
    // Điều chỉnh theo độ phức tạp ảnh
    // Ảnh phức tạp cần quantization nhẹ hơn để giữ chất lượng
    final complexityAdjustment = 1.0 - (imageComplexity * 0.3);
    
    return baseFactor * complexityAdjustment;
  }
  
  /// Iterative adjustment để đạt chính xác target ratio
  static List<List<int>> iterativeQuantizationAdjustment(
    List<List<int>> baseQuantTable,
    double currentRatio,
    double targetRatio,
    int maxIterations,
  ) {
    var quantTable = baseQuantTable.map((row) => List<int>.from(row)).toList();
    
    for (int iteration = 0; iteration < maxIterations; iteration++) {
      final ratioDiff = currentRatio - targetRatio;
      
      // Nếu đã đủ gần target thì dừng
      if (ratioDiff.abs() < 0.05) break;
      
      // Tính adjustment factor
      double adjustmentFactor;
      if (ratioDiff > 0) {
        // Current ratio cao hơn target -> cần compression nhiều hơn
        adjustmentFactor = 1.0 + (ratioDiff * 0.5);
      } else {
        // Current ratio thấp hơn target -> cần compression ít hơn
        adjustmentFactor = 1.0 + (ratioDiff * 0.5);
      }
      
      // Apply adjustment
      quantTable = quantTable.map((row) => 
        row.map((value) => MathUtils.clamp((value * adjustmentFactor).round(), 1, 255)).toList()
      ).toList();
      
      // Update current ratio (ước tính)
      currentRatio = _estimateRatioFromQuantTable(quantTable);
    }
    
    return quantTable;
  }
  
  /// Ước tính compression ratio từ quantization table
  static double _estimateRatioFromQuantTable(List<List<int>> quantTable) {
    // Tính average quantization value
    double sum = 0;
    int count = 0;
    
    for (final row in quantTable) {
      for (final value in row) {
        sum += value;
        count++;
      }
    }
    
    final avgQuant = sum / count;
    
    // Empirical formula để ước tính compression ratio
    // Quantization cao -> compression ratio thấp
    final estimatedRatio = math.max(0.05, math.min(0.95, 1.0 - (avgQuant - 1) / 254));
    
    return estimatedRatio;
  }
  
  /// Validate bounds configuration
  static bool validateBoundsConfiguration(CompressionOptions options) {
    final minRatio = options.minCompressionRatio;
    final maxRatio = options.maxCompressionRatio;
    
    if (minRatio == null && maxRatio == null) return true;
    
    if (minRatio != null) {
      if (minRatio < CompressionConstants.minCompressionRatio || 
          minRatio > CompressionConstants.maxCompressionRatio) {
        return false;
      }
    }
    
    if (maxRatio != null) {
      if (maxRatio < CompressionConstants.minCompressionRatio || 
          maxRatio > CompressionConstants.maxCompressionRatio) {
        return false;
      }
    }
    
    if (minRatio != null && maxRatio != null) {
      if (minRatio > maxRatio) return false;
    }
    
    return true;
  }
  
  /// Tính recommended bounds dựa trên image characteristics
  static Map<String, double> recommendBounds(
    int imageWidth,
    int imageHeight,
    int channels,
    double imageComplexity,
  ) {
    final totalPixels = imageWidth * imageHeight;
    
    // Ảnh lớn có thể compress nhiều hơn
    double sizeBasedMin = 0.1;
    double sizeBasedMax = 0.8;
    
    if (totalPixels > 2000000) { // > 2MP
      sizeBasedMin = 0.05;
      sizeBasedMax = 0.7;
    } else if (totalPixels < 500000) { // < 0.5MP
      sizeBasedMin = 0.2;
      sizeBasedMax = 0.9;
    }
    
    // Ảnh phức tạp cần compression ratio cao hơn để giữ chất lượng
    final complexityAdjustment = imageComplexity * 0.2;
    sizeBasedMin += complexityAdjustment;
    sizeBasedMax += complexityAdjustment;
    
    return {
      'recommendedMin': MathUtils.clamp(sizeBasedMin, 0.01, 0.99),
      'recommendedMax': MathUtils.clamp(sizeBasedMax, 0.01, 0.99),
    };
  }
}
