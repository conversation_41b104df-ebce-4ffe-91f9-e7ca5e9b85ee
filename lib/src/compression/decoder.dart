/// Decoder cho DCT compressed data
/// 
/// File này chứa logic để decode dữ liệu DCT compressed
/// trở lại thành pixel data.
library;

import 'dart:typed_data';
import 'dart:convert';
import '../utils/constants.dart';
import '../utils/math_utils.dart';
import '../image/pixel_data.dart';
import '../dct/block_processor.dart';
import '../dct/quantization.dart';
import '../dct/dct_transform.dart';
import 'encoder.dart';

/// Exception cho decoding errors
class DecodingException implements Exception {
  final String message;
  
  const DecodingException(this.message);
  
  @override
  String toString() => 'DecodingException: $message';
}

/// Kết quả decoding
class DecodingResult {
  final PixelData pixelData;
  final DctHeader header;
  final Duration decodingTime;
  final Map<String, dynamic> statistics;
  
  const DecodingResult({
    required this.pixelData,
    required this.header,
    required this.decodingTime,
    required this.statistics,
  });
  
  @override
  String toString() {
    return 'DecodingResult(${header.width}x${header.height}, '
           'channels: ${header.channels}, '
           'time: ${decodingTime.inMilliseconds}ms)';
  }
}

/// DCT Decoder
class DctDecoder {
  /// Decode compressed data thành PixelData
  DecodingResult decode(Uint8List compressedData) {
    final stopwatch = Stopwatch()..start();
    
    try {
      // Parse header
      final header = _parseHeader(compressedData);
      int offset = header.sizeInBytes;
      
      // Parse custom quantization tables nếu có
      List<List<int>>? customLuminanceTable;
      List<List<int>>? customChrominanceTable;
      
      if (header.hasCustomQuantTables) {
        final tablesResult = _parseQuantizationTables(compressedData, offset);
        customLuminanceTable = tablesResult['luminance'];
        customChrominanceTable = tablesResult['chrominance'];
        offset = tablesResult['offset'];
      }
      
      // Parse blocks data
      final blocks = _parseBlocksData(compressedData, offset, header);
      
      // Reconstruct pixel data
      final pixelData = _reconstructPixelData(
        blocks, 
        header, 
        customLuminanceTable, 
        customChrominanceTable,
      );
      
      stopwatch.stop();
      
      // Tính statistics
      final statistics = _calculateDecodingStatistics(blocks, pixelData);
      
      return DecodingResult(
        pixelData: pixelData,
        header: header,
        decodingTime: stopwatch.elapsed,
        statistics: statistics,
      );
    } catch (e) {
      throw DecodingException('Decode thất bại: $e');
    }
  }
  
  /// Parse header từ compressed data
  DctHeader _parseHeader(Uint8List data) {
    try {
      return DctHeader.fromBytes(data);
    } catch (e) {
      throw DecodingException('Parse header thất bại: $e');
    }
  }
  
  /// Parse custom quantization tables
  Map<String, dynamic> _parseQuantizationTables(Uint8List data, int offset) {
    if (offset >= data.length) {
      throw const DecodingException('Không đủ dữ liệu cho quantization tables');
    }
    
    List<List<int>>? luminanceTable;
    List<List<int>>? chrominanceTable;
    
    // Parse luminance table
    final hasLuminance = data[offset++] == 1;
    if (hasLuminance) {
      if (offset + 64 > data.length) {
        throw const DecodingException('Không đủ dữ liệu cho luminance table');
      }
      luminanceTable = _parseQuantizationTable(data.sublist(offset, offset + 64));
      offset += 64;
    }
    
    // Parse chrominance table
    final hasChrominance = data[offset++] == 1;
    if (hasChrominance) {
      if (offset + 64 > data.length) {
        throw const DecodingException('Không đủ dữ liệu cho chrominance table');
      }
      chrominanceTable = _parseQuantizationTable(data.sublist(offset, offset + 64));
      offset += 64;
    }
    
    return {
      'luminance': luminanceTable,
      'chrominance': chrominanceTable,
      'offset': offset,
    };
  }
  
  /// Parse một quantization table từ zigzag order
  List<List<int>> _parseQuantizationTable(Uint8List data) {
    if (data.length != 64) {
      throw const DecodingException('Quantization table phải có 64 values');
    }
    
    final table = List.generate(8, (_) => List<int>.filled(8, 0));
    final zigzagOrder = _getZigzagOrder();
    
    for (int i = 0; i < 64; i++) {
      final pos = zigzagOrder[i];
      table[pos[0]][pos[1]] = data[i];
    }
    
    return table;
  }
  
  /// Parse blocks data
  List<BlockInfo> _parseBlocksData(Uint8List data, int offset, DctHeader header) {
    final blocks = <BlockInfo>[];
    
    // Parse từng channel
    for (int channel = 0; channel < header.channels; channel++) {
      final channelResult = _parseChannelBlocks(data, offset, channel);
      blocks.addAll(channelResult['blocks']);
      offset = channelResult['offset'];
    }
    
    return blocks;
  }
  
  /// Parse blocks của một channel
  Map<String, dynamic> _parseChannelBlocks(Uint8List data, int offset, int channel) {
    if (offset + 4 > data.length) {
      throw const DecodingException('Không đủ dữ liệu cho channel blocks count');
    }
    
    // Parse số lượng blocks
    final blockCount = _bytesToInt(data.sublist(offset, offset + 4));
    offset += 4;
    
    final blocks = <BlockInfo>[];
    
    // Parse từng block
    for (int i = 0; i < blockCount; i++) {
      final blockResult = _parseBlock(data, offset, channel);
      blocks.add(blockResult['block']);
      offset = blockResult['offset'];
    }
    
    return {
      'blocks': blocks,
      'offset': offset,
    };
  }
  
  /// Parse một block
  Map<String, dynamic> _parseBlock(Uint8List data, int offset, int channel) {
    if (offset + 4 > data.length) {
      throw const DecodingException('Không đủ dữ liệu cho block position');
    }
    
    // Parse position
    final x = _bytesToInt(data.sublist(offset, offset + 2));
    final y = _bytesToInt(data.sublist(offset + 2, offset + 4));
    offset += 4;
    
    // Parse quantized data (run-length encoded)
    final rleResult = _parseRunLengthEncoded(data, offset);
    final quantizedValues = rleResult['values'];
    offset = rleResult['offset'];
    
    // Convert từ zigzag order về 8x8 matrix
    final quantizedData = _zigzagToMatrix(quantizedValues);
    
    // Tạo dummy pixel data (sẽ được reconstruct sau)
    final pixelData = List.generate(8, (_) => List<int>.filled(8, 0));
    
    final block = BlockInfo(
      x: x,
      y: y,
      channel: channel,
      pixelData: pixelData,
      quantizedData: quantizedData,
    );
    
    return {
      'block': block,
      'offset': offset,
    };
  }
  
  /// Parse run-length encoded data
  Map<String, dynamic> _parseRunLengthEncoded(Uint8List data, int offset) {
    final values = <int>[];
    
    while (values.length < 64 && offset < data.length) {
      if (offset + 3 > data.length) {
        throw const DecodingException('Không đủ dữ liệu cho RLE');
      }
      
      final count = data[offset++];
      final value = _bytesToInt(data.sublist(offset, offset + 2), signed: true);
      offset += 2;
      
      for (int i = 0; i < count; i++) {
        values.add(value);
      }
    }
    
    if (values.length != 64) {
      throw DecodingException('RLE data không đúng kích thước: ${values.length}');
    }
    
    return {
      'values': values,
      'offset': offset,
    };
  }
  
  /// Convert zigzag order values về 8x8 matrix
  List<List<int>> _zigzagToMatrix(List<int> values) {
    if (values.length != 64) {
      throw DecodingException('Zigzag values phải có 64 elements: ${values.length}');
    }
    
    final matrix = List.generate(8, (_) => List<int>.filled(8, 0));
    final zigzagOrder = _getZigzagOrder();
    
    for (int i = 0; i < 64; i++) {
      final pos = zigzagOrder[i];
      matrix[pos[0]][pos[1]] = values[i];
    }
    
    return matrix;
  }
  
  /// Reconstruct pixel data từ decoded blocks
  PixelData _reconstructPixelData(
    List<BlockInfo> blocks,
    DctHeader header,
    List<List<int>>? customLuminanceTable,
    List<List<int>>? customChrominanceTable,
  ) {
    final processor = BlockProcessor();
    final reconstructedChannels = <List<List<int>>>[];
    
    // Reconstruct từng channel
    for (int channel = 0; channel < header.channels; channel++) {
      final channelBlocks = blocks.where((b) => b.channel == channel).toList();
      
      // Chọn quantization table phù hợp
      List<List<int>> quantTable;
      if (channel == 0 && customLuminanceTable != null) {
        quantTable = customLuminanceTable;
      } else if (channel > 0 && customChrominanceTable != null) {
        quantTable = customChrominanceTable;
      } else {
        quantTable = Quantization.createQuantizationTable(
          header.quality,
          isLuminance: channel == 0,
        );
      }
      
      // Deprocess blocks (dequantization + IDCT)
      final deprocessedBlocks = <BlockInfo>[];
      for (final block in channelBlocks) {
        final deprocessed = processor.deprocessBlock(block, quantTable);
        deprocessedBlocks.add(deprocessed);
      }
      
      // Combine blocks thành channel data
      final channelData = processor.combineBlocks(
        deprocessedBlocks,
        header.width,
        header.height,
        channel,
      );
      
      reconstructedChannels.add(channelData);
    }
    
    // Combine channels thành PixelData
    return _combineChannelsToPixelData(reconstructedChannels, header);
  }
  
  /// Combine channels thành PixelData
  PixelData _combineChannelsToPixelData(
    List<List<List<int>>> channels,
    DctHeader header,
  ) {
    final info = ImageInfo(
      width: header.width,
      height: header.height,
      channels: header.channels,
      format: 'DCT',
      bitDepth: 8,
      hasAlpha: header.channels == 4,
      colorSpace: header.colorSpace,
      metadata: header.metadata,
    );
    
    final data = Uint8List(header.width * header.height * header.channels);
    int index = 0;
    
    for (int y = 0; y < header.height; y++) {
      for (int x = 0; x < header.width; x++) {
        for (int c = 0; c < header.channels; c++) {
          data[index++] = MathUtils.clamp(channels[c][y][x], 0, 255);
        }
      }
    }
    
    return PixelData.fromBytes(info, data);
  }
  
  /// Tính decoding statistics
  Map<String, dynamic> _calculateDecodingStatistics(
    List<BlockInfo> blocks,
    PixelData pixelData,
  ) {
    return {
      'totalBlocks': blocks.length,
      'reconstructedPixels': pixelData.info.totalPixels,
      'channels': pixelData.info.channels,
      'memoryUsage': pixelData.memoryUsage,
    };
  }
  
  /// Lấy zigzag order
  List<List<int>> _getZigzagOrder() {
    return [
      [0, 0], [0, 1], [1, 0], [2, 0], [1, 1], [0, 2], [0, 3], [1, 2],
      [2, 1], [3, 0], [4, 0], [3, 1], [2, 2], [1, 3], [0, 4], [0, 5],
      [1, 4], [2, 3], [3, 2], [4, 1], [5, 0], [6, 0], [5, 1], [4, 2],
      [3, 3], [2, 4], [1, 5], [0, 6], [0, 7], [1, 6], [2, 5], [3, 4],
      [4, 3], [5, 2], [6, 1], [7, 0], [7, 1], [6, 2], [5, 3], [4, 4],
      [3, 5], [2, 6], [1, 7], [2, 7], [3, 6], [4, 5], [5, 4], [6, 3],
      [7, 2], [7, 3], [6, 4], [5, 5], [4, 6], [3, 7], [4, 7], [5, 6],
      [6, 5], [7, 4], [7, 5], [6, 6], [5, 7], [6, 7], [7, 6], [7, 7],
    ];
  }
}

/// Utility functions
int _bytesToInt(Uint8List bytes, {bool signed = false}) {
  int result = 0;
  for (int i = 0; i < bytes.length; i++) {
    result = (result << 8) | bytes[i];
  }
  
  if (signed && bytes.isNotEmpty) {
    final signBit = 1 << (bytes.length * 8 - 1);
    if (result & signBit != 0) {
      result -= 1 << (bytes.length * 8);
    }
  }
  
  return result;
}
