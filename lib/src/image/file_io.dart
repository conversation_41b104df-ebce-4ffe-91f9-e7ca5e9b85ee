import 'dart:io';
import 'dart:typed_data';
import 'package:image/image.dart' as img;
import '../compression/compression_options.dart';
import '../compression/compression_result.dart';

/// Lớp xử lý input/output cho nhiều loại file và data sources
class FileIOHandler {
  /// Load dữ liệu từ nhiều nguồn khác nhau
  /// 
  /// [input]: <PERSON><PERSON> thể là File, String (path), hoặc Uint8List (bytes)
  /// Returns: Tuple (bytes, sourcePath)
  static Future<(Uint8List, String?)> loadInputData(dynamic input) async {
    if (input is File) {
      if (!await input.exists()) {
        throw FileIOException('File không tồn tại: ${input.path}');
      }
      final bytes = await input.readAsBytes();
      return (bytes, input.path);
    } else if (input is String) {
      final file = File(input);
      if (!await file.exists()) {
        throw FileIOException('File không tồn tại: $input');
      }
      final bytes = await file.readAsBytes();
      return (bytes, input);
    } else if (input is Uint8List) {
      return (input, null);
    } else {
      throw FileIOException('Input type không được hỗ trợ: ${input.runtimeType}');
    }
  }
  
  /// Load nhiều files cùng lúc
  /// 
  /// [inputs]: List các input (File, String, hoặc Uint8List)
  /// Returns: List các tuple (bytes, sourcePath)
  static Future<List<(Uint8List, String?)>> loadMultipleInputs(List<dynamic> inputs) async {
    final results = <(Uint8List, String?)>[];
    
    for (final input in inputs) {
      try {
        final result = await loadInputData(input);
        results.add(result);
      } catch (e) {
        // Log error nhưng tiếp tục với các files khác
        print('Lỗi khi load input: $e');
        // Có thể throw hoặc skip tùy theo yêu cầu
        rethrow;
      }
    }
    
    return results;
  }
  
  /// Validate file extension với format
  /// 
  /// [filePath]: Đường dẫn file
  /// [expectedFormat]: Format mong đợi
  /// Returns: true nếu extension phù hợp
  static bool validateFileExtension(String filePath, InputFormat expectedFormat) {
    final extension = _getFileExtension(filePath).toLowerCase();
    
    switch (expectedFormat) {
      case InputFormat.jpeg:
        return extension == '.jpg' || extension == '.jpeg';
      case InputFormat.png:
        return extension == '.png';
      case InputFormat.bmp:
        return extension == '.bmp';
      case InputFormat.tiff:
        return extension == '.tiff' || extension == '.tif';
      case InputFormat.webp:
        return extension == '.webp';
      case InputFormat.auto:
        return _isSupportedExtension(extension);
    }
  }
  
  /// Lấy file extension
  static String _getFileExtension(String filePath) {
    final lastDot = filePath.lastIndexOf('.');
    if (lastDot == -1) return '';
    return filePath.substring(lastDot);
  }
  
  /// Kiểm tra extension có được hỗ trợ không
  static bool _isSupportedExtension(String extension) {
    const supportedExtensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif', '.webp'};
    return supportedExtensions.contains(extension.toLowerCase());
  }
  
  /// Detect format từ file extension
  /// 
  /// [filePath]: Đường dẫn file
  /// Returns: InputFormat được detect
  static InputFormat detectFormatFromPath(String filePath) {
    final extension = _getFileExtension(filePath).toLowerCase();
    
    switch (extension) {
      case '.jpg':
      case '.jpeg':
        return InputFormat.jpeg;
      case '.png':
        return InputFormat.png;
      case '.bmp':
        return InputFormat.bmp;
      case '.tiff':
      case '.tif':
        return InputFormat.tiff;
      case '.webp':
        return InputFormat.webp;
      default:
        return InputFormat.auto;
    }
  }
  
  /// Lưu compressed data ra file
  /// 
  /// [result]: CompressionResult chứa data cần lưu
  /// [outputPath]: Đường dẫn output file
  /// [outputFormat]: Format để lưu (nếu khác với result format)
  static Future<void> saveToFile(
    CompressionResult result,
    String outputPath, {
    OutputFormat? outputFormat,
  }) async {
    if (!result.isSuccess) {
      throw FileIOException('Không thể lưu file: compression không thành công');
    }
    
    final format = outputFormat ?? _getOutputFormatFromPath(outputPath);
    Uint8List dataToSave;
    
    // Convert data nếu cần thiết
    if (format == OutputFormat.jpeg) {
      dataToSave = await _convertToJpeg(result);
    } else if (format == OutputFormat.png) {
      dataToSave = await _convertToPng(result);
    } else {
      // Sử dụng compressed data trực tiếp
      dataToSave = result.compressedData;
    }
    
    // Tạo directory nếu chưa tồn tại
    final file = File(outputPath);
    final directory = file.parent;
    if (!await directory.exists()) {
      await directory.create(recursive: true);
    }
    
    // Ghi file
    await file.writeAsBytes(dataToSave);
  }
  
  /// Lưu nhiều results cùng lúc
  /// 
  /// [results]: List các CompressionResult
  /// [outputDirectory]: Thư mục output
  /// [namePattern]: Pattern cho tên file (có thể chứa {index}, {taskId})
  /// [outputFormat]: Format để lưu
  static Future<List<String>> saveMultipleToFiles(
    List<CompressionResult> results,
    String outputDirectory, {
    String namePattern = 'compressed_{index}',
    OutputFormat outputFormat = OutputFormat.jpeg,
  }) async {
    final savedPaths = <String>[];
    
    // Tạo output directory nếu chưa tồn tại
    final directory = Directory(outputDirectory);
    if (!await directory.exists()) {
      await directory.create(recursive: true);
    }
    
    for (int i = 0; i < results.length; i++) {
      final result = results[i];
      if (!result.isSuccess) continue;
      
      // Generate filename
      final extension = outputFormat == OutputFormat.jpeg ? '.jpg' : '.png';
      final filename = namePattern
          .replaceAll('{index}', i.toString().padLeft(3, '0'))
          .replaceAll('{taskId}', result.taskId)
          + extension;
      
      final outputPath = '$outputDirectory/$filename';
      
      try {
        await saveToFile(result, outputPath, outputFormat: outputFormat);
        savedPaths.add(outputPath);
      } catch (e) {
        print('Lỗi khi lưu file $outputPath: $e');
        // Tiếp tục với files khác
      }
    }
    
    return savedPaths;
  }
  
  /// Lưu vào temporary file
  /// 
  /// [result]: CompressionResult cần lưu
  /// [prefix]: Prefix cho tên file
  /// [outputFormat]: Format để lưu
  /// Returns: File object của temp file
  static Future<File> saveToTempFile(
    CompressionResult result, {
    String prefix = 'dct_compressed_',
    OutputFormat outputFormat = OutputFormat.jpeg,
  }) async {
    if (!result.isSuccess) {
      throw FileIOException('Không thể lưu temp file: compression không thành công');
    }
    
    final extension = outputFormat == OutputFormat.jpeg ? '.jpg' : '.png';
    final tempDir = Directory.systemTemp;
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final tempPath = '${tempDir.path}/$prefix$timestamp$extension';
    
    await saveToFile(result, tempPath, outputFormat: outputFormat);
    return File(tempPath);
  }
  
  /// Detect output format từ file path
  static OutputFormat _getOutputFormatFromPath(String outputPath) {
    final extension = _getFileExtension(outputPath).toLowerCase();
    
    switch (extension) {
      case '.jpg':
      case '.jpeg':
        return OutputFormat.jpeg;
      case '.png':
        return OutputFormat.png;
      default:
        return OutputFormat.jpeg; // Default
    }
  }
  
  /// Convert compression result sang JPEG format
  static Future<Uint8List> _convertToJpeg(CompressionResult result) async {
    // Nếu data đã là JPEG format, return trực tiếp
    if (result.compressedData.length > 2 &&
        result.compressedData[0] == 0xFF &&
        result.compressedData[1] == 0xD8) {
      return result.compressedData;
    }
    
    // Nếu không, cần reconstruct image và encode lại
    // Đây là placeholder - sẽ implement chi tiết sau
    return result.compressedData;
  }
  
  /// Convert compression result sang PNG format
  static Future<Uint8List> _convertToPng(CompressionResult result) async {
    // Nếu data đã là PNG format, return trực tiếp
    if (result.compressedData.length > 8 &&
        result.compressedData[0] == 0x89 &&
        result.compressedData[1] == 0x50 &&
        result.compressedData[2] == 0x4E &&
        result.compressedData[3] == 0x47) {
      return result.compressedData;
    }
    
    // Nếu không, cần reconstruct image và encode lại
    // Đây là placeholder - sẽ implement chi tiết sau
    return result.compressedData;
  }
  
  /// Tạo backup của file gốc trước khi overwrite
  /// 
  /// [originalPath]: Đường dẫn file gốc
  /// Returns: Đường dẫn backup file
  static Future<String> createBackup(String originalPath) async {
    final file = File(originalPath);
    if (!await file.exists()) {
      throw FileIOException('File gốc không tồn tại: $originalPath');
    }
    
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final backupPath = '$originalPath.backup_$timestamp';
    
    await file.copy(backupPath);
    return backupPath;
  }
  
  /// Xóa backup files cũ
  /// 
  /// [directory]: Thư mục chứa backup files
  /// [maxAge]: Tuổi tối đa của backup (days)
  static Future<void> cleanupBackups(String directory, {int maxAge = 7}) async {
    final dir = Directory(directory);
    if (!await dir.exists()) return;
    
    final cutoffTime = DateTime.now().subtract(Duration(days: maxAge));
    
    await for (final entity in dir.list()) {
      if (entity is File && entity.path.contains('.backup_')) {
        final stat = await entity.stat();
        if (stat.modified.isBefore(cutoffTime)) {
          try {
            await entity.delete();
          } catch (e) {
            print('Không thể xóa backup file ${entity.path}: $e');
          }
        }
      }
    }
  }
  
  /// Validate file size
  /// 
  /// [filePath]: Đường dẫn file
  /// [maxSizeBytes]: Kích thước tối đa (bytes)
  /// Returns: true nếu file size hợp lệ
  static Future<bool> validateFileSize(String filePath, int maxSizeBytes) async {
    final file = File(filePath);
    if (!await file.exists()) return false;
    
    final size = await file.length();
    return size <= maxSizeBytes;
  }
  
  /// Lấy thông tin file
  /// 
  /// [filePath]: Đường dẫn file
  /// Returns: Map chứa thông tin file
  static Future<Map<String, dynamic>> getFileInfo(String filePath) async {
    final file = File(filePath);
    if (!await file.exists()) {
      throw FileIOException('File không tồn tại: $filePath');
    }
    
    final stat = await file.stat();
    final extension = _getFileExtension(filePath);
    final format = detectFormatFromPath(filePath);
    
    return {
      'path': filePath,
      'size': stat.size,
      'modified': stat.modified,
      'extension': extension,
      'format': format.name,
      'isSupported': _isSupportedExtension(extension),
    };
  }
  
  /// Scan directory để tìm image files
  /// 
  /// [directoryPath]: Đường dẫn thư mục
  /// [recursive]: Có scan recursive không
  /// [supportedOnly]: Chỉ trả về supported formats
  /// Returns: List đường dẫn image files
  static Future<List<String>> scanImageFiles(
    String directoryPath, {
    bool recursive = false,
    bool supportedOnly = true,
  }) async {
    final directory = Directory(directoryPath);
    if (!await directory.exists()) {
      throw FileIOException('Thư mục không tồn tại: $directoryPath');
    }
    
    final imageFiles = <String>[];
    
    await for (final entity in directory.list(recursive: recursive)) {
      if (entity is File) {
        final extension = _getFileExtension(entity.path);
        
        if (!supportedOnly || _isSupportedExtension(extension)) {
          imageFiles.add(entity.path);
        }
      }
    }
    
    return imageFiles;
  }
}

/// Exception cho file I/O operations
class FileIOException implements Exception {
  final String message;
  
  const FileIOException(this.message);
  
  @override
  String toString() => 'FileIOException: $message';
}

/// Utility class cho output handling
class OutputHandler {
  /// Lưu CompressionResult ra file
  static Future<void> saveToFile(CompressionResult result, String path) async {
    await FileIOHandler.saveToFile(result, path);
  }
  
  /// Lưu ra temporary file
  static Future<File> saveToTempFile(CompressionResult result) async {
    return await FileIOHandler.saveToTempFile(result);
  }
  
  /// Lấy bytes từ CompressionResult
  static Uint8List getBytes(CompressionResult result) {
    if (!result.isSuccess) {
      throw FileIOException('Không thể lấy bytes: compression không thành công');
    }
    return result.compressedData;
  }
  
  /// Lưu nhiều results cùng lúc
  static Future<List<String>> saveMultiple(
    List<CompressionResult> results,
    String outputDirectory, {
    String namePattern = 'compressed_{index}',
    OutputFormat outputFormat = OutputFormat.jpeg,
  }) async {
    return await FileIOHandler.saveMultipleToFiles(
      results,
      outputDirectory,
      namePattern: namePattern,
      outputFormat: outputFormat,
    );
  }
}

/// Utility class cho input handling
class InputHandler {
  /// Load từ File object
  static Future<(Uint8List, String?)> fromFile(File file) async {
    return await FileIOHandler.loadInputData(file);
  }
  
  /// Load từ file path
  static Future<(Uint8List, String?)> fromPath(String path) async {
    return await FileIOHandler.loadInputData(path);
  }
  
  /// Load từ bytes
  static Future<(Uint8List, String?)> fromBytes(Uint8List bytes) async {
    return await FileIOHandler.loadInputData(bytes);
  }
  
  /// Load từ nhiều paths
  static Future<List<(Uint8List, String?)>> fromMultiplePaths(List<String> paths) async {
    return await FileIOHandler.loadMultipleInputs(paths);
  }
  
  /// Scan directory để tìm image files
  static Future<List<String>> scanDirectory(
    String directoryPath, {
    bool recursive = false,
  }) async {
    return await FileIOHandler.scanImageFiles(
      directoryPath,
      recursive: recursive,
    );
  }
}
