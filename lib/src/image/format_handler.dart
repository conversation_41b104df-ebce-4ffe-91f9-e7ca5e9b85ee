/// X<PERSON> lý đa định dạng ảnh cho DCT compression
/// 
/// File này chứa logic để đọc và ghi các định dạng ảnh khác nhau
/// (JPEG, PNG, BMP, TIFF, WebP) sử dụng package image.
library;

import 'dart:typed_data';
import 'dart:io';
import 'package:image/image.dart' as img;
import 'pixel_data.dart';
import '../utils/constants.dart';
import '../utils/validation.dart';
import '../compression/compression_options.dart';

/// Exception cho format handling
class FormatException implements Exception {
  final String message;
  final String? format;
  
  const FormatException(this.message, [this.format]);
  
  @override
  String toString() => 'FormatException: $message${format != null ? ' ($format)' : ''}';
}

/// Kết quả decode ảnh
class DecodeResult {
  final PixelData pixelData;
  final String detectedFormat;
  final Map<String, dynamic>? metadata;
  
  const DecodeResult({
    required this.pixelData,
    required this.detectedFormat,
    this.metadata,
  });
}

/// Xử lý đa định dạng ảnh
class FormatHandler {
  /// Detect format từ magic bytes
  static String? detectFormat(Uint8List data) {
    if (data.length < 8) return null;
    
    // JPEG
    if (data.length >= 3 && 
        data[0] == FormatConstants.jpegMagicBytes[0] &&
        data[1] == FormatConstants.jpegMagicBytes[1] &&
        data[2] == FormatConstants.jpegMagicBytes[2]) {
      return 'JPEG';
    }
    
    // PNG
    if (data.length >= 8) {
      bool isPng = true;
      for (int i = 0; i < FormatConstants.pngMagicBytes.length; i++) {
        if (data[i] != FormatConstants.pngMagicBytes[i]) {
          isPng = false;
          break;
        }
      }
      if (isPng) return 'PNG';
    }
    
    // BMP
    if (data.length >= 2 &&
        data[0] == FormatConstants.bmpMagicBytes[0] &&
        data[1] == FormatConstants.bmpMagicBytes[1]) {
      return 'BMP';
    }
    
    // TIFF (little endian)
    if (data.length >= 4) {
      bool isTiffLE = true;
      for (int i = 0; i < FormatConstants.tiffMagicBytesLE.length; i++) {
        if (data[i] != FormatConstants.tiffMagicBytesLE[i]) {
          isTiffLE = false;
          break;
        }
      }
      if (isTiffLE) return 'TIFF';
    }
    
    // TIFF (big endian)
    if (data.length >= 4) {
      bool isTiffBE = true;
      for (int i = 0; i < FormatConstants.tiffMagicBytesBE.length; i++) {
        if (data[i] != FormatConstants.tiffMagicBytesBE[i]) {
          isTiffBE = false;
          break;
        }
      }
      if (isTiffBE) return 'TIFF';
    }
    
    // WebP
    if (data.length >= 12 &&
        data[0] == FormatConstants.webpMagicBytes[0] &&
        data[1] == FormatConstants.webpMagicBytes[1] &&
        data[2] == FormatConstants.webpMagicBytes[2] &&
        data[3] == FormatConstants.webpMagicBytes[3] &&
        data[8] == FormatConstants.webpFormatBytes[0] &&
        data[9] == FormatConstants.webpFormatBytes[1] &&
        data[10] == FormatConstants.webpFormatBytes[2] &&
        data[11] == FormatConstants.webpFormatBytes[3]) {
      return 'WebP';
    }
    
    return null;
  }
  
  /// Decode ảnh từ bytes
  static DecodeResult decodeImage(Uint8List data) {
    // Validate input
    if (!ValidationUtils.validateByteData(data)) {
      throw const FormatException('Dữ liệu ảnh không hợp lệ');
    }
    
    // Detect format
    final format = detectFormat(data);
    if (format == null) {
      throw const FormatException('Không thể xác định định dạng ảnh');
    }
    
    // Decode using image package
    img.Image? image;
    try {
      image = img.decodeImage(data);
    } catch (e) {
      throw FormatException('Không thể decode ảnh: $e', format);
    }
    
    if (image == null) {
      throw FormatException('Decode ảnh thất bại', format);
    }
    
    // Convert to PixelData
    final pixelData = _convertFromImagePackage(image, format);
    
    // Extract metadata
    final metadata = _extractMetadata(image, format);
    
    return DecodeResult(
      pixelData: pixelData,
      detectedFormat: format,
      metadata: metadata,
    );
  }
  
  /// Decode ảnh từ file
  static Future<DecodeResult> decodeImageFromFile(String path) async {
    // Validate file
    if (!ValidationUtils.validateFilePath(path)) {
      throw FormatException('Đường dẫn file không hợp lệ: $path');
    }
    
    if (!await ValidationUtils.validateFileExists(path)) {
      throw FormatException('File không tồn tại: $path');
    }
    
    // Read file
    final file = File(path);
    final data = await file.readAsBytes();
    
    return decodeImage(data);
  }
  
  /// Convert từ image package sang PixelData
  static PixelData _convertFromImagePackage(img.Image image, String format) {
    final width = image.width;
    final height = image.height;
    final hasAlpha = image.hasAlpha;
    final channels = hasAlpha ? 4 : 3;
    
    // Tạo ImageInfo
    final info = ImageInfo(
      width: width,
      height: height,
      channels: channels,
      format: format,
      bitDepth: 8,
      hasAlpha: hasAlpha,
      colorSpace: 'RGB',
    );
    
    // Convert pixel data
    final data = Uint8List(width * height * channels);
    int index = 0;
    
    for (int y = 0; y < height; y++) {
      for (int x = 0; x < width; x++) {
        final pixel = image.getPixel(x, y);
        
        // Extract RGB(A) components
        data[index++] = pixel.r.toInt();
        data[index++] = pixel.g.toInt();
        data[index++] = pixel.b.toInt();

        if (hasAlpha) {
          data[index++] = pixel.a.toInt();
        }
      }
    }
    
    return PixelData.fromBytes(info, data);
  }
  
  /// Extract metadata từ image
  static Map<String, dynamic>? _extractMetadata(img.Image image, String format) {
    final metadata = <String, dynamic>{
      'format': format,
      'width': image.width,
      'height': image.height,
      'hasAlpha': image.hasAlpha,
      'channels': image.hasAlpha ? 4 : 3,
    };
    
    // Extract EXIF data nếu có
    try {
      // For now, just indicate if EXIF data exists
      metadata['hasExif'] = true;
    } catch (e) {
      metadata['hasExif'] = false;
    }
    
    return metadata;
  }
  
  /// Encode PixelData thành bytes
  static Uint8List encodeImage(PixelData pixelData, OutputFormat format, {
    int quality = CompressionConstants.defaultQuality,
    bool preserveMetadata = false,
    Map<String, dynamic>? metadata,
  }) {
    // Convert PixelData to image package format
    final image = _convertToImagePackage(pixelData);
    
    // Add metadata nếu cần
    if (preserveMetadata && metadata != null) {
      _addMetadataToImage(image, metadata);
    }
    
    // Encode theo format
    Uint8List? encoded;
    try {
      switch (format) {
        case OutputFormat.jpeg:
          encoded = img.encodeJpg(image, quality: quality);
          break;
        case OutputFormat.png:
          encoded = img.encodePng(image);
          break;
      }
    } catch (e) {
      throw FormatException('Encode ảnh thất bại: $e', format.name);
    }
    
    if (encoded == null) {
      throw FormatException('Encode ảnh thất bại', format.name);
    }
    
    return encoded;
  }
  
  /// Convert PixelData sang image package format
  static img.Image _convertToImagePackage(PixelData pixelData) {
    final info = pixelData.info;
    final data = pixelData.data;
    
    final image = img.Image(
      width: info.width,
      height: info.height,
      numChannels: info.channels,
    );
    
    int index = 0;
    for (int y = 0; y < info.height; y++) {
      for (int x = 0; x < info.width; x++) {
        final r = data[index++];
        final g = data[index++];
        final b = data[index++];
        final a = info.hasAlpha ? data[index++] : 255;
        
        final pixel = img.ColorRgba8(r, g, b, a);
        image.setPixel(x, y, pixel);
      }
    }
    
    return image;
  }
  
  /// Add metadata to image
  static void _addMetadataToImage(img.Image image, Map<String, dynamic> metadata) {
    // Add EXIF data nếu có
    if (metadata.containsKey('exif') && metadata['exif'] is Map) {
      final exifData = metadata['exif'] as Map;
      for (final entry in exifData.entries) {
        if (entry.key is String && entry.value != null) {
          image.exif[entry.key] = entry.value;
        }
      }
    }
  }
  
  /// Save PixelData to file
  static Future<void> saveImageToFile(PixelData pixelData, String path, {
    OutputFormat? format,
    int quality = CompressionConstants.defaultQuality,
    bool preserveMetadata = false,
    Map<String, dynamic>? metadata,
  }) async {
    // Determine format từ extension nếu không được chỉ định
    format ??= _getFormatFromExtension(path);
    
    // Encode image
    final encoded = encodeImage(
      pixelData, 
      format,
      quality: quality,
      preserveMetadata: preserveMetadata,
      metadata: metadata,
    );
    
    // Write to file
    final file = File(path);
    await file.writeAsBytes(encoded);
  }
  
  /// Get format từ file extension
  static OutputFormat _getFormatFromExtension(String path) {
    final extension = path.toLowerCase().split('.').last;
    
    switch (extension) {
      case 'jpg':
      case 'jpeg':
        return OutputFormat.jpeg;
      case 'png':
        return OutputFormat.png;
      default:
        return OutputFormat.jpeg; // Default
    }
  }
  
  /// Kiểm tra format có được hỗ trợ không
  static bool isFormatSupported(String format) {
    const supportedFormats = {'JPEG', 'PNG', 'BMP', 'TIFF', 'WebP'};
    return supportedFormats.contains(format.toUpperCase());
  }
  
  /// Lấy danh sách formats được hỗ trợ
  static List<String> getSupportedFormats() {
    return ['JPEG', 'PNG', 'BMP', 'TIFF', 'WebP'];
  }
  
  /// Validate format compatibility
  static bool validateFormatCompatibility(String inputFormat, OutputFormat outputFormat) {
    // Tất cả input formats đều có thể convert sang JPEG/PNG
    return isFormatSupported(inputFormat);
  }
}
