import 'dart:io';
import 'dart:typed_data';
import 'package:image/image.dart' as img;
import 'pixel_data.dart';
import '../compression/compression_options.dart';

/// Lớp xử lý ảnh đa định dạng với hỗ trợ loading từ nhiều nguồn
class ImageProcessor {
  /// Load ảnh từ nhiều nguồn khác nhau (File, path, bytes)
  /// 
  /// [input]: <PERSON><PERSON> thể là File, String (path), hoặc Uint8List (bytes)
  /// Returns: ImageData chứa thông tin và pixel data
  static Future<ImageData> loadImage(dynamic input) async {
    Uint8List bytes;
    String? sourcePath;
    
    // Xử lý các loại input khác nhau
    if (input is File) {
      bytes = await input.readAsBytes();
      sourcePath = input.path;
    } else if (input is String) {
      final file = File(input);
      if (!await file.exists()) {
        throw ImageProcessingException('File không tồn tại: $input');
      }
      bytes = await file.readAsBytes();
      sourcePath = input;
    } else if (input is Uint8List) {
      bytes = input;
    } else {
      throw ImageProcessingException('Input type không được hỗ trợ: ${input.runtimeType}');
    }
    
    // Decode ảnh sử dụng image package
    final image = img.decodeImage(bytes);
    if (image == null) {
      throw ImageProcessingException('Không thể decode ảnh từ dữ liệu đã cho');
    }
    
    // Detect format từ bytes
    final format = _detectImageFormat(bytes);
    
    // Tạo ImageInfo
    final imageInfo = ImageInfo(
      width: image.width,
      height: image.height,
      channels: image.numChannels,
      format: format.name,
      bitDepth: 8, // Image package sử dụng 8-bit per channel
      hasAlpha: image.hasAlpha,
      colorSpace: 'RGB',
      metadata: _extractMetadata(image),
    );
    
    // Convert sang pixel data format phù hợp cho DCT
    final pixelData = _convertToPixelData(image);
    
    return ImageData(
      imageInfo: imageInfo,
      pixelData: pixelData,
      originalBytes: bytes,
      sourcePath: sourcePath,
    );
  }
  
  /// Detect định dạng ảnh từ file header
  static InputFormat _detectImageFormat(Uint8List bytes) {
    if (bytes.length < 4) return InputFormat.auto;
    
    // JPEG: FF D8 FF
    if (bytes[0] == 0xFF && bytes[1] == 0xD8 && bytes[2] == 0xFF) {
      return InputFormat.jpeg;
    }
    
    // PNG: 89 50 4E 47
    if (bytes[0] == 0x89 && bytes[1] == 0x50 && bytes[2] == 0x4E && bytes[3] == 0x47) {
      return InputFormat.png;
    }
    
    // BMP: 42 4D
    if (bytes[0] == 0x42 && bytes[1] == 0x4D) {
      return InputFormat.bmp;
    }
    
    // TIFF: 49 49 2A 00 hoặc 4D 4D 00 2A
    if ((bytes[0] == 0x49 && bytes[1] == 0x49 && bytes[2] == 0x2A && bytes[3] == 0x00) ||
        (bytes[0] == 0x4D && bytes[1] == 0x4D && bytes[2] == 0x00 && bytes[3] == 0x2A)) {
      return InputFormat.tiff;
    }
    
    // WebP: RIFF....WEBP
    if (bytes.length >= 12 &&
        bytes[0] == 0x52 && bytes[1] == 0x49 && bytes[2] == 0x46 && bytes[3] == 0x46 &&
        bytes[8] == 0x57 && bytes[9] == 0x45 && bytes[10] == 0x42 && bytes[11] == 0x50) {
      return InputFormat.webp;
    }
    
    return InputFormat.auto;
  }
  
  /// Extract metadata từ image
  static Map<String, dynamic>? _extractMetadata(img.Image image) {
    final metadata = <String, dynamic>{};

    // Extract EXIF data nếu có
    try {
      // Check if EXIF data exists
      metadata['exif'] = 'present';
    } catch (e) {
      // Ignore EXIF errors
    }

    // Extract ICC profile nếu có
    if (image.iccProfile != null) {
      metadata['iccProfile'] = 'present';
    }

    return metadata.isNotEmpty ? metadata : null;
  }
  
  /// Convert Image sang pixel data format cho DCT processing
  static List<List<List<int>>> _convertToPixelData(img.Image image) {
    final width = image.width;
    final height = image.height;
    final channels = image.numChannels;
    
    // Tạo 3D array: [height][width][channels]
    final pixelData = List.generate(
      height,
      (_) => List.generate(
        width,
        (_) => List<int>.filled(channels, 0),
      ),
    );
    
    // Copy pixel data
    for (int y = 0; y < height; y++) {
      for (int x = 0; x < width; x++) {
        final pixel = image.getPixel(x, y);
        
        if (channels >= 3) {
          pixelData[y][x][0] = pixel.r.toInt(); // Red
          pixelData[y][x][1] = pixel.g.toInt(); // Green
          pixelData[y][x][2] = pixel.b.toInt(); // Blue
        } else {
          // Grayscale
          final gray = (0.299 * pixel.r + 0.587 * pixel.g + 0.114 * pixel.b).round();
          pixelData[y][x][0] = gray;
        }
        
        if (channels == 4 && image.hasAlpha) {
          pixelData[y][x][3] = pixel.a.toInt(); // Alpha
        }
      }
    }
    
    return pixelData;
  }
  
  /// Chia ảnh thành các blocks 8x8 cho DCT processing
  /// 
  /// [pixelData]: Pixel data từ ImageData
  /// [channel]: Channel index (0=R, 1=G, 2=B)
  /// Returns: List các blocks 8x8
  static List<List<List<int>>> splitIntoBlocks(
    List<List<List<int>>> pixelData,
    int channel,
  ) {
    final height = pixelData.length;
    final width = pixelData[0].length;
    final blocks = <List<List<int>>>[];
    
    // Xử lý từng block 8x8
    for (int blockY = 0; blockY < height; blockY += 8) {
      for (int blockX = 0; blockX < width; blockX += 8) {
        final block = List.generate(8, (_) => List<int>.filled(8, 0));
        
        // Copy pixel data vào block
        for (int y = 0; y < 8; y++) {
          for (int x = 0; x < 8; x++) {
            final pixelY = blockY + y;
            final pixelX = blockX + x;
            
            if (pixelY < height && pixelX < width) {
              block[y][x] = pixelData[pixelY][pixelX][channel];
            } else {
              // Padding với pixel cuối cùng nếu ảnh không chia hết cho 8
              final lastY = (pixelY >= height) ? height - 1 : pixelY;
              final lastX = (pixelX >= width) ? width - 1 : pixelX;
              block[y][x] = pixelData[lastY][lastX][channel];
            }
          }
        }
        
        blocks.add(block);
      }
    }
    
    return blocks;
  }
  
  /// Ghép các blocks 8x8 thành ảnh hoàn chỉnh
  /// 
  /// [blocks]: List các blocks 8x8
  /// [width]: Chiều rộng ảnh gốc
  /// [height]: Chiều cao ảnh gốc
  /// [channel]: Channel index
  /// Returns: Pixel data đã được reconstruct
  static List<List<List<int>>> combineBlocks(
    List<List<List<int>>> blocks,
    int width,
    int height,
    int channels,
  ) {
    final pixelData = List.generate(
      height,
      (_) => List.generate(
        width,
        (_) => List<int>.filled(channels, 0),
      ),
    );
    
    int blockIndex = 0;

    // Reconstruct từng block
    for (int blockY = 0; blockY < height; blockY += 8) {
      for (int blockX = 0; blockX < width; blockX += 8) {
        if (blockIndex >= blocks.length) break;

        // Copy block data về pixel data
        for (int y = 0; y < 8; y++) {
          for (int x = 0; x < 8; x++) {
            final pixelY = blockY + y;
            final pixelX = blockX + x;

            if (pixelY < height && pixelX < width) {
              // Reconstruct tất cả channels từ các blocks tương ứng
              for (int c = 0; c < channels; c++) {
                final channelBlockIndex = blockIndex + c * (blocks.length ~/ channels);
                if (channelBlockIndex < blocks.length) {
                  pixelData[pixelY][pixelX][c] = blocks[channelBlockIndex][y][x];
                }
              }
            }
          }
        }

        blockIndex++;
      }
    }
    
    return pixelData;
  }
  
  /// Resize ảnh để đảm bảo kích thước chia hết cho 8
  /// Cần thiết cho DCT processing
  /// 
  /// [imageData]: ImageData gốc
  /// Returns: ImageData với kích thước đã được adjust
  static ImageData padToMultipleOf8(ImageData imageData) {
    final originalWidth = imageData.imageInfo.width;
    final originalHeight = imageData.imageInfo.height;
    
    final paddedWidth = ((originalWidth + 7) ~/ 8) * 8;
    final paddedHeight = ((originalHeight + 7) ~/ 8) * 8;
    
    // Nếu đã chia hết cho 8 thì không cần pad
    if (paddedWidth == originalWidth && paddedHeight == originalHeight) {
      return imageData;
    }
    
    final channels = imageData.imageInfo.channels;
    final paddedPixelData = List.generate(
      paddedHeight,
      (_) => List.generate(
        paddedWidth,
        (_) => List<int>.filled(channels, 0),
      ),
    );
    
    // Copy pixel data gốc
    for (int y = 0; y < originalHeight; y++) {
      for (int x = 0; x < originalWidth; x++) {
        for (int c = 0; c < channels; c++) {
          paddedPixelData[y][x][c] = imageData.pixelData[y][x][c];
        }
      }
    }
    
    // Pad bằng cách repeat edge pixels
    // Pad right edge
    for (int y = 0; y < originalHeight; y++) {
      for (int x = originalWidth; x < paddedWidth; x++) {
        for (int c = 0; c < channels; c++) {
          paddedPixelData[y][x][c] = imageData.pixelData[y][originalWidth - 1][c];
        }
      }
    }
    
    // Pad bottom edge
    for (int y = originalHeight; y < paddedHeight; y++) {
      for (int x = 0; x < paddedWidth; x++) {
        for (int c = 0; c < channels; c++) {
          final sourceX = x < originalWidth ? x : originalWidth - 1;
          paddedPixelData[y][x][c] = imageData.pixelData[originalHeight - 1][sourceX][c];
        }
      }
    }
    
    // Update ImageInfo
    final paddedImageInfo = imageData.imageInfo.copyWith(
      width: paddedWidth,
      height: paddedHeight,
    );
    
    return ImageData(
      imageInfo: paddedImageInfo,
      pixelData: paddedPixelData,
      originalBytes: imageData.originalBytes,
      sourcePath: imageData.sourcePath,
      originalDimensions: (originalWidth, originalHeight),
    );
  }
  
  /// Crop ảnh về kích thước gốc sau khi xử lý
  /// 
  /// [imageData]: ImageData đã được process
  /// [originalWidth]: Chiều rộng gốc
  /// [originalHeight]: Chiều cao gốc
  /// Returns: ImageData với kích thước gốc
  static ImageData cropToOriginalSize(
    ImageData imageData,
    int originalWidth,
    int originalHeight,
  ) {
    if (imageData.imageInfo.width == originalWidth && 
        imageData.imageInfo.height == originalHeight) {
      return imageData;
    }
    
    final channels = imageData.imageInfo.channels;
    final croppedPixelData = List.generate(
      originalHeight,
      (_) => List.generate(
        originalWidth,
        (_) => List<int>.filled(channels, 0),
      ),
    );
    
    // Copy pixel data trong phạm vi gốc
    for (int y = 0; y < originalHeight; y++) {
      for (int x = 0; x < originalWidth; x++) {
        for (int c = 0; c < channels; c++) {
          croppedPixelData[y][x][c] = imageData.pixelData[y][x][c];
        }
      }
    }
    
    // Update ImageInfo
    final croppedImageInfo = imageData.imageInfo.copyWith(
      width: originalWidth,
      height: originalHeight,
    );
    
    return ImageData(
      imageInfo: croppedImageInfo,
      pixelData: croppedPixelData,
      originalBytes: imageData.originalBytes,
      sourcePath: imageData.sourcePath,
    );
  }
}

/// Lớp chứa dữ liệu ảnh đã được load và process
class ImageData {
  /// Thông tin về ảnh
  final ImageInfo imageInfo;
  
  /// Pixel data dạng 3D array [height][width][channels]
  final List<List<List<int>>> pixelData;
  
  /// Dữ liệu bytes gốc
  final Uint8List originalBytes;
  
  /// Đường dẫn source file (nếu có)
  final String? sourcePath;
  
  /// Kích thước gốc trước khi pad (nếu có)
  final (int, int)? originalDimensions;
  
  const ImageData({
    required this.imageInfo,
    required this.pixelData,
    required this.originalBytes,
    this.sourcePath,
    this.originalDimensions,
  });
  
  /// Kiểm tra xem có phải ảnh đã được pad không
  bool get isPadded => originalDimensions != null;
  
  /// Lấy kích thước gốc (trước khi pad)
  (int, int) get originalSize => originalDimensions ?? (imageInfo.width, imageInfo.height);
  
  @override
  String toString() {
    return 'ImageData(${imageInfo.width}x${imageInfo.height}, '
           '${imageInfo.channels} channels, format: ${imageInfo.format})';
  }
}

/// Exception cho image processing errors
class ImageProcessingException implements Exception {
  final String message;
  
  const ImageProcessingException(this.message);
  
  @override
  String toString() => 'ImageProcessingException: $message';
}
