/// C<PERSON>u trúc dữ liệu pixel hiệu quả cho DCT compression
/// 
/// File này chứa các class để quản lý dữ liệu pixel một cách hiệu quả,
/// hỗ trợ nhiều format màu và tối ưu memory usage.
library;

import 'dart:typed_data';
import 'dart:math' as math;
import '../utils/constants.dart';
import '../utils/math_utils.dart';
import '../utils/memory_manager.dart';

/// Thông tin metadata của ảnh
class ImageInfo {
  final int width;
  final int height;
  final int channels;
  final String format;
  final int bitDepth;
  final bool hasAlpha;
  final String colorSpace;
  final Map<String, dynamic>? metadata;
  
  const ImageInfo({
    required this.width,
    required this.height,
    required this.channels,
    required this.format,
    required this.bitDepth,
    required this.hasAlpha,
    required this.colorSpace,
    this.metadata,
  });
  
  /// Tổng số pixels
  int get totalPixels => width * height;
  
  /// <PERSON><PERSON>ch thước dữ liệu pixel (bytes)
  int get dataSize => totalPixels * channels;
  
  /// Aspect ratio
  double get aspectRatio => width / height;
  
  /// Có phải ảnh vuông không
  bool get isSquare => width == height;
  
  /// Có phải ảnh landscape không
  bool get isLandscape => width > height;
  
  /// Có phải ảnh portrait không
  bool get isPortrait => height > width;
  
  @override
  String toString() {
    return 'ImageInfo(${width}x$height, $channels channels, $format, $colorSpace)';
  }
  
  /// Tạo copy với thông tin mới
  ImageInfo copyWith({
    int? width,
    int? height,
    int? channels,
    String? format,
    int? bitDepth,
    bool? hasAlpha,
    String? colorSpace,
    Map<String, dynamic>? metadata,
  }) {
    return ImageInfo(
      width: width ?? this.width,
      height: height ?? this.height,
      channels: channels ?? this.channels,
      format: format ?? this.format,
      bitDepth: bitDepth ?? this.bitDepth,
      hasAlpha: hasAlpha ?? this.hasAlpha,
      colorSpace: colorSpace ?? this.colorSpace,
      metadata: metadata ?? this.metadata,
    );
  }
}

/// Pixel data hiệu quả với memory management
class PixelData {
  final ImageInfo _info;
  final Uint8List _data;
  final MemoryManager _memoryManager = MemoryManager();
  
  PixelData._(this._info, this._data);
  
  /// Tạo PixelData từ raw bytes
  factory PixelData.fromBytes(ImageInfo info, Uint8List data) {
    assert(data.length == info.dataSize, 
           'Data size không khớp với image info: ${data.length} != ${info.dataSize}');
    return PixelData._(info, data);
  }
  
  /// Tạo PixelData từ 3D array
  factory PixelData.from3DArray(List<List<List<int>>> pixelArray) {
    final height = pixelArray.length;
    final width = pixelArray[0].length;
    final channels = pixelArray[0][0].length;
    
    final info = ImageInfo(
      width: width,
      height: height,
      channels: channels,
      format: 'RAW',
      bitDepth: 8,
      hasAlpha: channels == 4,
      colorSpace: channels == 1 ? 'GRAY' : 'RGB',
    );
    
    final data = Uint8List(width * height * channels);
    int index = 0;
    
    for (int y = 0; y < height; y++) {
      for (int x = 0; x < width; x++) {
        for (int c = 0; c < channels; c++) {
          data[index++] = MathUtils.clamp(pixelArray[y][x][c], 0, 255);
        }
      }
    }
    
    return PixelData._(info, data);
  }
  
  /// Tạo PixelData trống với kích thước cho trước
  factory PixelData.empty(int width, int height, int channels) {
    final info = ImageInfo(
      width: width,
      height: height,
      channels: channels,
      format: 'RAW',
      bitDepth: 8,
      hasAlpha: channels == 4,
      colorSpace: channels == 1 ? 'GRAY' : 'RGB',
    );
    
    final data = Uint8List(width * height * channels);
    return PixelData._(info, data);
  }
  
  /// Thông tin ảnh
  ImageInfo get info => _info;
  
  /// Raw pixel data
  Uint8List get data => _data;
  
  /// Lấy pixel tại vị trí (x, y)
  List<int> getPixel(int x, int y) {
    assert(x >= 0 && x < _info.width, 'x out of bounds: $x');
    assert(y >= 0 && y < _info.height, 'y out of bounds: $y');
    
    final index = (y * _info.width + x) * _info.channels;
    return List.generate(_info.channels, (c) => _data[index + c]);
  }
  
  /// Set pixel tại vị trí (x, y)
  void setPixel(int x, int y, List<int> pixel) {
    assert(x >= 0 && x < _info.width, 'x out of bounds: $x');
    assert(y >= 0 && y < _info.height, 'y out of bounds: $y');
    assert(pixel.length == _info.channels, 'Pixel channels không khớp');
    
    final index = (y * _info.width + x) * _info.channels;
    for (int c = 0; c < _info.channels; c++) {
      _data[index + c] = MathUtils.clamp(pixel[c], 0, 255);
    }
  }
  
  /// Lấy giá trị channel tại vị trí (x, y)
  int getChannel(int x, int y, int channel) {
    assert(x >= 0 && x < _info.width, 'x out of bounds: $x');
    assert(y >= 0 && y < _info.height, 'y out of bounds: $y');
    assert(channel >= 0 && channel < _info.channels, 'Channel out of bounds: $channel');
    
    final index = (y * _info.width + x) * _info.channels + channel;
    return _data[index];
  }
  
  /// Set giá trị channel tại vị trí (x, y)
  void setChannel(int x, int y, int channel, int value) {
    assert(x >= 0 && x < _info.width, 'x out of bounds: $x');
    assert(y >= 0 && y < _info.height, 'y out of bounds: $y');
    assert(channel >= 0 && channel < _info.channels, 'Channel out of bounds: $channel');
    
    final index = (y * _info.width + x) * _info.channels + channel;
    _data[index] = MathUtils.clamp(value, 0, 255);
  }
  
  /// Chuyển đổi sang 3D array
  List<List<List<int>>> to3DArray() {
    final result = List.generate(_info.height, 
      (_) => List.generate(_info.width, 
        (_) => List<int>.filled(_info.channels, 0)));
    
    for (int y = 0; y < _info.height; y++) {
      for (int x = 0; x < _info.width; x++) {
        result[y][x] = getPixel(x, y);
      }
    }
    
    return result;
  }
  
  /// Trích xuất một channel thành 2D array
  List<List<int>> extractChannel(int channel) {
    assert(channel >= 0 && channel < _info.channels, 'Channel out of bounds: $channel');
    
    final result = List.generate(_info.height, 
      (_) => List<int>.filled(_info.width, 0));
    
    for (int y = 0; y < _info.height; y++) {
      for (int x = 0; x < _info.width; x++) {
        result[y][x] = getChannel(x, y, channel);
      }
    }
    
    return result;
  }
  
  /// Ghi đè một channel từ 2D array
  void setChannel2D(int channel, List<List<int>> channelData) {
    assert(channel >= 0 && channel < _info.channels, 'Channel out of bounds: $channel');
    assert(channelData.length == _info.height, 'Height không khớp');
    assert(channelData[0].length == _info.width, 'Width không khớp');
    
    for (int y = 0; y < _info.height; y++) {
      for (int x = 0; x < _info.width; x++) {
        setChannel(x, y, channel, channelData[y][x]);
      }
    }
  }
  
  /// Crop ảnh
  PixelData crop(int startX, int startY, int width, int height) {
    assert(startX >= 0 && startX < _info.width, 'startX out of bounds');
    assert(startY >= 0 && startY < _info.height, 'startY out of bounds');
    assert(startX + width <= _info.width, 'Crop width vượt quá bounds');
    assert(startY + height <= _info.height, 'Crop height vượt quá bounds');
    
    final croppedData = Uint8List(width * height * _info.channels);
    int destIndex = 0;
    
    for (int y = startY; y < startY + height; y++) {
      for (int x = startX; x < startX + width; x++) {
        final pixel = getPixel(x, y);
        for (int c = 0; c < _info.channels; c++) {
          croppedData[destIndex++] = pixel[c];
        }
      }
    }
    
    final croppedInfo = _info.copyWith(width: width, height: height);
    return PixelData._(croppedInfo, croppedData);
  }
  
  /// Resize ảnh (nearest neighbor)
  PixelData resize(int newWidth, int newHeight) {
    final resizedData = Uint8List(newWidth * newHeight * _info.channels);
    int destIndex = 0;
    
    final scaleX = _info.width / newWidth;
    final scaleY = _info.height / newHeight;
    
    for (int y = 0; y < newHeight; y++) {
      for (int x = 0; x < newWidth; x++) {
        final srcX = (x * scaleX).floor().clamp(0, _info.width - 1);
        final srcY = (y * scaleY).floor().clamp(0, _info.height - 1);
        
        final pixel = getPixel(srcX, srcY);
        for (int c = 0; c < _info.channels; c++) {
          resizedData[destIndex++] = pixel[c];
        }
      }
    }
    
    final resizedInfo = _info.copyWith(width: newWidth, height: newHeight);
    return PixelData._(resizedInfo, resizedData);
  }
  
  /// Tạo copy của PixelData
  PixelData copy() {
    final copiedData = Uint8List.fromList(_data);
    return PixelData._(_info, copiedData);
  }
  
  /// Tính histogram cho một channel
  Map<int, int> calculateHistogram(int channel) {
    assert(channel >= 0 && channel < _info.channels, 'Channel out of bounds: $channel');
    
    final histogram = <int, int>{};
    
    for (int y = 0; y < _info.height; y++) {
      for (int x = 0; x < _info.width; x++) {
        final value = getChannel(x, y, channel);
        histogram[value] = (histogram[value] ?? 0) + 1;
      }
    }
    
    return histogram;
  }
  
  /// Tính statistics cho một channel
  Map<String, double> calculateChannelStatistics(int channel) {
    assert(channel >= 0 && channel < _info.channels, 'Channel out of bounds: $channel');
    
    final values = <int>[];
    for (int y = 0; y < _info.height; y++) {
      for (int x = 0; x < _info.width; x++) {
        values.add(getChannel(x, y, channel));
      }
    }
    
    final mean = MathUtils.mean(values);
    final stdDev = MathUtils.standardDeviation(values);
    final min = values.reduce(math.min);
    final max = values.reduce(math.max);
    
    return {
      'mean': mean,
      'standardDeviation': stdDev,
      'min': min.toDouble(),
      'max': max.toDouble(),
      'range': (max - min).toDouble(),
    };
  }
  
  /// Kiểm tra memory usage
  int get memoryUsage => _data.length;
  
  /// Validate pixel data integrity
  bool validate() {
    if (_data.length != _info.dataSize) return false;
    
    // Kiểm tra tất cả pixel values trong range hợp lệ
    for (final value in _data) {
      if (value < 0 || value > 255) return false;
    }
    
    return true;
  }
}
