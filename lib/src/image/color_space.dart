import 'dart:math' as math;

/// Lớp xử lý chuyển đổi không gian màu RGB ↔ YUV
/// YUV được sử dụng trong JPEG compression để tận dụng đặc tính thị giác con người
class ColorSpaceConverter {
  // Hằng số cho chuyển đổi RGB → YUV (ITU-R BT.601)
  static const double _kr = 0.299;
  static const double _kg = 0.587;
  static const double _kb = 0.114;
  
  /// Chuyển đổi RGB sang YUV
  ///
  /// [r]: Red component (0-255)
  /// [g]: Green component (0-255)
  /// [b]: Blue component (0-255)
  /// Returns: (Y, U, V) tuple với Y trong [0, 255], U,V trong [-128, 127]
  static (int, int, int) rgbToYuv(int r, int g, int b) {
    // Sử dụng công thức YUV chuẩn ITU-R BT.601
    final y = (0.299 * r + 0.587 * g + 0.114 * b).round();
    final u = ((-0.169 * r - 0.331 * g + 0.5 * b) + 128).round();
    final v = ((0.5 * r - 0.419 * g - 0.081 * b) + 128).round();

    return (
      y.clamp(0, 255),
      u.clamp(0, 255),
      v.clamp(0, 255),
    );
  }
  
  /// Chuyển đổi YUV sang RGB
  ///
  /// [y]: Luminance (0-255)
  /// [u]: Chrominance U (0-255, shifted from -128 to 127)
  /// [v]: Chrominance V (0-255, shifted from -128 to 127)
  /// Returns: (R, G, B) tuple với các giá trị trong [0, 255]
  static (int, int, int) yuvToRgb(int y, int u, int v) {
    // Sử dụng công thức YUV inverse chuẩn ITU-R BT.601
    final uShifted = u - 128;
    final vShifted = v - 128;

    final r = (y + 1.402 * vShifted).round();
    final g = (y - 0.344 * uShifted - 0.714 * vShifted).round();
    final b = (y + 1.772 * uShifted).round();

    return (
      r.clamp(0, 255),
      g.clamp(0, 255),
      b.clamp(0, 255),
    );
  }
  
  /// Chuyển đổi toàn bộ ảnh từ RGB sang YUV
  /// 
  /// [rgbPixelData]: Pixel data RGB format [height][width][3]
  /// Returns: Pixel data YUV format [height][width][3]
  static List<List<List<int>>> convertRgbToYuv(List<List<List<int>>> rgbPixelData) {
    final height = rgbPixelData.length;
    final width = rgbPixelData[0].length;
    
    final yuvPixelData = List.generate(
      height,
      (_) => List.generate(
        width,
        (_) => List<int>.filled(3, 0),
      ),
    );
    
    for (int y = 0; y < height; y++) {
      for (int x = 0; x < width; x++) {
        final r = rgbPixelData[y][x][0];
        final g = rgbPixelData[y][x][1];
        final b = rgbPixelData[y][x][2];
        
        final (yComp, uComp, vComp) = rgbToYuv(r, g, b);
        
        yuvPixelData[y][x][0] = yComp;
        yuvPixelData[y][x][1] = uComp; // U already in [0, 255] range
        yuvPixelData[y][x][2] = vComp; // V already in [0, 255] range
      }
    }
    
    return yuvPixelData;
  }
  
  /// Chuyển đổi toàn bộ ảnh từ YUV sang RGB
  /// 
  /// [yuvPixelData]: Pixel data YUV format [height][width][3]
  /// Returns: Pixel data RGB format [height][width][3]
  static List<List<List<int>>> convertYuvToRgb(List<List<List<int>>> yuvPixelData) {
    final height = yuvPixelData.length;
    final width = yuvPixelData[0].length;
    
    final rgbPixelData = List.generate(
      height,
      (_) => List.generate(
        width,
        (_) => List<int>.filled(3, 0),
      ),
    );
    
    for (int y = 0; y < height; y++) {
      for (int x = 0; x < width; x++) {
        final yComp = yuvPixelData[y][x][0];
        final uComp = yuvPixelData[y][x][1]; // U in [0, 255] range
        final vComp = yuvPixelData[y][x][2]; // V in [0, 255] range
        
        final (r, g, b) = yuvToRgb(yComp, uComp, vComp);
        
        rgbPixelData[y][x][0] = r;
        rgbPixelData[y][x][1] = g;
        rgbPixelData[y][x][2] = b;
      }
    }
    
    return rgbPixelData;
  }
  
  /// Tách ảnh YUV thành các channels riêng biệt
  /// Hữu ích cho DCT processing từng channel
  /// 
  /// [yuvPixelData]: Pixel data YUV format
  /// Returns: (Y channel, U channel, V channel)
  static (List<List<int>>, List<List<int>>, List<List<int>>) separateYuvChannels(
    List<List<List<int>>> yuvPixelData,
  ) {
    final height = yuvPixelData.length;
    final width = yuvPixelData[0].length;
    
    final yChannel = List.generate(height, (_) => List<int>.filled(width, 0));
    final uChannel = List.generate(height, (_) => List<int>.filled(width, 0));
    final vChannel = List.generate(height, (_) => List<int>.filled(width, 0));
    
    for (int y = 0; y < height; y++) {
      for (int x = 0; x < width; x++) {
        yChannel[y][x] = yuvPixelData[y][x][0];
        uChannel[y][x] = yuvPixelData[y][x][1];
        vChannel[y][x] = yuvPixelData[y][x][2];
      }
    }
    
    return (yChannel, uChannel, vChannel);
  }
  
  /// Ghép các channels YUV riêng biệt thành ảnh hoàn chỉnh
  /// 
  /// [yChannel]: Y channel data
  /// [uChannel]: U channel data  
  /// [vChannel]: V channel data
  /// Returns: Pixel data YUV format [height][width][3]
  static List<List<List<int>>> combineYuvChannels(
    List<List<int>> yChannel,
    List<List<int>> uChannel,
    List<List<int>> vChannel,
  ) {
    final height = yChannel.length;
    final width = yChannel[0].length;
    
    final yuvPixelData = List.generate(
      height,
      (_) => List.generate(
        width,
        (_) => List<int>.filled(3, 0),
      ),
    );
    
    for (int y = 0; y < height; y++) {
      for (int x = 0; x < width; x++) {
        yuvPixelData[y][x][0] = yChannel[y][x];
        yuvPixelData[y][x][1] = uChannel[y][x];
        yuvPixelData[y][x][2] = vChannel[y][x];
      }
    }
    
    return yuvPixelData;
  }
  
  /// Chuyển đổi RGB sang Grayscale
  /// Sử dụng công thức luminance chuẩn
  /// 
  /// [r]: Red component (0-255)
  /// [g]: Green component (0-255)
  /// [b]: Blue component (0-255)
  /// Returns: Grayscale value (0-255)
  static int rgbToGrayscale(int r, int g, int b) {
    return (_kr * r + _kg * g + _kb * b).round().clamp(0, 255);
  }
  
  /// Chuyển đổi toàn bộ ảnh RGB sang Grayscale
  /// 
  /// [rgbPixelData]: Pixel data RGB format [height][width][3]
  /// Returns: Pixel data Grayscale format [height][width][1]
  static List<List<List<int>>> convertRgbToGrayscale(List<List<List<int>>> rgbPixelData) {
    final height = rgbPixelData.length;
    final width = rgbPixelData[0].length;
    
    final grayscalePixelData = List.generate(
      height,
      (_) => List.generate(
        width,
        (_) => List<int>.filled(1, 0),
      ),
    );
    
    for (int y = 0; y < height; y++) {
      for (int x = 0; x < width; x++) {
        final r = rgbPixelData[y][x][0];
        final g = rgbPixelData[y][x][1];
        final b = rgbPixelData[y][x][2];
        
        grayscalePixelData[y][x][0] = rgbToGrayscale(r, g, b);
      }
    }
    
    return grayscalePixelData;
  }
  
  /// Chuyển đổi Grayscale sang RGB (duplicate gray value)
  /// 
  /// [grayscalePixelData]: Pixel data Grayscale format [height][width][1]
  /// Returns: Pixel data RGB format [height][width][3]
  static List<List<List<int>>> convertGrayscaleToRgb(List<List<List<int>>> grayscalePixelData) {
    final height = grayscalePixelData.length;
    final width = grayscalePixelData[0].length;
    
    final rgbPixelData = List.generate(
      height,
      (_) => List.generate(
        width,
        (_) => List<int>.filled(3, 0),
      ),
    );
    
    for (int y = 0; y < height; y++) {
      for (int x = 0; x < width; x++) {
        final gray = grayscalePixelData[y][x][0];
        
        rgbPixelData[y][x][0] = gray; // R
        rgbPixelData[y][x][1] = gray; // G
        rgbPixelData[y][x][2] = gray; // B
      }
    }
    
    return rgbPixelData;
  }
  
  /// Subsample chrominance channels (U, V) theo tỷ lệ 4:2:0
  /// Giảm resolution của U,V channels để tiết kiệm dung lượng
  /// Đây là kỹ thuật chuẩn trong JPEG compression
  /// 
  /// [uChannel]: U channel data
  /// [vChannel]: V channel data
  /// Returns: (subsampled U, subsampled V)
  static (List<List<int>>, List<List<int>>) subsampleChrominance420(
    List<List<int>> uChannel,
    List<List<int>> vChannel,
  ) {
    final height = uChannel.length;
    final width = uChannel[0].length;
    
    final subsampledHeight = (height + 1) ~/ 2; // Round up
    final subsampledWidth = (width + 1) ~/ 2;   // Round up
    
    final subsampledU = List.generate(
      subsampledHeight,
      (_) => List<int>.filled(subsampledWidth, 0),
    );
    final subsampledV = List.generate(
      subsampledHeight,
      (_) => List<int>.filled(subsampledWidth, 0),
    );
    
    // Average 2x2 blocks
    for (int y = 0; y < subsampledHeight; y++) {
      for (int x = 0; x < subsampledWidth; x++) {
        final srcY = y * 2;
        final srcX = x * 2;
        
        int uSum = 0;
        int vSum = 0;
        int count = 0;
        
        // Average 2x2 block
        for (int dy = 0; dy < 2; dy++) {
          for (int dx = 0; dx < 2; dx++) {
            final pixelY = srcY + dy;
            final pixelX = srcX + dx;
            
            if (pixelY < height && pixelX < width) {
              uSum += uChannel[pixelY][pixelX];
              vSum += vChannel[pixelY][pixelX];
              count++;
            }
          }
        }
        
        if (count > 0) {
          subsampledU[y][x] = (uSum / count).round();
          subsampledV[y][x] = (vSum / count).round();
        }
      }
    }
    
    return (subsampledU, subsampledV);
  }
  
  /// Upsample chrominance channels từ 4:2:0 về full resolution
  /// 
  /// [subsampledU]: Subsampled U channel
  /// [subsampledV]: Subsampled V channel
  /// [targetHeight]: Target height cho output
  /// [targetWidth]: Target width cho output
  /// Returns: (upsampled U, upsampled V)
  static (List<List<int>>, List<List<int>>) upsampleChrominance420(
    List<List<int>> subsampledU,
    List<List<int>> subsampledV,
    int targetHeight,
    int targetWidth,
  ) {
    final upsampledU = List.generate(
      targetHeight,
      (_) => List<int>.filled(targetWidth, 0),
    );
    final upsampledV = List.generate(
      targetHeight,
      (_) => List<int>.filled(targetWidth, 0),
    );
    
    final subsampledHeight = subsampledU.length;
    final subsampledWidth = subsampledU[0].length;
    
    // Bilinear interpolation
    for (int y = 0; y < targetHeight; y++) {
      for (int x = 0; x < targetWidth; x++) {
        final srcY = y / 2.0;
        final srcX = x / 2.0;
        
        final y0 = srcY.floor().clamp(0, subsampledHeight - 1);
        final y1 = (y0 + 1).clamp(0, subsampledHeight - 1);
        final x0 = srcX.floor().clamp(0, subsampledWidth - 1);
        final x1 = (x0 + 1).clamp(0, subsampledWidth - 1);
        
        final wy = srcY - y0;
        final wx = srcX - x0;
        
        // Bilinear interpolation for U
        final u00 = subsampledU[y0][x0];
        final u01 = subsampledU[y0][x1];
        final u10 = subsampledU[y1][x0];
        final u11 = subsampledU[y1][x1];
        
        final uInterpolated = (1 - wy) * (1 - wx) * u00 +
                             (1 - wy) * wx * u01 +
                             wy * (1 - wx) * u10 +
                             wy * wx * u11;
        
        // Bilinear interpolation for V
        final v00 = subsampledV[y0][x0];
        final v01 = subsampledV[y0][x1];
        final v10 = subsampledV[y1][x0];
        final v11 = subsampledV[y1][x1];
        
        final vInterpolated = (1 - wy) * (1 - wx) * v00 +
                             (1 - wy) * wx * v01 +
                             wy * (1 - wx) * v10 +
                             wy * wx * v11;
        
        upsampledU[y][x] = uInterpolated.round().clamp(0, 255);
        upsampledV[y][x] = vInterpolated.round().clamp(0, 255);
      }
    }
    
    return (upsampledU, upsampledV);
  }
  
  /// Validate color space conversion accuracy
  /// Test round-trip RGB → YUV → RGB
  /// 
  /// [r]: Red component
  /// [g]: Green component  
  /// [b]: Blue component
  /// Returns: true nếu error < threshold
  static bool validateConversion(int r, int g, int b, {int threshold = 2}) {
    final (y, u, v) = rgbToYuv(r, g, b);
    final (rBack, gBack, bBack) = yuvToRgb(y, u, v);
    
    final errorR = (r - rBack).abs();
    final errorG = (g - gBack).abs();
    final errorB = (b - bBack).abs();
    
    return errorR <= threshold && errorG <= threshold && errorB <= threshold;
  }
}
