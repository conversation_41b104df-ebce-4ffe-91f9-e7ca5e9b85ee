/// Flutter DCT Compress Library
///
/// Package nén ảnh thuần Dart sử dụng thuật toán Discrete Cosine Transform (DCT)
/// với hỗ trợ đa định dạng, x<PERSON> lý nền isolate, và kiểm soát tỷ lệ nén chính xác.
library;

// Export core DCT algorithm
export 'src/dct/dct_transform.dart';
export 'src/dct/quantization.dart';

// Export compression classes
export 'src/compression/compression_options.dart';
export 'src/compression/compression_result.dart';
export 'src/compression/compressor.dart';
export 'src/compression/compression_bounds.dart';
export 'src/compression/quality_controller.dart';
export 'src/compression/encoder.dart';
export 'src/compression/decoder.dart';

// Export image processing utilities
export 'src/image/image_processor.dart';
export 'src/image/color_space.dart';
export 'src/image/file_io.dart';
export 'src/image/pixel_data.dart';
export 'src/image/format_handler.dart';

// Export DCT processing
export 'src/dct/block_processor.dart';

// Export isolate-based processing
export 'src/isolates/compression_isolate.dart';

// Export utilities
export 'src/utils/constants.dart';
export 'src/utils/math_utils.dart';
export 'src/utils/validation.dart';
export 'src/utils/memory_manager.dart';
