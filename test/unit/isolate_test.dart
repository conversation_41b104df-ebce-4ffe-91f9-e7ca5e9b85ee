/// Unit tests cho isolate-based processing
/// 
/// <PERSON><PERSON><PERSON> tra background processing, progress tracking,
/// task management, và isolate communication.
library;

import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_dct_compress/src/isolates/progress_tracker.dart';
import 'package:flutter_dct_compress/src/isolates/task_manager.dart';
import 'package:flutter_dct_compress/src/isolates/isolate_utils.dart';
import 'dart:async';

void main() {
  group('Progress Tracker Tests', () {
    test('Progress info creation and properties', () {
      final startTime = DateTime.now();
      final progress = ProgressInfo(
        taskId: 'test_task',
        completed: 5,
        total: 10,
        currentItem: 'Processing item 5',
        startTime: startTime,
        lastUpdate: startTime.add(const Duration(seconds: 5)),
        processingRate: 1.0,
        status: ProgressStatus.running,
      );
      
      expect(progress.taskId, equals('test_task'));
      expect(progress.completed, equals(5));
      expect(progress.total, equals(10));
      expect(progress.completionRatio, equals(0.5));
      expect(progress.completionPercentage, equals(50.0));
      expect(progress.isCompleted, isFalse);
      expect(progress.elapsedTime, equals(const Duration(seconds: 5)));
    });
    
    test('Task progress tracker updates', () {
      final tracker = TaskProgressTracker(
        taskId: 'tracker_test',
        totalItems: 20,
      );
      
      // Initial state
      var progress = tracker.getCurrentProgress();
      expect(progress.completed, equals(0));
      expect(progress.status, equals(ProgressStatus.notStarted));
      
      // Update progress
      tracker.updateProgress(
        completed: 5,
        currentItem: 'Item 5',
        status: ProgressStatus.running,
      );
      
      progress = tracker.getCurrentProgress();
      expect(progress.completed, equals(5));
      expect(progress.currentItem, equals('Item 5'));
      expect(progress.status, equals(ProgressStatus.running));
      expect(progress.completionPercentage, equals(25.0));
    });
    
    test('Progress tracker completion', () {
      final tracker = TaskProgressTracker(
        taskId: 'completion_test',
        totalItems: 10,
      );
      
      tracker.markCompleted();
      
      final progress = tracker.getCurrentProgress();
      expect(progress.status, equals(ProgressStatus.completed));
      expect(progress.completed, equals(10));
      expect(progress.isCompleted, isTrue);
    });
    
    test('Progress tracker cancellation', () {
      final tracker = TaskProgressTracker(
        taskId: 'cancel_test',
        totalItems: 10,
      );
      
      tracker.markCancelled();
      
      final progress = tracker.getCurrentProgress();
      expect(progress.status, equals(ProgressStatus.cancelled));
    });
    
    test('Global progress tracker management', () {
      final progressTracker = ProgressTracker();
      
      // Create tracker
      final tracker = progressTracker.createTracker(
        taskId: 'global_test',
        totalItems: 15,
      );
      
      expect(tracker.taskId, equals('global_test'));
      expect(tracker.totalItems, equals(15));
      
      // Update progress
      progressTracker.updateProgress(
        'global_test',
        completed: 7,
        status: ProgressStatus.running,
      );
      
      final progress = progressTracker.getProgress('global_test');
      expect(progress?.completed, equals(7));
      expect(progress?.status, equals(ProgressStatus.running));
      
      progressTracker.dispose();
    });
  });
  
  group('Task Manager Tests', () {
    test('Task creation and properties', () {
      final task = TaskInfo(
        id: 'test_task',
        name: 'Test Task',
        priority: TaskPriority.high,
        metadata: {'key': 'value'},
      );
      
      expect(task.id, equals('test_task'));
      expect(task.name, equals('Test Task'));
      expect(task.priority, equals(TaskPriority.high));
      expect(task.status, equals(TaskStatus.queued));
      expect(task.metadata['key'], equals('value'));
      expect(task.isCancelled, isFalse);
    });
    
    test('Cancellation token functionality', () {
      final token = CancellationToken();
      
      expect(token.isCancelled, isFalse);
      expect(token.reason, isNull);
      
      token.cancel('Test cancellation');
      
      expect(token.isCancelled, isTrue);
      expect(token.reason, equals('Test cancellation'));
      
      expect(() => token.throwIfCancelled(), 
             throwsA(isA<TaskCancelledException>()));
    });
    
    test('Task manager basic operations', () {
      final manager = TaskManager();
      
      // Add task
      final taskId = manager.addTask(
        name: 'Test Task',
        priority: TaskPriority.normal,
      );
      
      expect(taskId, isNotEmpty);
      
      // Get task
      final task = manager.getTask(taskId);
      expect(task, isNotNull);
      expect(task!.name, equals('Test Task'));
      expect(task.priority, equals(TaskPriority.normal));
      
      // Get stats
      final stats = manager.getStats();
      expect(stats.totalTasks, equals(1));
      expect(stats.queuedTasks, greaterThanOrEqualTo(0));
      
      manager.dispose();
    });
    
    test('Task cancellation', () {
      final manager = TaskManager();
      
      final taskId = manager.addTask(name: 'Cancellable Task');
      final success = manager.cancelTask(taskId, 'User requested');
      
      expect(success, isTrue);
      
      final task = manager.getTask(taskId);
      expect(task?.status, equals(TaskStatus.cancelled));
      expect(task?.isCancelled, isTrue);
      
      manager.dispose();
    });
    
    test('Task priority ordering', () {
      final manager = TaskManager();
      
      // Add tasks with different priorities
      final lowId = manager.addTask(
        name: 'Low Priority',
        priority: TaskPriority.low,
      );
      final highId = manager.addTask(
        name: 'High Priority',
        priority: TaskPriority.high,
      );
      final urgentId = manager.addTask(
        name: 'Urgent Priority',
        priority: TaskPriority.urgent,
      );
      
      final allTasks = manager.getAllTasks();
      expect(allTasks.length, equals(3));
      
      // Find tasks by priority
      final urgentTask = manager.getTask(urgentId);
      final highTask = manager.getTask(highId);
      final lowTask = manager.getTask(lowId);
      
      expect(urgentTask?.priority, equals(TaskPriority.urgent));
      expect(highTask?.priority, equals(TaskPriority.high));
      expect(lowTask?.priority, equals(TaskPriority.low));
      
      manager.dispose();
    });
    
    test('Task manager statistics', () {
      final manager = TaskManager();
      
      // Add multiple tasks
      for (int i = 0; i < 5; i++) {
        manager.addTask(name: 'Task $i');
      }
      
      final stats = manager.getStats();
      expect(stats.totalTasks, equals(5));
      expect(stats.utilizationRate, greaterThanOrEqualTo(0.0));
      expect(stats.utilizationRate, lessThanOrEqualTo(1.0));
      
      manager.dispose();
    });
  });
  
  group('Isolate Utils Tests', () {
    test('Isolate message creation and serialization', () {
      final message = IsolateMessage(
        type: IsolateMessageType.processRequest,
        taskId: 'test_task',
        data: {'key': 'value', 'number': 42},
        metadata: {'source': 'test'},
      );
      
      expect(message.type, equals(IsolateMessageType.processRequest));
      expect(message.taskId, equals('test_task'));
      expect(message.messageId, isNotEmpty);
      expect(message.data?['key'], equals('value'));
      expect(message.data?['number'], equals(42));
      expect(message.metadata?['source'], equals('test'));
      
      // Test serialization
      final map = message.toMap();
      final restored = IsolateMessage.fromMap(map);
      
      expect(restored.type, equals(message.type));
      expect(restored.taskId, equals(message.taskId));
      expect(restored.messageId, equals(message.messageId));
      expect(restored.data?['key'], equals('value'));
    });
    
    test('Isolate utils helper functions', () {
      // Test Uint8List serialization
      final originalData = Uint8List.fromList([1, 2, 3, 4, 5]);
      final serialized = IsolateUtils.serializeUint8List(originalData);
      final deserialized = IsolateUtils.deserializeUint8List(serialized);
      
      expect(deserialized, equals(originalData));
      
      // Test object serialization
      final originalObject = {'name': 'test', 'value': 123};
      final serializedJson = IsolateUtils.serializeObject(originalObject);
      final deserializedObject = IsolateUtils.deserializeObject(serializedJson);
      
      expect(deserializedObject['name'], equals('test'));
      expect(deserializedObject['value'], equals(123));
      
      // Test memory estimation
      final memoryUsage = IsolateUtils.estimateMemoryUsage(originalData);
      expect(memoryUsage, equals(5)); // 5 bytes
    });
    
    test('Priority queue functionality', () {
      final queue = PriorityQueue<int>((a, b) => a.compareTo(b));
      
      // Add elements in random order
      queue.add(5);
      queue.add(2);
      queue.add(8);
      queue.add(1);
      queue.add(9);
      
      expect(queue.length, equals(5));
      expect(queue.isNotEmpty, isTrue);
      
      // Remove elements (should come out in sorted order)
      final results = <int>[];
      while (queue.isNotEmpty) {
        results.add(queue.removeFirst());
      }
      
      expect(results, equals([1, 2, 5, 8, 9]));
      expect(queue.isEmpty, isTrue);
    });
    
    test('Priority queue with custom objects', () {
      final queue = PriorityQueue<TaskInfo>((a, b) {
        final priorityCompare = b.priority.value.compareTo(a.priority.value);
        if (priorityCompare != 0) return priorityCompare;
        return a.createdAt.compareTo(b.createdAt);
      });
      
      final task1 = TaskInfo(id: '1', name: 'Low', priority: TaskPriority.low);
      final task2 = TaskInfo(id: '2', name: 'High', priority: TaskPriority.high);
      final task3 = TaskInfo(id: '3', name: 'Normal', priority: TaskPriority.normal);
      
      queue.add(task1);
      queue.add(task2);
      queue.add(task3);
      
      // Should come out in priority order: High, Normal, Low
      final first = queue.removeFirst();
      expect(first.priority, equals(TaskPriority.high));
      
      final second = queue.removeFirst();
      expect(second.priority, equals(TaskPriority.normal));
      
      final third = queue.removeFirst();
      expect(third.priority, equals(TaskPriority.low));
    });
  });
  
  group('Isolate Pool Tests', () {
    test('Isolate pool stats', () {
      final stats = IsolatePoolStats(
        totalWorkers: 3,
        availableWorkers: 2,
        busyWorkers: 1,
        queuedItems: 5,
      );
      
      expect(stats.totalWorkers, equals(3));
      expect(stats.availableWorkers, equals(2));
      expect(stats.busyWorkers, equals(1));
      expect(stats.queuedItems, equals(5));
      expect(stats.utilizationRate, closeTo(0.333, 0.01));
    });
  });
}
