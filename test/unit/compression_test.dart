/// Unit tests cho compression engine
/// 
/// <PERSON><PERSON><PERSON> tra các thành phần compression như options, bounds,
/// quality controller, encoder/decoder.
library;

import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_dct_compress/src/compression/compression_options.dart';
import 'package:flutter_dct_compress/src/compression/compression_result.dart';
import 'package:flutter_dct_compress/src/compression/compression_bounds.dart';
import 'package:flutter_dct_compress/src/compression/quality_controller.dart';
import 'package:flutter_dct_compress/src/image/pixel_data.dart';
import 'dart:typed_data';

void main() {
  group('Compression Options Tests', () {
    test('Default compression options are valid', () {
      const options = CompressionOptions();
      
      expect(options.quality, equals(85));
      expect(options.colorSpace, equals(ColorSpace.yuv));
      expect(options.outputFormat, equals(OutputFormat.jpeg));
      expect(options.useIsolate, isTrue);
      expect(options.useAdaptiveQuantization, isTrue);
    });
    
    test('Quality-based options creation', () {
      final options = CompressionOptions.quality(95);
      
      expect(options.quality, equals(95));
      expect(options.colorSpace, equals(ColorSpace.yuv));
    });
    
    test('Bounds-based options creation', () {
      final options = CompressionOptions.withBounds(
        minRatio: 0.2,
        maxRatio: 0.8,
        quality: 75,
      );
      
      expect(options.minCompressionRatio, equals(0.2));
      expect(options.maxCompressionRatio, equals(0.8));
      expect(options.quality, equals(75));
    });
    
    test('High quality preset', () {
      final options = CompressionOptions.highQuality();
      
      expect(options.quality, equals(95));
      expect(options.colorSpace, equals(ColorSpace.rgb));
      expect(options.useAdaptiveQuantization, isFalse);
    });
    
    test('High compression preset', () {
      final options = CompressionOptions.highCompression();
      
      expect(options.quality, equals(50));
      expect(options.colorSpace, equals(ColorSpace.yuv));
      expect(options.useAdaptiveQuantization, isTrue);
      expect(options.progressive, isTrue);
    });
    
    test('Web optimized preset', () {
      final options = CompressionOptions.webOptimized();
      
      expect(options.quality, equals(75));
      expect(options.outputFormat, equals(OutputFormat.jpeg));
      expect(options.progressive, isTrue);
      expect(options.preserveMetadata, isFalse);
    });
    
    test('Options serialization', () {
      const options = CompressionOptions(
        quality: 80,
        minCompressionRatio: 0.3,
        maxCompressionRatio: 0.7,
        preserveMetadata: true,
      );
      
      final map = options.toMap();
      final restored = CompressionOptions.fromMap(map);
      
      expect(restored.quality, equals(options.quality));
      expect(restored.minCompressionRatio, equals(options.minCompressionRatio));
      expect(restored.maxCompressionRatio, equals(options.maxCompressionRatio));
      expect(restored.preserveMetadata, equals(options.preserveMetadata));
    });
    
    test('Invalid bounds throw assertion error', () {
      expect(() => CompressionOptions.withBounds(
        minRatio: 0.8,
        maxRatio: 0.2, // Invalid: min > max
      ), throwsA(isA<AssertionError>()));
      
      expect(() => CompressionOptions.withBounds(
        minRatio: -0.1, // Invalid: negative
        maxRatio: 0.5,
      ), throwsA(isA<AssertionError>()));
    });
  });
  
  group('Compression Result Tests', () {
    test('Compression result creation', () {
      final imageInfo = ImageInfo(
        width: 800,
        height: 600,
        channels: 3,
        format: 'JPEG',
        bitDepth: 8,
        hasAlpha: false,
        colorSpace: 'RGB',
      );
      
      final result = CompressionResult(
        compressedData: Uint8List.fromList([1, 2, 3, 4, 5]),
        compressionRatio: 0.5,
        originalSize: 1000,
        compressedSize: 500,
        processingTime: const Duration(milliseconds: 100),
        imageInfo: imageInfo,
        taskId: 'test_task',
        qualityUsed: 85,
      );
      
      expect(result.compressionRatio, equals(0.5));
      expect(result.originalSize, equals(1000));
      expect(result.compressedSize, equals(500));
      expect(result.isSuccess, isTrue);
      expect(result.spaceSaved, equals(500));
      expect(result.compressionPercentage, equals(50.0));
    });
    
    test('Error result creation', () {
      final imageInfo = ImageInfo(
        width: 800,
        height: 600,
        channels: 3,
        format: 'JPEG',
        bitDepth: 8,
        hasAlpha: false,
        colorSpace: 'RGB',
      );
      
      final result = CompressionResult.error(
        taskId: 'error_task',
        errorMessage: 'Test error',
        imageInfo: imageInfo,
        originalSize: 1000,
      );
      
      expect(result.isSuccess, isFalse);
      expect(result.errorMessage, equals('Test error'));
      expect(result.compressedSize, equals(0));
      expect(result.compressionRatio, equals(0.0));
    });
    
    test('Result serialization', () {
      final imageInfo = ImageInfo(
        width: 800,
        height: 600,
        channels: 3,
        format: 'JPEG',
        bitDepth: 8,
        hasAlpha: false,
        colorSpace: 'RGB',
      );
      
      final result = CompressionResult(
        compressedData: Uint8List.fromList([1, 2, 3]),
        compressionRatio: 0.6,
        originalSize: 1000,
        compressedSize: 600,
        processingTime: const Duration(milliseconds: 150),
        imageInfo: imageInfo,
        taskId: 'serialize_test',
        qualityUsed: 75,
        psnr: 35.5,
        ssim: 0.95,
      );
      
      final map = result.toMap();
      expect(map['compressionRatio'], equals(0.6));
      expect(map['originalSize'], equals(1000));
      expect(map['compressedSize'], equals(600));
      expect(map['taskId'], equals('serialize_test'));
      expect(map['psnr'], equals(35.5));
      expect(map['ssim'], equals(0.95));
    });
  });
  
  group('Compression Bounds Tests', () {
    test('Quality adjustment calculation', () {
      final adjustment = QualityAdjustment(
        originalQuality: 85,
        adjustedQuality: 75,
        expectedRatio: 0.6,
        reason: 'Test adjustment',
      );
      
      expect(adjustment.originalQuality, equals(85));
      expect(adjustment.adjustedQuality, equals(75));
      expect(adjustment.expectedRatio, equals(0.6));
      expect(adjustment.qualityDelta, equals(-10));
    });
    
    test('Bounds checking logic', () {
      final bounds = CompressionBounds();
      
      // Test within bounds
      final withinBounds = bounds.checkBounds(
        originalSize: 1000,
        compressedSize: 500,
        minRatio: 0.3,
        maxRatio: 0.7,
      );
      expect(withinBounds.isWithinBounds, isTrue);
      expect(withinBounds.actualRatio, equals(0.5));
      
      // Test below minimum
      final belowMin = bounds.checkBounds(
        originalSize: 1000,
        compressedSize: 200,
        minRatio: 0.3,
        maxRatio: 0.7,
      );
      expect(belowMin.isWithinBounds, isFalse);
      expect(belowMin.violationType, equals(BoundsViolationType.belowMinimum));
      
      // Test above maximum
      final aboveMax = bounds.checkBounds(
        originalSize: 1000,
        compressedSize: 800,
        minRatio: 0.3,
        maxRatio: 0.7,
      );
      expect(aboveMax.isWithinBounds, isFalse);
      expect(aboveMax.violationType, equals(BoundsViolationType.aboveMaximum));
    });
  });
  
  group('Quality Controller Tests', () {
    test('Quality metrics calculation', () {
      // Create test pixel data
      final imageInfo = ImageInfo(
        width: 8,
        height: 8,
        channels: 1,
        format: 'test',
        bitDepth: 8,
        hasAlpha: false,
        colorSpace: 'grayscale',
      );
      
      final originalData = List.generate(64, (i) => i % 256);
      final compressedData = List.generate(64, (i) => (i % 256) + 1);
      
      final original = PixelData.fromBytes(imageInfo, originalData);
      final compressed = PixelData.fromBytes(imageInfo, compressedData);
      
      final metrics = QualityController.calculateQualityMetrics(
        original,
        compressed,
      );
      
      expect(metrics.psnr, isA<double>());
      expect(metrics.ssim, isA<double>());
      expect(metrics.mse, isA<double>());
      expect(metrics.psnr, greaterThan(0));
      expect(metrics.ssim, greaterThanOrEqualTo(0));
      expect(metrics.ssim, lessThanOrEqualTo(1));
    });
    
    test('Adaptive quality adjustment', () {
      final imageInfo = ImageInfo(
        width: 16,
        height: 16,
        channels: 1,
        format: 'test',
        bitDepth: 8,
        hasAlpha: false,
        colorSpace: 'grayscale',
      );
      
      // Simple pattern
      final simpleData = List.generate(256, (i) => 128);
      final simplePixelData = PixelData.fromBytes(imageInfo, simpleData);
      
      final adjustment = QualityController.calculateAdaptiveQuality(
        simplePixelData,
        baseQuality: 75,
        targetCompressionRatio: 0.5,
      );
      
      expect(adjustment.adjustedQuality, isA<int>());
      expect(adjustment.adjustedQuality, greaterThan(0));
      expect(adjustment.adjustedQuality, lessThanOrEqualTo(100));
    });
  });
}
