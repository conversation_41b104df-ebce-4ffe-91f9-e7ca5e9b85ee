/// Unit tests cho format support
/// 
/// <PERSON><PERSON><PERSON> tra hỗ trợ các định dạng ảnh khác nhau:
/// JPEG, PNG, BMP, TIFF, WebP
library;

import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_dct_compress/src/image/format_handler.dart';
import 'package:flutter_dct_compress/src/image/pixel_data.dart';
import 'package:flutter_dct_compress/src/image/color_space.dart';
import 'package:flutter_dct_compress/src/image/file_io.dart';
import 'dart:typed_data';
import 'dart:math' as math;

void main() {
  group('Format Detection Tests', () {
    test('JPEG format detection', () {
      // JPEG magic bytes: FF D8 FF
      final jpegBytes = Uint8List.fromList([0xFF, 0xD8, 0xFF, 0xE0]);
      final format = FormatHandler.detectFormat(jpegBytes);
      expect(format, equals(ImageFormat.jpeg));
    });
    
    test('PNG format detection', () {
      // PNG magic bytes: 89 50 4E 47 0D 0A 1A 0A
      final pngBytes = Uint8List.fromList([
        0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A
      ]);
      final format = FormatHandler.detectFormat(pngBytes);
      expect(format, equals(ImageFormat.png));
    });
    
    test('BMP format detection', () {
      // BMP magic bytes: 42 4D (BM)
      final bmpBytes = Uint8List.fromList([0x42, 0x4D]);
      final format = FormatHandler.detectFormat(bmpBytes);
      expect(format, equals(ImageFormat.bmp));
    });
    
    test('WebP format detection', () {
      // WebP magic bytes: 52 49 46 46 ... 57 45 42 50 (RIFF...WEBP)
      final webpBytes = Uint8List.fromList([
        0x52, 0x49, 0x46, 0x46, // RIFF
        0x00, 0x00, 0x00, 0x00, // File size (placeholder)
        0x57, 0x45, 0x42, 0x50  // WEBP
      ]);
      final format = FormatHandler.detectFormat(webpBytes);
      expect(format, equals(ImageFormat.webp));
    });
    
    test('TIFF format detection', () {
      // TIFF magic bytes: 49 49 2A 00 (little endian) or 4D 4D 00 2A (big endian)
      final tiffBytes = Uint8List.fromList([0x49, 0x49, 0x2A, 0x00]);
      final format = FormatHandler.detectFormat(tiffBytes);
      expect(format, equals(ImageFormat.tiff));
    });
    
    test('Unknown format detection', () {
      final unknownBytes = Uint8List.fromList([0x00, 0x01, 0x02, 0x03]);
      final format = FormatHandler.detectFormat(unknownBytes);
      expect(format, equals(ImageFormat.unknown));
    });
    
    test('Empty data handling', () {
      final emptyBytes = Uint8List(0);
      final format = FormatHandler.detectFormat(emptyBytes);
      expect(format, equals(ImageFormat.unknown));
    });
  });
  
  group('Format Validation Tests', () {
    test('Valid format validation', () {
      expect(FormatHandler.isFormatSupported(ImageFormat.jpeg), isTrue);
      expect(FormatHandler.isFormatSupported(ImageFormat.png), isTrue);
      expect(FormatHandler.isFormatSupported(ImageFormat.bmp), isTrue);
      expect(FormatHandler.isFormatSupported(ImageFormat.tiff), isTrue);
      expect(FormatHandler.isFormatSupported(ImageFormat.webp), isTrue);
    });
    
    test('Invalid format validation', () {
      expect(FormatHandler.isFormatSupported(ImageFormat.unknown), isFalse);
    });
    
    test('Format extension mapping', () {
      expect(FormatHandler.getFormatFromExtension('jpg'), equals(ImageFormat.jpeg));
      expect(FormatHandler.getFormatFromExtension('jpeg'), equals(ImageFormat.jpeg));
      expect(FormatHandler.getFormatFromExtension('png'), equals(ImageFormat.png));
      expect(FormatHandler.getFormatFromExtension('bmp'), equals(ImageFormat.bmp));
      expect(FormatHandler.getFormatFromExtension('tiff'), equals(ImageFormat.tiff));
      expect(FormatHandler.getFormatFromExtension('tif'), equals(ImageFormat.tiff));
      expect(FormatHandler.getFormatFromExtension('webp'), equals(ImageFormat.webp));
      expect(FormatHandler.getFormatFromExtension('unknown'), equals(ImageFormat.unknown));
    });
    
    test('Case insensitive extension mapping', () {
      expect(FormatHandler.getFormatFromExtension('JPG'), equals(ImageFormat.jpeg));
      expect(FormatHandler.getFormatFromExtension('PNG'), equals(ImageFormat.png));
      expect(FormatHandler.getFormatFromExtension('BMP'), equals(ImageFormat.bmp));
    });
  });
  
  group('Image Decoding Tests', () {
    test('Create test image data', () {
      // Create a simple test image (8x8 grayscale)
      final width = 8;
      final height = 8;
      final channels = 1;
      
      final imageData = List.generate(height, (y) =>
        List.generate(width, (x) =>
          List.generate(channels, (c) => (x + y) % 256)
        )
      );
      
      expect(imageData.length, equals(height));
      expect(imageData[0].length, equals(width));
      expect(imageData[0][0].length, equals(channels));
    });
    
    test('Pixel data creation from bytes', () {
      final imageInfo = ImageInfo(
        width: 4,
        height: 4,
        channels: 3,
        format: 'RGB',
        bitDepth: 8,
        hasAlpha: false,
        colorSpace: 'RGB',
      );
      
      // Create test RGB data (4x4x3 = 48 bytes)
      final testData = Uint8List.fromList(
        List.generate(48, (i) => i % 256)
      );
      
      final pixelData = PixelData.fromBytes(imageInfo, testData);
      
      expect(pixelData.info.width, equals(4));
      expect(pixelData.info.height, equals(4));
      expect(pixelData.info.channels, equals(3));
      expect(pixelData.data.length, equals(48));
    });
    
    test('Pixel access and modification', () {
      final imageInfo = ImageInfo(
        width: 2,
        height: 2,
        channels: 3,
        format: 'RGB',
        bitDepth: 8,
        hasAlpha: false,
        colorSpace: 'RGB',
      );
      
      final testData = Uint8List.fromList([
        255, 0, 0,    // Red pixel
        0, 255, 0,    // Green pixel
        0, 0, 255,    // Blue pixel
        255, 255, 255 // White pixel
      ]);
      
      final pixelData = PixelData.fromBytes(imageInfo, testData);
      
      // Test pixel access
      final redPixel = pixelData.getPixel(0, 0);
      expect(redPixel, equals([255, 0, 0]));
      
      final greenPixel = pixelData.getPixel(1, 0);
      expect(greenPixel, equals([0, 255, 0]));
      
      // Test pixel modification
      pixelData.setPixel(0, 0, [128, 128, 128]);
      final modifiedPixel = pixelData.getPixel(0, 0);
      expect(modifiedPixel, equals([128, 128, 128]));
    });
  });
  
  group('Color Space Conversion Tests', () {
    test('RGB to YUV conversion', () {
      final rgbData = [
        [[255, 0, 0], [0, 255, 0]], // Red, Green
        [[0, 0, 255], [255, 255, 255]] // Blue, White
      ];
      
      final yuvData = ColorSpaceConverter.convertRgbToYuv(rgbData);
      
      expect(yuvData.length, equals(2));
      expect(yuvData[0].length, equals(2));
      expect(yuvData[0][0].length, equals(3));
      
      // Check that conversion produces valid YUV values
      for (int y = 0; y < 2; y++) {
        for (int x = 0; x < 2; x++) {
          final yuv = yuvData[y][x];
          expect(yuv[0], greaterThanOrEqualTo(0)); // Y component
          expect(yuv[0], lessThanOrEqualTo(255));
          expect(yuv[1], greaterThanOrEqualTo(0)); // U component
          expect(yuv[1], lessThanOrEqualTo(255));
          expect(yuv[2], greaterThanOrEqualTo(0)); // V component
          expect(yuv[2], lessThanOrEqualTo(255));
        }
      }
    });
    
    test('YUV to RGB conversion', () {
      final yuvData = [
        [[128, 128, 128], [200, 100, 150]], // Gray, Some color
        [[50, 200, 75], [255, 128, 128]]    // Dark color, Bright
      ];
      
      final rgbData = ColorSpaceConverter.convertYuvToRgb(yuvData);
      
      expect(rgbData.length, equals(2));
      expect(rgbData[0].length, equals(2));
      expect(rgbData[0][0].length, equals(3));
      
      // Check that conversion produces valid RGB values
      for (int y = 0; y < 2; y++) {
        for (int x = 0; x < 2; x++) {
          final rgb = rgbData[y][x];
          expect(rgb[0], greaterThanOrEqualTo(0)); // R component
          expect(rgb[0], lessThanOrEqualTo(255));
          expect(rgb[1], greaterThanOrEqualTo(0)); // G component
          expect(rgb[1], lessThanOrEqualTo(255));
          expect(rgb[2], greaterThanOrEqualTo(0)); // B component
          expect(rgb[2], lessThanOrEqualTo(255));
        }
      }
    });
    
    test('RGB to YUV to RGB round trip', () {
      final originalRgb = [
        [[255, 128, 64], [32, 200, 100]]
      ];
      
      final yuv = ColorSpaceConverter.convertRgbToYuv(originalRgb);
      final reconstructedRgb = ColorSpaceConverter.convertYuvToRgb(yuv);
      
      // Check that round trip preserves values reasonably well
      for (int y = 0; y < originalRgb.length; y++) {
        for (int x = 0; x < originalRgb[y].length; x++) {
          for (int c = 0; c < 3; c++) {
            final original = originalRgb[y][x][c];
            final reconstructed = reconstructedRgb[y][x][c];
            final diff = (original - reconstructed).abs();
            expect(diff, lessThan(5)); // Allow small rounding errors
          }
        }
      }
    });
  });
  
  group('File I/O Tests', () {
    test('Input validation', () {
      // Test empty input
      expect(() => FileIO.validateInput([]), throwsArgumentError);
      
      // Test null input
      expect(() => FileIO.validateInput(null), throwsArgumentError);
    });
    
    test('Output format validation', () {
      expect(FileIO.isValidOutputFormat(OutputFormat.jpeg), isTrue);
      expect(FileIO.isValidOutputFormat(OutputFormat.png), isTrue);
    });
    
    test('Path validation', () {
      expect(FileIO.isValidPath('test.jpg'), isTrue);
      expect(FileIO.isValidPath('test.png'), isTrue);
      expect(FileIO.isValidPath(''), isFalse);
      expect(FileIO.isValidPath('test.xyz'), isFalse);
    });
  });
}
