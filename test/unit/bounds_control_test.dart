/// Unit tests cho compression bounds control
/// 
/// <PERSON><PERSON><PERSON> tra hệ thống kiểm so<PERSON>t tỷ lệ nén min/max,
/// quality adjustment, và bounds validation.
library;

import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_dct_compress/src/compression/compression_bounds.dart';
import 'package:flutter_dct_compress/src/compression/compression_options.dart';
import 'package:flutter_dct_compress/src/image/pixel_data.dart';
import 'dart:typed_data';

void main() {
  group('Compression Bounds Tests', () {
    test('Bounds result creation', () {
      final result = BoundsResult(
        isWithinBounds: true,
        actualRatio: 0.6,
        targetMinRatio: 0.4,
        targetMaxRatio: 0.8,
        violationType: BoundsViolationType.none,
        suggestedQuality: 75,
      );
      
      expect(result.isWithinBounds, isTrue);
      expect(result.actualRatio, equals(0.6));
      expect(result.targetMinRatio, equals(0.4));
      expect(result.targetMaxRatio, equals(0.8));
      expect(result.violationType, equals(BoundsViolationType.none));
      expect(result.suggestedQuality, equals(75));
    });
    
    test('Bounds checking - within bounds', () {
      final bounds = CompressionBounds();
      
      final result = bounds.checkBounds(
        originalSize: 1000,
        compressedSize: 600,
        minRatio: 0.4,
        maxRatio: 0.8,
      );
      
      expect(result.isWithinBounds, isTrue);
      expect(result.actualRatio, equals(0.6));
      expect(result.violationType, equals(BoundsViolationType.none));
    });
    
    test('Bounds checking - below minimum', () {
      final bounds = CompressionBounds();
      
      final result = bounds.checkBounds(
        originalSize: 1000,
        compressedSize: 200, // 20% - below 40% minimum
        minRatio: 0.4,
        maxRatio: 0.8,
      );
      
      expect(result.isWithinBounds, isFalse);
      expect(result.actualRatio, equals(0.2));
      expect(result.violationType, equals(BoundsViolationType.belowMinimum));
      expect(result.suggestedQuality, lessThan(75)); // Should suggest lower quality
    });
    
    test('Bounds checking - above maximum', () {
      final bounds = CompressionBounds();
      
      final result = bounds.checkBounds(
        originalSize: 1000,
        compressedSize: 900, // 90% - above 80% maximum
        minRatio: 0.4,
        maxRatio: 0.8,
      );
      
      expect(result.isWithinBounds, isFalse);
      expect(result.actualRatio, equals(0.9));
      expect(result.violationType, equals(BoundsViolationType.aboveMaximum));
      expect(result.suggestedQuality, greaterThan(75)); // Should suggest higher quality
    });
    
    test('Quality adjustment calculation', () {
      final adjustment = QualityAdjustment(
        originalQuality: 85,
        adjustedQuality: 75,
        expectedRatio: 0.6,
        reason: 'Bounds adjustment',
      );
      
      expect(adjustment.originalQuality, equals(85));
      expect(adjustment.adjustedQuality, equals(75));
      expect(adjustment.qualityDelta, equals(-10));
      expect(adjustment.expectedRatio, equals(0.6));
      expect(adjustment.reason, equals('Bounds adjustment'));
    });
    
    test('Adaptive quality adjustment based on target ratio', () {
      final bounds = CompressionBounds();
      
      // Test for target ratio 0.5 (50% compression)
      final adjustment1 = bounds.calculateQualityAdjustment(
        currentQuality: 85,
        targetCompressionRatio: 0.5,
        actualCompressionRatio: 0.7, // Too high, need more compression
      );
      
      expect(adjustment1.adjustedQuality, lessThan(85)); // Should reduce quality
      
      // Test for target ratio 0.8 (20% compression)
      final adjustment2 = bounds.calculateQualityAdjustment(
        currentQuality: 85,
        targetCompressionRatio: 0.8,
        actualCompressionRatio: 0.5, // Too low, need less compression
      );
      
      expect(adjustment2.adjustedQuality, greaterThan(85)); // Should increase quality
    });
    
    test('Iterative quality adjustment', () {
      final bounds = CompressionBounds();
      
      var currentQuality = 85;
      final targetRatio = 0.6;
      
      // Simulate iterative adjustment
      for (int iteration = 0; iteration < 3; iteration++) {
        final actualRatio = 0.8 - (iteration * 0.1); // Gradually approaching target
        
        final adjustment = bounds.calculateQualityAdjustment(
          currentQuality: currentQuality,
          targetCompressionRatio: targetRatio,
          actualCompressionRatio: actualRatio,
        );
        
        currentQuality = adjustment.adjustedQuality;
        
        // Quality should be adjusting towards target
        expect(currentQuality, greaterThan(0));
        expect(currentQuality, lessThanOrEqualTo(100));
      }
    });
  });
  
  group('Quality Estimation Tests', () {
    test('Quality estimation for target ratio', () {
      final bounds = CompressionBounds();
      
      // Test different target ratios
      final quality50 = bounds.estimateQualityForRatio(0.5); // 50% compression
      final quality30 = bounds.estimateQualityForRatio(0.3); // 70% compression
      final quality80 = bounds.estimateQualityForRatio(0.8); // 20% compression
      
      // Higher compression ratio should require lower quality
      expect(quality30, lessThan(quality50));
      expect(quality50, lessThan(quality80));
      
      // All qualities should be in valid range
      expect(quality30, greaterThanOrEqualTo(1));
      expect(quality30, lessThanOrEqualTo(100));
      expect(quality50, greaterThanOrEqualTo(1));
      expect(quality50, lessThanOrEqualTo(100));
      expect(quality80, greaterThanOrEqualTo(1));
      expect(quality80, lessThanOrEqualTo(100));
    });
    
    test('Compression ratio estimation for quality', () {
      final bounds = CompressionBounds();
      
      // Test different qualities
      final ratio95 = bounds.estimateRatioForQuality(95); // High quality
      final ratio75 = bounds.estimateRatioForQuality(75); // Medium quality
      final ratio25 = bounds.estimateRatioForQuality(25); // Low quality
      
      // Higher quality should result in higher compression ratio (less compression)
      expect(ratio95, greaterThan(ratio75));
      expect(ratio75, greaterThan(ratio25));
      
      // All ratios should be in valid range
      expect(ratio25, greaterThanOrEqualTo(0.1));
      expect(ratio25, lessThanOrEqualTo(0.9));
      expect(ratio75, greaterThanOrEqualTo(0.1));
      expect(ratio75, lessThanOrEqualTo(0.9));
      expect(ratio95, greaterThanOrEqualTo(0.1));
      expect(ratio95, lessThanOrEqualTo(0.9));
    });
  });
  
  group('Bounds Enforcement Tests', () {
    test('Enforce minimum compression ratio', () {
      final bounds = CompressionBounds();
      
      final result = bounds.enforceMinimumRatio(
        originalSize: 1000,
        compressedSize: 200, // 20%
        minRatio: 0.4, // Require at least 40%
        currentQuality: 85,
      );
      
      expect(result.isWithinBounds, isFalse);
      expect(result.suggestedQuality, lessThan(85)); // Should suggest lower quality
    });
    
    test('Enforce maximum compression ratio', () {
      final bounds = CompressionBounds();
      
      final result = bounds.enforceMaximumRatio(
        originalSize: 1000,
        compressedSize: 950, // 95%
        maxRatio: 0.8, // Allow at most 80%
        currentQuality: 85,
      );
      
      expect(result.isWithinBounds, isFalse);
      expect(result.suggestedQuality, greaterThan(85)); // Should suggest higher quality
    });
    
    test('Bounds enforcement with image complexity', () {
      final bounds = CompressionBounds();
      
      // Create test image data
      final imageInfo = ImageInfo(
        width: 16,
        height: 16,
        channels: 1,
        format: 'test',
        bitDepth: 8,
        hasAlpha: false,
        colorSpace: 'grayscale',
      );
      
      // Simple image (low complexity)
      final simpleData = Uint8List.fromList(List.generate(256, (i) => 128));
      final simplePixelData = PixelData.fromBytes(imageInfo, simpleData);
      
      // Complex image (high complexity)
      final complexData = Uint8List.fromList(List.generate(256, (i) => i % 256));
      final complexPixelData = PixelData.fromBytes(imageInfo, complexData);
      
      final simpleResult = bounds.enforceWithComplexity(
        pixelData: simplePixelData,
        originalSize: 1000,
        compressedSize: 300,
        minRatio: 0.4,
        maxRatio: 0.8,
        currentQuality: 75,
      );
      
      final complexResult = bounds.enforceWithComplexity(
        pixelData: complexPixelData,
        originalSize: 1000,
        compressedSize: 300,
        minRatio: 0.4,
        maxRatio: 0.8,
        currentQuality: 75,
      );
      
      // Complex images might need different quality adjustments
      expect(simpleResult.suggestedQuality, isA<int>());
      expect(complexResult.suggestedQuality, isA<int>());
    });
  });
  
  group('Edge Cases Tests', () {
    test('Zero size handling', () {
      final bounds = CompressionBounds();
      
      expect(() => bounds.checkBounds(
        originalSize: 0,
        compressedSize: 100,
        minRatio: 0.4,
        maxRatio: 0.8,
      ), throwsArgumentError);
      
      expect(() => bounds.checkBounds(
        originalSize: 1000,
        compressedSize: -100,
        minRatio: 0.4,
        maxRatio: 0.8,
      ), throwsArgumentError);
    });
    
    test('Invalid ratio bounds', () {
      final bounds = CompressionBounds();
      
      expect(() => bounds.checkBounds(
        originalSize: 1000,
        compressedSize: 500,
        minRatio: 0.8,
        maxRatio: 0.4, // min > max
      ), throwsArgumentError);
      
      expect(() => bounds.checkBounds(
        originalSize: 1000,
        compressedSize: 500,
        minRatio: -0.1, // negative
        maxRatio: 0.8,
      ), throwsArgumentError);
    });
    
    test('Extreme quality values', () {
      final bounds = CompressionBounds();
      
      // Test with very low quality
      final lowQualityRatio = bounds.estimateRatioForQuality(1);
      expect(lowQualityRatio, greaterThan(0.0));
      expect(lowQualityRatio, lessThan(1.0));
      
      // Test with very high quality
      final highQualityRatio = bounds.estimateRatioForQuality(100);
      expect(highQualityRatio, greaterThan(0.0));
      expect(highQualityRatio, lessThan(1.0));
      
      // High quality should give higher ratio than low quality
      expect(highQualityRatio, greaterThan(lowQualityRatio));
    });
  });
}
