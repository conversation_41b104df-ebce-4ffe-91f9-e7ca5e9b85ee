/// Integration tests cho end-to-end compression workflow
/// 
/// <PERSON><PERSON><PERSON> tra toàn bộ chu trình từ input đến output,
/// bao gồm compression và decompression.
library;

import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_dct_compress/flutter_dct_compress.dart';
import 'dart:typed_data';
import 'dart:math' as math;

void main() {
  group('End-to-End Compression Tests', () {
    late DctCompressor compressor;
    
    setUp(() {
      compressor = DctCompressor();
    });
    
    test('Complete compression and decompression cycle', () async {
      // Create test image data (64x64 RGB)
      final width = 64;
      final height = 64;
      final channels = 3;
      final imageSize = width * height * channels;
      
      final testImageData = Uint8List.fromList(
        List.generate(imageSize, (i) {
          final pixel = i ~/ channels;
          final channel = i % channels;
          final x = pixel % width;
          final y = pixel ~/ width;
          
          // Create a simple gradient pattern
          switch (channel) {
            case 0: return ((x / width) * 255).round(); // Red gradient
            case 1: return ((y / height) * 255).round(); // Green gradient
            case 2: return 128; // Blue constant
            default: return 0;
          }
        })
      );
      
      final imageInfo = ImageInfo(
        width: width,
        height: height,
        channels: channels,
        format: 'RGB',
        bitDepth: 8,
        hasAlpha: false,
        colorSpace: 'RGB',
      );
      
      final originalPixelData = PixelData.fromBytes(imageInfo, testImageData);
      
      // Test compression with different quality settings
      for (final quality in [25, 50, 75, 95]) {
        final options = CompressionOptions.quality(quality);
        
        final result = await compressor.compressImage(
          originalPixelData,
          options: options,
        );
        
        expect(result.isSuccess, isTrue);
        expect(result.compressedData.isNotEmpty, isTrue);
        expect(result.compressionRatio, greaterThan(0.0));
        expect(result.compressionRatio, lessThan(1.0));
        expect(result.qualityUsed, equals(quality));
        
        // Higher quality should result in larger compressed size
        if (quality > 25) {
          // This is a general expectation, though not always guaranteed
          expect(result.compressedSize, greaterThan(0));
        }
        
        // Test decompression
        final decompressedData = await compressor.decompress(result.compressedData);
        expect(decompressedData.info.width, equals(width));
        expect(decompressedData.info.height, equals(height));
        expect(decompressedData.info.channels, equals(channels));
      }
    });
    
    test('Compression with bounds control', () async {
      // Create test image
      final testData = _createTestImage(32, 32, 3);
      
      final options = CompressionOptions.withBounds(
        minRatio: 0.3,
        maxRatio: 0.7,
        quality: 75,
      );
      
      final result = await compressor.compressImage(testData, options: options);
      
      expect(result.isSuccess, isTrue);
      expect(result.compressionRatio, greaterThanOrEqualTo(0.3));
      expect(result.compressionRatio, lessThanOrEqualTo(0.7));
    });
    
    test('Different color space conversions', () async {
      final testData = _createTestImage(16, 16, 3);
      
      // Test RGB color space
      final rgbOptions = CompressionOptions(
        quality: 80,
        colorSpace: ColorSpace.rgb,
      );
      
      final rgbResult = await compressor.compressImage(testData, options: rgbOptions);
      expect(rgbResult.isSuccess, isTrue);
      
      // Test YUV color space
      final yuvOptions = CompressionOptions(
        quality: 80,
        colorSpace: ColorSpace.yuv,
      );
      
      final yuvResult = await compressor.compressImage(testData, options: yuvOptions);
      expect(yuvResult.isSuccess, isTrue);
      
      // YUV typically provides better compression for natural images
      // But for our test pattern, results may vary
      expect(yuvResult.compressionRatio, greaterThan(0.0));
      expect(rgbResult.compressionRatio, greaterThan(0.0));
    });
    
    test('Progressive compression', () async {
      final testData = _createTestImage(32, 32, 3);
      
      final options = CompressionOptions(
        quality: 75,
        progressive: true,
      );
      
      final result = await compressor.compressImage(testData, options: options);
      
      expect(result.isSuccess, isTrue);
      expect(result.compressedData.isNotEmpty, isTrue);
    });
    
    test('Adaptive quantization', () async {
      // Create two different types of images
      final simpleImage = _createSimpleImage(16, 16);
      final complexImage = _createComplexImage(16, 16);
      
      final options = CompressionOptions(
        quality: 75,
        useAdaptiveQuantization: true,
      );
      
      final simpleResult = await compressor.compressImage(simpleImage, options: options);
      final complexResult = await compressor.compressImage(complexImage, options: options);
      
      expect(simpleResult.isSuccess, isTrue);
      expect(complexResult.isSuccess, isTrue);
      expect(simpleResult.usedAdaptiveQuantization, isTrue);
      expect(complexResult.usedAdaptiveQuantization, isTrue);
      
      // Complex images might have different compression characteristics
      expect(simpleResult.compressionRatio, greaterThan(0.0));
      expect(complexResult.compressionRatio, greaterThan(0.0));
    });
    
    test('Error handling for invalid input', () async {
      // Test with empty data
      expect(() async => await compressor.compressImage(Uint8List(0)),
             throwsA(isA<CompressionException>()));
      
      // Test with invalid pixel data
      final invalidImageInfo = ImageInfo(
        width: 0,
        height: 0,
        channels: 0,
        format: 'invalid',
        bitDepth: 8,
        hasAlpha: false,
        colorSpace: 'RGB',
      );
      
      expect(() async => await compressor.compressImage(
        PixelData.fromBytes(invalidImageInfo, Uint8List(0))
      ), throwsA(isA<Exception>()));
    });
    
    test('Memory management with large images', () async {
      // Create a larger test image (128x128)
      final largeImage = _createTestImage(128, 128, 3);
      
      final options = CompressionOptions(
        quality: 75,
        maxImageSizeInMemory: 100 * 1024, // 100KB limit
      );
      
      // This should either succeed or fail gracefully with memory error
      try {
        final result = await compressor.compressImage(largeImage, options: options);
        expect(result.isSuccess, isTrue);
      } catch (e) {
        expect(e, isA<CompressionException>());
        expect(e.toString(), contains('memory'));
      }
    });
  });
  
  group('Quality Metrics Tests', () {
    test('PSNR and SSIM calculation', () async {
      final testData = _createTestImage(32, 32, 3);
      
      final highQualityOptions = CompressionOptions.quality(95);
      final lowQualityOptions = CompressionOptions.quality(25);
      
      final highQualityResult = await compressor.compressImage(
        testData, 
        options: highQualityOptions
      );
      final lowQualityResult = await compressor.compressImage(
        testData, 
        options: lowQualityOptions
      );
      
      // High quality should have better PSNR and SSIM
      if (highQualityResult.psnr != null && lowQualityResult.psnr != null) {
        expect(highQualityResult.psnr!, greaterThan(lowQualityResult.psnr!));
      }
      
      if (highQualityResult.ssim != null && lowQualityResult.ssim != null) {
        expect(highQualityResult.ssim!, greaterThan(lowQualityResult.ssim!));
      }
    });
  });
  
  group('Cross-Platform Consistency Tests', () {
    test('Consistent results across multiple runs', () async {
      final testData = _createTestImage(16, 16, 3);
      final options = CompressionOptions.quality(75);
      
      final results = <CompressionResult>[];
      
      // Run compression multiple times
      for (int i = 0; i < 3; i++) {
        final result = await compressor.compressImage(testData, options: options);
        results.add(result);
      }
      
      // Results should be consistent
      for (int i = 1; i < results.length; i++) {
        expect(results[i].compressedSize, equals(results[0].compressedSize));
        expect(results[i].compressionRatio, closeTo(results[0].compressionRatio, 0.001));
      }
    });
  });
}

/// Helper function to create test image data
PixelData _createTestImage(int width, int height, int channels) {
  final imageSize = width * height * channels;
  final data = Uint8List.fromList(
    List.generate(imageSize, (i) {
      final pixel = i ~/ channels;
      final channel = i % channels;
      final x = pixel % width;
      final y = pixel ~/ width;
      
      // Create a pattern based on position
      return ((x + y + channel) * 37) % 256;
    })
  );
  
  final imageInfo = ImageInfo(
    width: width,
    height: height,
    channels: channels,
    format: 'RGB',
    bitDepth: 8,
    hasAlpha: false,
    colorSpace: 'RGB',
  );
  
  return PixelData.fromBytes(imageInfo, data);
}

/// Helper function to create simple image (low complexity)
PixelData _createSimpleImage(int width, int height) {
  final imageSize = width * height * 3;
  final data = Uint8List.fromList(
    List.generate(imageSize, (i) => 128) // Constant gray
  );
  
  final imageInfo = ImageInfo(
    width: width,
    height: height,
    channels: 3,
    format: 'RGB',
    bitDepth: 8,
    hasAlpha: false,
    colorSpace: 'RGB',
  );
  
  return PixelData.fromBytes(imageInfo, data);
}

/// Helper function to create complex image (high complexity)
PixelData _createComplexImage(int width, int height) {
  final imageSize = width * height * 3;
  final random = math.Random(42); // Fixed seed for reproducibility
  
  final data = Uint8List.fromList(
    List.generate(imageSize, (i) => random.nextInt(256))
  );
  
  final imageInfo = ImageInfo(
    width: width,
    height: height,
    channels: 3,
    format: 'RGB',
    bitDepth: 8,
    hasAlpha: false,
    colorSpace: 'RGB',
  );
  
  return PixelData.fromBytes(imageInfo, data);
}
