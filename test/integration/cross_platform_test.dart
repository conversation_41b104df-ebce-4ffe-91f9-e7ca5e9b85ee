/// Integration tests cho cross-platform compatibility
/// 
/// <PERSON><PERSON><PERSON> tra tính nhất quán và tương thích trên các platform khác nhau:
/// Android, iOS, Web, Desktop.
library;

import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_dct_compress/flutter_dct_compress.dart';
import 'dart:typed_data';
import 'dart:io' show Platform;

void main() {
  group('Cross-Platform Compatibility Tests', () {
    late DctCompressor compressor;
    
    setUp(() {
      compressor = DctCompressor();
    });
    
    test('Platform detection and capabilities', () {
      // Test platform-specific features
      final capabilities = PlatformCapabilities.detect();
      
      expect(capabilities.supportsIsolates, isA<bool>());
      expect(capabilities.supportsFileIO, isA<bool>());
      expect(capabilities.maxMemoryLimit, greaterThan(0));
      expect(capabilities.recommendedConcurrency, greaterThan(0));
      
      // Platform-specific expectations
      if (capabilities.isWeb) {
        // Web platform limitations
        expect(capabilities.supportsFileIO, isFalse);
        expect(capabilities.maxMemoryLimit, lessThan(2 * 1024 * 1024 * 1024)); // < 2GB
      } else if (capabilities.isMobile) {
        // Mobile platform characteristics
        expect(capabilities.recommendedConcurrency, lessThanOrEqualTo(4));
      } else if (capabilities.isDesktop) {
        // Desktop platform capabilities
        expect(capabilities.supportsFileIO, isTrue);
        expect(capabilities.recommendedConcurrency, greaterThanOrEqualTo(2));
      }
    });
    
    test('Consistent compression results across platforms', () async {
      final testData = _createDeterministicTestImage(32, 32, 3);
      final options = CompressionOptions.quality(75);
      
      final result = await compressor.compressImage(testData, options: options);
      
      expect(result.isSuccess, isTrue);
      expect(result.compressedData.isNotEmpty, isTrue);
      
      // Store expected values for cross-platform comparison
      final expectedCompressionRatio = result.compressionRatio;
      final expectedCompressedSize = result.compressedSize;
      
      // Results should be deterministic and consistent
      expect(expectedCompressionRatio, greaterThan(0.0));
      expect(expectedCompressionRatio, lessThan(1.0));
      expect(expectedCompressedSize, greaterThan(0));
      
      // Test decompression consistency
      final decompressed = await compressor.decompress(result.compressedData);
      expect(decompressed.info.width, equals(32));
      expect(decompressed.info.height, equals(32));
      expect(decompressed.info.channels, equals(3));
    });
    
    test('Memory management across platforms', () async {
      final capabilities = PlatformCapabilities.detect();
      
      // Create test image appropriate for platform
      final imageSize = capabilities.isMobile ? 64 : 128; // Smaller on mobile
      final testData = _createTestImage(imageSize, imageSize, 3);
      
      final options = CompressionOptions(
        quality: 75,
        maxImageSizeInMemory: capabilities.maxMemoryLimit ~/ 10, // Use 10% of available memory
      );
      
      final result = await compressor.compressImage(testData, options: options);
      
      expect(result.isSuccess, isTrue);
      expect(result.memoryUsed, lessThanOrEqualTo(capabilities.maxMemoryLimit));
    });
    
    test('Isolate support across platforms', () async {
      final capabilities = PlatformCapabilities.detect();
      final testData = _createTestImage(16, 16, 3);
      
      if (capabilities.supportsIsolates) {
        // Test isolate-based compression
        final isolateOptions = CompressionOptions(
          quality: 75,
          useIsolate: true,
        );
        
        final isolateResult = await compressor.compressImage(
          testData, 
          options: isolateOptions
        );
        
        expect(isolateResult.isSuccess, isTrue);
        expect(isolateResult.usedIsolate, isTrue);
        
        // Compare with main thread compression
        final mainThreadOptions = CompressionOptions(
          quality: 75,
          useIsolate: false,
        );
        
        final mainThreadResult = await compressor.compressImage(
          testData, 
          options: mainThreadOptions
        );
        
        expect(mainThreadResult.isSuccess, isTrue);
        expect(mainThreadResult.usedIsolate, isFalse);
        
        // Results should be similar
        expect(isolateResult.compressionRatio, 
               closeTo(mainThreadResult.compressionRatio, 0.05));
      } else {
        // Platform doesn't support isolates
        final options = CompressionOptions(
          quality: 75,
          useIsolate: true, // Request isolate but should fallback
        );
        
        final result = await compressor.compressImage(testData, options: options);
        
        expect(result.isSuccess, isTrue);
        expect(result.usedIsolate, isFalse); // Should fallback to main thread
      }
    });
    
    test('File I/O support across platforms', () async {
      final capabilities = PlatformCapabilities.detect();
      
      if (capabilities.supportsFileIO) {
        // Test file-based operations
        final testData = _createTestImage(16, 16, 3);
        
        // This would test actual file operations in a real implementation
        // For now, we'll test the capability detection
        expect(capabilities.supportsFileIO, isTrue);
        
        // Test path validation
        expect(FileIO.isValidPath('test.jpg'), isTrue);
        expect(FileIO.isValidPath('test.png'), isTrue);
        expect(FileIO.isValidPath(''), isFalse);
      } else {
        // Web platform or restricted environment
        expect(capabilities.supportsFileIO, isFalse);
        
        // Should handle file operations gracefully
        expect(() => FileIO.isValidPath('test.jpg'), 
               returnsNormally); // Should not throw
      }
    });
    
    test('Performance characteristics by platform', () async {
      final capabilities = PlatformCapabilities.detect();
      final testData = _createTestImage(32, 32, 3);
      
      final startTime = DateTime.now();
      
      final result = await compressor.compressImage(
        testData,
        options: CompressionOptions.quality(75),
      );
      
      final processingTime = DateTime.now().difference(startTime);
      
      expect(result.isSuccess, isTrue);
      
      // Platform-specific performance expectations
      if (capabilities.isMobile) {
        // Mobile devices might be slower
        expect(processingTime.inMilliseconds, lessThan(5000)); // 5 seconds max
      } else if (capabilities.isDesktop) {
        // Desktop should be faster
        expect(processingTime.inMilliseconds, lessThan(2000)); // 2 seconds max
      } else if (capabilities.isWeb) {
        // Web performance varies
        expect(processingTime.inMilliseconds, lessThan(10000)); // 10 seconds max
      }
    });
    
    test('Concurrent processing limits by platform', () async {
      final capabilities = PlatformCapabilities.detect();
      final inputs = <PixelData>[];
      
      // Create test batch
      for (int i = 0; i < 10; i++) {
        inputs.add(_createTestImage(8, 8, 3, seed: i));
      }
      
      final batchProcessor = BatchProcessor();
      
      final result = await batchProcessor.processBatch(
        inputs,
        options: CompressionOptions.quality(75),
        maxConcurrency: capabilities.recommendedConcurrency,
      );
      
      expect(result.isCompleteSuccess, isTrue);
      expect(result.totalProcessingTime.inSeconds, lessThan(30));
    });
    
    test('Error handling consistency across platforms', () async {
      // Test with invalid data
      final invalidData = Uint8List(0);
      
      try {
        await compressor.compressImage(invalidData);
        fail('Should have thrown an exception');
      } catch (e) {
        expect(e, isA<CompressionException>());
        expect(e.toString(), contains('Invalid'));
      }
      
      // Test with oversized data (if memory limited)
      final capabilities = PlatformCapabilities.detect();
      
      if (capabilities.maxMemoryLimit < 100 * 1024 * 1024) { // Less than 100MB
        final oversizedData = _createTestImage(1000, 1000, 3); // Large image
        
        try {
          final result = await compressor.compressImage(
            oversizedData,
            options: CompressionOptions(
              quality: 75,
              maxImageSizeInMemory: 1024, // Very small limit
            ),
          );
          
          // Should either succeed or fail gracefully
          if (!result.isSuccess) {
            expect(result.errorMessage, contains('memory'));
          }
        } catch (e) {
          expect(e, isA<CompressionException>());
          expect(e.toString(), contains('memory'));
        }
      }
    });
    
    test('Format support consistency', () {
      // All platforms should support basic formats
      expect(FormatHandler.isFormatSupported(ImageFormat.jpeg), isTrue);
      expect(FormatHandler.isFormatSupported(ImageFormat.png), isTrue);
      
      // Test format detection consistency
      final jpegBytes = Uint8List.fromList([0xFF, 0xD8, 0xFF, 0xE0]);
      expect(FormatHandler.detectFormat(jpegBytes), equals(ImageFormat.jpeg));
      
      final pngBytes = Uint8List.fromList([
        0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A
      ]);
      expect(FormatHandler.detectFormat(pngBytes), equals(ImageFormat.png));
    });
  });
  
  group('Platform-Specific Feature Tests', () {
    test('Web-specific limitations', () {
      final capabilities = PlatformCapabilities.detect();
      
      if (capabilities.isWeb) {
        // Web platform should handle limitations gracefully
        expect(capabilities.supportsFileIO, isFalse);
        
        // Should not attempt file operations
        expect(() => FileIO.validatePath('/invalid/path'), 
               throwsA(isA<UnsupportedError>()));
      }
    });
    
    test('Mobile-specific optimizations', () {
      final capabilities = PlatformCapabilities.detect();
      
      if (capabilities.isMobile) {
        // Mobile should use conservative settings
        expect(capabilities.recommendedConcurrency, lessThanOrEqualTo(4));
        expect(capabilities.maxMemoryLimit, lessThan(4 * 1024 * 1024 * 1024)); // < 4GB
        
        // Should prefer lower memory usage
        final options = CompressionOptions.mobileOptimized();
        expect(options.maxImageSizeInMemory, lessThan(50 * 1024 * 1024)); // < 50MB
      }
    });
    
    test('Desktop-specific capabilities', () {
      final capabilities = PlatformCapabilities.detect();
      
      if (capabilities.isDesktop) {
        // Desktop should support full features
        expect(capabilities.supportsFileIO, isTrue);
        expect(capabilities.supportsIsolates, isTrue);
        expect(capabilities.recommendedConcurrency, greaterThanOrEqualTo(2));
        
        // Should handle larger images
        final options = CompressionOptions.desktopOptimized();
        expect(options.maxImageSizeInMemory, greaterThan(100 * 1024 * 1024)); // > 100MB
      }
    });
  });
}

/// Helper function to create deterministic test image
PixelData _createDeterministicTestImage(int width, int height, int channels) {
  final imageSize = width * height * channels;
  final data = Uint8List.fromList(
    List.generate(imageSize, (i) {
      final pixel = i ~/ channels;
      final channel = i % channels;
      final x = pixel % width;
      final y = pixel ~/ width;
      
      // Deterministic pattern for cross-platform consistency
      return ((x * 17 + y * 23 + channel * 7) % 256);
    })
  );
  
  final imageInfo = ImageInfo(
    width: width,
    height: height,
    channels: channels,
    format: 'RGB',
    bitDepth: 8,
    hasAlpha: false,
    colorSpace: 'RGB',
  );
  
  return PixelData.fromBytes(imageInfo, data);
}

/// Helper function to create test image with seed
PixelData _createTestImage(int width, int height, int channels, {int seed = 0}) {
  final imageSize = width * height * channels;
  final data = Uint8List.fromList(
    List.generate(imageSize, (i) {
      final pixel = i ~/ channels;
      final channel = i % channels;
      final x = pixel % width;
      final y = pixel ~/ width;
      
      return ((x + y + channel + seed) * 37) % 256;
    })
  );
  
  final imageInfo = ImageInfo(
    width: width,
    height: height,
    channels: channels,
    format: 'RGB',
    bitDepth: 8,
    hasAlpha: false,
    colorSpace: 'RGB',
  );
  
  return PixelData.fromBytes(imageInfo, data);
}
