/// Integration tests cho batch processing
/// 
/// <PERSON><PERSON><PERSON> tra xử lý hàng loạt nhiều ảnh với progress tracking,
/// cancellation, và error handling.
library;

import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_dct_compress/flutter_dct_compress.dart';
import 'dart:typed_data';
import 'dart:async';

void main() {
  group('Batch Processing Tests', () {
    late BatchProcessor batchProcessor;
    
    setUp(() {
      batchProcessor = BatchProcessor();
    });
    
    test('Basic batch processing', () async {
      // Create multiple test images
      final inputs = <PixelData>[];
      for (int i = 0; i < 5; i++) {
        inputs.add(_createTestImage(16, 16, 3, seed: i));
      }
      
      final options = CompressionOptions.quality(75);
      
      final result = await batchProcessor.processBatch(
        inputs,
        options: options,
      );
      
      expect(result.totalCount, equals(5));
      expect(result.successCount, equals(5));
      expect(result.failureCount, equals(0));
      expect(result.cancelledCount, equals(0));
      expect(result.isCompleteSuccess, isTrue);
      expect(result.successRate, equals(1.0));
      
      // Check individual results
      for (int i = 0; i < result.results.length; i++) {
        final individualResult = result.results[i];
        expect(individualResult, isNotNull);
        expect(individualResult!.isSuccess, isTrue);
        expect(individualResult.compressedData.isNotEmpty, isTrue);
      }
    });
    
    test('Batch processing with progress tracking', () async {
      final inputs = <PixelData>[];
      for (int i = 0; i < 10; i++) {
        inputs.add(_createTestImage(8, 8, 3, seed: i));
      }
      
      final progressUpdates = <int>[];
      final itemCompletions = <int>[];
      
      final result = await batchProcessor.processBatch(
        inputs,
        options: CompressionOptions.quality(75),
        onProgress: (completed, total, currentItem) {
          progressUpdates.add(completed);
          expect(completed, lessThanOrEqualTo(total));
          expect(total, equals(10));
        },
        onItemComplete: (index, result) {
          itemCompletions.add(index);
          expect(result.isSuccess, isTrue);
        },
      );
      
      expect(result.isCompleteSuccess, isTrue);
      expect(progressUpdates.isNotEmpty, isTrue);
      expect(itemCompletions.length, equals(10));
      
      // Progress should be monotonically increasing
      for (int i = 1; i < progressUpdates.length; i++) {
        expect(progressUpdates[i], greaterThanOrEqualTo(progressUpdates[i - 1]));
      }
    });
    
    test('Batch processing with cancellation', () async {
      final inputs = <PixelData>[];
      for (int i = 0; i < 20; i++) {
        inputs.add(_createTestImage(16, 16, 3, seed: i));
      }
      
      final cancellationToken = CancellationToken();
      
      // Cancel after a short delay
      Timer(const Duration(milliseconds: 100), () {
        cancellationToken.cancel();
      });
      
      final result = await batchProcessor.processBatch(
        inputs,
        options: CompressionOptions.quality(75),
        cancellationToken: cancellationToken,
      );
      
      expect(result.wasCancelled, isTrue);
      expect(result.cancelledCount, greaterThan(0));
      expect(result.successCount + result.failureCount + result.cancelledCount, 
             equals(result.totalCount));
    });
    
    test('Batch processing with mixed success and failure', () async {
      final inputs = <dynamic>[];
      
      // Add valid inputs
      for (int i = 0; i < 3; i++) {
        inputs.add(_createTestImage(8, 8, 3, seed: i));
      }
      
      // Add invalid inputs that should cause errors
      inputs.add(Uint8List(0)); // Empty data
      inputs.add("invalid_string"); // Invalid type
      
      final errors = <String>[];
      
      final result = await batchProcessor.processBatch(
        inputs,
        options: CompressionOptions.quality(75),
        onError: (index, error) {
          errors.add(error);
        },
      );
      
      expect(result.totalCount, equals(5));
      expect(result.successCount, equals(3));
      expect(result.failureCount, equals(2));
      expect(errors.length, equals(2));
      expect(result.successRate, equals(0.6));
    });
    
    test('Batch processing with concurrency control', () async {
      final inputs = <PixelData>[];
      for (int i = 0; i < 15; i++) {
        inputs.add(_createTestImage(8, 8, 3, seed: i));
      }
      
      final startTime = DateTime.now();
      
      // Test with different concurrency levels
      final result1 = await batchProcessor.processBatch(
        inputs,
        options: CompressionOptions.quality(75),
        maxConcurrency: 1, // Sequential processing
      );
      
      final sequentialTime = DateTime.now().difference(startTime);
      
      final startTime2 = DateTime.now();
      
      final result2 = await batchProcessor.processBatch(
        inputs,
        options: CompressionOptions.quality(75),
        maxConcurrency: 5, // Parallel processing
      );
      
      final parallelTime = DateTime.now().difference(startTime2);
      
      expect(result1.isCompleteSuccess, isTrue);
      expect(result2.isCompleteSuccess, isTrue);
      
      // Parallel processing should generally be faster
      // (though this might not always be true for small test images)
      expect(parallelTime.inMilliseconds, lessThanOrEqualTo(
        sequentialTime.inMilliseconds * 2 // Allow some variance
      ));
    });
    
    test('Batch processing time estimation', () async {
      final inputs = <PixelData>[];
      for (int i = 0; i < 5; i++) {
        inputs.add(_createTestImage(16, 16, 3, seed: i));
      }
      
      final estimatedTime = await batchProcessor.estimateBatchProcessingTime(
        inputs,
        CompressionOptions.quality(75),
      );
      
      expect(estimatedTime.inMilliseconds, greaterThan(0));
      
      final startTime = DateTime.now();
      
      final result = await batchProcessor.processBatch(
        inputs,
        options: CompressionOptions.quality(75),
      );
      
      final actualTime = DateTime.now().difference(startTime);
      
      expect(result.isCompleteSuccess, isTrue);
      
      // Estimation should be reasonably close to actual time
      // Allow significant variance for test environment
      expect(actualTime.inMilliseconds, 
             lessThanOrEqualTo(estimatedTime.inMilliseconds * 5));
    });
    
    test('Batch processing with different options per item', () async {
      final inputs = <PixelData>[];
      for (int i = 0; i < 3; i++) {
        inputs.add(_createTestImage(16, 16, 3, seed: i));
      }
      
      // Process with different quality settings
      final results = <CompressionResult>[];
      
      for (int i = 0; i < inputs.length; i++) {
        final quality = 25 + (i * 25); // 25, 50, 75
        final options = CompressionOptions.quality(quality);
        
        final batchResult = await batchProcessor.processBatch(
          [inputs[i]],
          options: options,
        );
        
        expect(batchResult.isCompleteSuccess, isTrue);
        results.add(batchResult.results[0]!);
      }
      
      // Verify different quality settings were applied
      for (int i = 0; i < results.length; i++) {
        final expectedQuality = 25 + (i * 25);
        expect(results[i].qualityUsed, equals(expectedQuality));
      }
    });
    
    test('Batch processing memory management', () async {
      // Create a larger batch to test memory management
      final inputs = <PixelData>[];
      for (int i = 0; i < 50; i++) {
        inputs.add(_createTestImage(32, 32, 3, seed: i));
      }
      
      final options = CompressionOptions(
        quality: 75,
        maxImageSizeInMemory: 10 * 1024, // Small memory limit
      );
      
      final result = await batchProcessor.processBatch(
        inputs,
        options: options,
        maxConcurrency: 2,
      );
      
      // Should handle memory constraints gracefully
      expect(result.totalCount, equals(50));
      expect(result.successCount + result.failureCount + result.cancelledCount,
             equals(50));
    });
    
    test('Batch processing error recovery', () async {
      final inputs = <dynamic>[];
      
      // Mix of valid and invalid inputs
      inputs.add(_createTestImage(8, 8, 3, seed: 1));
      inputs.add(Uint8List(0)); // Invalid
      inputs.add(_createTestImage(8, 8, 3, seed: 2));
      inputs.add("invalid"); // Invalid
      inputs.add(_createTestImage(8, 8, 3, seed: 3));
      
      final result = await batchProcessor.processBatch(
        inputs,
        options: CompressionOptions.quality(75),
      );
      
      expect(result.totalCount, equals(5));
      expect(result.successCount, equals(3));
      expect(result.failureCount, equals(2));
      
      // Check that valid items were processed successfully
      expect(result.results[0], isNotNull);
      expect(result.results[0]!.isSuccess, isTrue);
      expect(result.results[1], isNull); // Failed
      expect(result.results[2], isNotNull);
      expect(result.results[2]!.isSuccess, isTrue);
      expect(result.results[3], isNull); // Failed
      expect(result.results[4], isNotNull);
      expect(result.results[4]!.isSuccess, isTrue);
    });
  });
  
  group('Batch Processing Performance Tests', () {
    test('Large batch processing', () async {
      final inputs = <PixelData>[];
      for (int i = 0; i < 100; i++) {
        inputs.add(_createTestImage(8, 8, 1, seed: i)); // Grayscale for speed
      }
      
      final startTime = DateTime.now();
      
      final result = await batchProcessor.processBatch(
        inputs,
        options: CompressionOptions(
          quality: 50, // Lower quality for speed
          useIsolate: false, // Disable isolate for simpler testing
        ),
        maxConcurrency: 5,
      );
      
      final processingTime = DateTime.now().difference(startTime);
      
      expect(result.isCompleteSuccess, isTrue);
      expect(processingTime.inSeconds, lessThan(30)); // Should complete in reasonable time
      
      // Check processing rate
      final itemsPerSecond = result.totalCount / processingTime.inSeconds;
      expect(itemsPerSecond, greaterThan(1.0)); // At least 1 item per second
    });
  });
}

/// Helper function to create test image data with optional seed
PixelData _createTestImage(int width, int height, int channels, {int seed = 0}) {
  final imageSize = width * height * channels;
  final data = Uint8List.fromList(
    List.generate(imageSize, (i) {
      final pixel = i ~/ channels;
      final channel = i % channels;
      final x = pixel % width;
      final y = pixel ~/ width;
      
      // Create a pattern based on position and seed
      return ((x + y + channel + seed) * 37) % 256;
    })
  );
  
  final imageInfo = ImageInfo(
    width: width,
    height: height,
    channels: channels,
    format: 'RGB',
    bitDepth: 8,
    hasAlpha: false,
    colorSpace: 'RGB',
  );
  
  return PixelData.fromBytes(imageInfo, data);
}
