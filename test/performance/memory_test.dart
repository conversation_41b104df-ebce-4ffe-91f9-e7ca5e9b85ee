/// Memory usage and management tests
/// 
/// <PERSON><PERSON><PERSON> tra memory consumption, memory leaks,
/// và memory management strategies.
library;

import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_dct_compress/flutter_dct_compress.dart';
import 'dart:typed_data';
import 'dart:math' as math;

void main() {
  group('Memory Usage Tests', () {
    test('Memory consumption by image size', () async {
      final compressor = DctCompressor();
      final memoryManager = MemoryManager();
      
      final sizes = [16, 32, 64, 128];
      final memoryUsages = <int, int>{};
      
      for (final size in sizes) {
        final testData = _createTestImage(size, size, 3);
        
        // Get initial memory state
        final initialMemory = memoryManager.getCurrentMemoryUsage();
        
        final result = await compressor.compressImage(
          testData,
          options: CompressionOptions.quality(75),
        );
        
        expect(result.isSuccess, isTrue);
        
        // Calculate memory usage
        final finalMemory = memoryManager.getCurrentMemoryUsage();
        final memoryUsed = finalMemory - initialMemory;
        memoryUsages[size] = memoryUsed;
        
        print('${size}x$size image: ${memoryUsed} bytes memory used');
        
        // Memory usage should scale roughly with image size
        final expectedMemory = size * size * 3; // Minimum expected
        expect(memoryUsed, greaterThan(expectedMemory ~/ 2)); // At least half
        expect(memoryUsed, lessThan(expectedMemory * 20)); // Not more than 20x
      }
      
      // Larger images should use more memory
      expect(memoryUsages[128]!, greaterThan(memoryUsages[16]!));
    });
    
    test('Memory limits enforcement', () async {
      final compressor = DctCompressor();
      final testData = _createTestImage(128, 128, 3);
      
      // Test with very restrictive memory limit
      final restrictiveOptions = CompressionOptions(
        quality: 75,
        maxImageSizeInMemory: 1024, // Very small limit
      );
      
      try {
        final result = await compressor.compressImage(
          testData,
          options: restrictiveOptions,
        );
        
        // Should either succeed with memory management or fail gracefully
        if (result.isSuccess) {
          expect(result.memoryUsed, lessThanOrEqualTo(1024 * 10)); // Allow some overhead
        } else {
          expect(result.errorMessage, contains('memory'));
        }
      } catch (e) {
        expect(e, isA<CompressionException>());
        expect(e.toString(), contains('memory'));
      }
    });
    
    test('Memory cleanup after compression', () async {
      final compressor = DctCompressor();
      final memoryManager = MemoryManager();
      
      final initialMemory = memoryManager.getCurrentMemoryUsage();
      
      // Process multiple images
      for (int i = 0; i < 10; i++) {
        final testData = _createTestImage(32, 32, 3, seed: i);
        
        final result = await compressor.compressImage(
          testData,
          options: CompressionOptions.quality(75),
        );
        
        expect(result.isSuccess, isTrue);
        
        // Trigger cleanup
        memoryManager.autoCleanupIfNeeded();
      }
      
      // Force garbage collection simulation
      await Future.delayed(const Duration(milliseconds: 100));
      
      final finalMemory = memoryManager.getCurrentMemoryUsage();
      final memoryGrowth = finalMemory - initialMemory;
      
      print('Memory growth after 10 compressions: $memoryGrowth bytes');
      
      // Memory growth should be reasonable (not indicating major leaks)
      expect(memoryGrowth, lessThan(10 * 1024 * 1024)); // < 10MB growth
    });
    
    test('Memory manager allocation tracking', () {
      final memoryManager = MemoryManager();
      
      // Test allocation checking
      expect(memoryManager.canAllocateForImage(100, 100, 3), isTrue);
      expect(memoryManager.canAllocateForImage(10000, 10000, 3), isFalse);
      
      // Test memory estimation
      final estimated = memoryManager.estimateMemoryForImage(64, 64, 3);
      expect(estimated, greaterThan(0));
      expect(estimated, equals(64 * 64 * 3 * 4)); // 4 bytes per pixel estimate
      
      // Test memory limits
      final currentUsage = memoryManager.getCurrentMemoryUsage();
      expect(currentUsage, greaterThanOrEqualTo(0));
      
      final availableMemory = memoryManager.getAvailableMemory();
      expect(availableMemory, greaterThan(0));
    });
    
    test('Batch processing memory management', () async {
      final batchProcessor = BatchProcessor();
      final inputs = <PixelData>[];
      
      // Create batch of images
      for (int i = 0; i < 20; i++) {
        inputs.add(_createTestImage(32, 32, 3, seed: i));
      }
      
      final memoryManager = MemoryManager();
      final initialMemory = memoryManager.getCurrentMemoryUsage();
      
      final result = await batchProcessor.processBatch(
        inputs,
        options: CompressionOptions(
          quality: 75,
          maxImageSizeInMemory: 50 * 1024, // 50KB per image limit
        ),
        maxConcurrency: 3,
      );
      
      expect(result.successCount, greaterThan(0));
      
      final finalMemory = memoryManager.getCurrentMemoryUsage();
      final memoryUsed = finalMemory - initialMemory;
      
      print('Batch processing memory usage: $memoryUsed bytes');
      
      // Memory usage should be controlled
      expect(memoryUsed, lessThan(100 * 1024 * 1024)); // < 100MB
    });
  });
  
  group('Memory Leak Detection Tests', () {
    test('Repeated compression memory stability', () async {
      final compressor = DctCompressor();
      final memoryManager = MemoryManager();
      final testData = _createTestImage(64, 64, 3);
      
      final memoryReadings = <int>[];
      
      // Perform multiple compressions
      for (int i = 0; i < 50; i++) {
        final result = await compressor.compressImage(
          testData,
          options: CompressionOptions.quality(75),
        );
        
        expect(result.isSuccess, isTrue);
        
        // Record memory usage every 10 iterations
        if (i % 10 == 0) {
          memoryReadings.add(memoryManager.getCurrentMemoryUsage());
        }
        
        // Trigger cleanup periodically
        if (i % 5 == 0) {
          memoryManager.autoCleanupIfNeeded();
        }
      }
      
      print('Memory readings: $memoryReadings');
      
      // Memory should stabilize (not grow indefinitely)
      if (memoryReadings.length >= 3) {
        final firstReading = memoryReadings[0];
        final lastReading = memoryReadings.last;
        final growth = lastReading - firstReading;
        
        // Allow some growth but not excessive
        expect(growth, lessThan(50 * 1024 * 1024)); // < 50MB growth
      }
    });
    
    test('DCT transform memory stability', () {
      final testBlock = List.generate(8, (i) =>
        List.generate(8, (j) => (i * 8 + j).toDouble())
      );
      
      final memoryManager = MemoryManager();
      final initialMemory = memoryManager.getCurrentMemoryUsage();
      
      // Perform many DCT operations
      for (int i = 0; i < 10000; i++) {
        final result = DctTransform.forwardDct(testBlock);
        DctTransform.inverseDct(result);
        
        // Periodic cleanup
        if (i % 1000 == 0) {
          memoryManager.autoCleanupIfNeeded();
        }
      }
      
      final finalMemory = memoryManager.getCurrentMemoryUsage();
      final memoryGrowth = finalMemory - initialMemory;
      
      print('DCT operations memory growth: $memoryGrowth bytes');
      
      // Should not have significant memory growth
      expect(memoryGrowth, lessThan(1024 * 1024)); // < 1MB growth
    });
    
    test('Isolate memory isolation', () async {
      final compressor = DctCompressor();
      final testData = _createTestImage(64, 64, 3);
      
      // Test isolate-based compression
      final isolateOptions = CompressionOptions(
        quality: 75,
        useIsolate: true,
      );
      
      final memoryManager = MemoryManager();
      final initialMemory = memoryManager.getCurrentMemoryUsage();
      
      // Process multiple images with isolates
      for (int i = 0; i < 10; i++) {
        final result = await compressor.compressImage(
          testData,
          options: isolateOptions,
        );
        
        // Should succeed or gracefully handle isolate limitations
        if (result.isSuccess) {
          expect(result.usedIsolate, isTrue);
        }
      }
      
      final finalMemory = memoryManager.getCurrentMemoryUsage();
      final memoryGrowth = finalMemory - initialMemory;
      
      print('Isolate processing memory growth: $memoryGrowth bytes');
      
      // Isolate processing should have controlled memory usage
      expect(memoryGrowth, lessThan(20 * 1024 * 1024)); // < 20MB growth
    });
  });
  
  group('Memory Optimization Tests', () {
    test('Memory-optimized compression settings', () async {
      final compressor = DctCompressor();
      final testData = _createTestImage(128, 128, 3);
      
      // Test memory-optimized options
      final optimizedOptions = CompressionOptions(
        quality: 75,
        maxImageSizeInMemory: 32 * 1024, // 32KB limit
        useStreamingCompression: true,
        enableMemoryOptimization: true,
      );
      
      final memoryManager = MemoryManager();
      final initialMemory = memoryManager.getCurrentMemoryUsage();
      
      final result = await compressor.compressImage(
        testData,
        options: optimizedOptions,
      );
      
      final finalMemory = memoryManager.getCurrentMemoryUsage();
      final memoryUsed = finalMemory - initialMemory;
      
      print('Memory-optimized compression used: $memoryUsed bytes');
      
      // Should either succeed with low memory usage or fail gracefully
      if (result.isSuccess) {
        expect(memoryUsed, lessThan(1024 * 1024)); // < 1MB
      } else {
        expect(result.errorMessage, isNotNull);
      }
    });
    
    test('Progressive memory allocation', () async {
      final compressor = DctCompressor();
      final memoryManager = MemoryManager();
      
      // Test with progressively larger images
      final sizes = [16, 32, 64, 128];
      
      for (final size in sizes) {
        final testData = _createTestImage(size, size, 3);
        
        final beforeMemory = memoryManager.getCurrentMemoryUsage();
        
        final result = await compressor.compressImage(
          testData,
          options: CompressionOptions.quality(75),
        );
        
        final afterMemory = memoryManager.getCurrentMemoryUsage();
        final memoryDelta = afterMemory - beforeMemory;
        
        print('${size}x$size: $memoryDelta bytes allocated');
        
        if (result.isSuccess) {
          // Memory allocation should be reasonable
          final imageSize = size * size * 3;
          expect(memoryDelta, lessThan(imageSize * 50)); // < 50x image size
        }
        
        // Cleanup between tests
        memoryManager.autoCleanupIfNeeded();
      }
    });
  });
}

/// Helper function to create test image data
PixelData _createTestImage(int width, int height, int channels, {int seed = 0}) {
  final imageSize = width * height * channels;
  final random = math.Random(seed);
  
  final data = Uint8List.fromList(
    List.generate(imageSize, (i) {
      return (random.nextInt(256) + i) % 256;
    })
  );
  
  final imageInfo = ImageInfo(
    width: width,
    height: height,
    channels: channels,
    format: 'RGB',
    bitDepth: 8,
    hasAlpha: false,
    colorSpace: 'RGB',
  );
  
  return PixelData.fromBytes(imageInfo, data);
}
