/// Isolate performance tests
/// 
/// <PERSON><PERSON><PERSON> tra hiệu năng của isolate-based processing,
/// communication overhead, và scalability.
library;

import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_dct_compress/flutter_dct_compress.dart';
import 'dart:typed_data';
import 'dart:math' as math;
import 'dart:async';

void main() {
  group('Isolate Performance Tests', () {
    test('Isolate vs main thread performance comparison', () async {
      final compressor = DctCompressor();
      final testData = _createTestImage(64, 64, 3);
      
      // Test main thread compression
      final mainThreadStopwatch = Stopwatch()..start();
      final mainThreadResult = await compressor.compressImage(
        testData,
        options: CompressionOptions(quality: 75, useIsolate: false),
      );
      mainThreadStopwatch.stop();
      
      // Test isolate compression
      final isolateStopwatch = Stopwatch()..start();
      final isolateResult = await compressor.compressImage(
        testData,
        options: CompressionOptions(quality: 75, useIsolate: true),
      );
      isolateStopwatch.stop();
      
      expect(mainThreadResult.isSuccess, isTrue);
      
      print('Main thread: ${mainThreadStopwatch.elapsedMilliseconds}ms');
      print('Isolate: ${isolateStopwatch.elapsedMilliseconds}ms');
      
      // Both should succeed (or isolate should fail gracefully if not supported)
      if (isolateResult.isSuccess) {
        expect(isolateResult.usedIsolate, isTrue);
        
        // Isolate overhead should be reasonable
        final overhead = isolateStopwatch.elapsedMilliseconds - 
                        mainThreadStopwatch.elapsedMilliseconds;
        expect(overhead, lessThan(5000)); // < 5 seconds overhead
      } else {
        // Platform might not support isolates
        expect(isolateResult.errorMessage, contains('isolate'));
      }
    });
    
    test('Isolate communication overhead', () async {
      final isolateUtils = IsolateUtils();
      
      // Test data serialization performance
      final testData = Uint8List.fromList(
        List.generate(1024 * 1024, (i) => i % 256) // 1MB data
      );
      
      final serializationStopwatch = Stopwatch()..start();
      final serialized = IsolateUtils.serializeUint8List(testData);
      serializationStopwatch.stop();
      
      final deserializationStopwatch = Stopwatch()..start();
      final deserialized = IsolateUtils.deserializeUint8List(serialized);
      deserializationStopwatch.stop();
      
      expect(deserialized, equals(testData));
      
      print('Serialization (1MB): ${serializationStopwatch.elapsedMicroseconds}μs');
      print('Deserialization (1MB): ${deserializationStopwatch.elapsedMicroseconds}μs');
      
      // Should be fast
      expect(serializationStopwatch.elapsedMilliseconds, lessThan(100));
      expect(deserializationStopwatch.elapsedMilliseconds, lessThan(100));
      
      // Test object serialization
      final complexObject = {
        'width': 1920,
        'height': 1080,
        'quality': 75,
        'options': ['progressive', 'adaptive'],
        'metadata': {'created': DateTime.now().toIso8601String()},
      };
      
      final objectSerializationStopwatch = Stopwatch()..start();
      final serializedObject = IsolateUtils.serializeObject(complexObject);
      objectSerializationStopwatch.stop();
      
      final objectDeserializationStopwatch = Stopwatch()..start();
      final deserializedObject = IsolateUtils.deserializeObject(serializedObject);
      objectDeserializationStopwatch.stop();
      
      expect(deserializedObject['width'], equals(1920));
      expect(deserializedObject['height'], equals(1080));
      
      print('Object serialization: ${objectSerializationStopwatch.elapsedMicroseconds}μs');
      print('Object deserialization: ${objectDeserializationStopwatch.elapsedMicroseconds}μs');
    });
    
    test('Isolate pool performance', () async {
      final isolatePool = IsolatePool(maxIsolates: 3);
      
      try {
        await isolatePool.initialize();
        
        // Create test work items
        final workItems = <TestWorkItem>[];
        for (int i = 0; i < 10; i++) {
          workItems.add(TestWorkItem(
            id: 'work_$i',
            data: {'index': i, 'size': 32},
          ));
        }
        
        final stopwatch = Stopwatch()..start();
        
        // Submit all work items
        final futures = workItems.map((item) => isolatePool.submit(item)).toList();
        
        // Wait for completion
        final results = await Future.wait(futures);
        
        stopwatch.stop();
        
        expect(results.length, equals(10));
        for (final result in results) {
          expect(result, isA<String>());
        }
        
        print('Isolate pool (10 items): ${stopwatch.elapsedMilliseconds}ms');
        
        // Should complete in reasonable time
        expect(stopwatch.elapsedSeconds, lessThan(30));
        
        // Check pool statistics
        final stats = isolatePool.getStats();
        expect(stats.totalWorkers, equals(3));
        expect(stats.queuedItems, equals(0)); // All should be completed
        
      } finally {
        await isolatePool.dispose();
      }
    });
    
    test('Isolate scalability with different worker counts', () async {
      final workerCounts = [1, 2, 4];
      final results = <int, Duration>{};
      
      for (final workerCount in workerCounts) {
        final isolatePool = IsolatePool(maxIsolates: workerCount);
        
        try {
          await isolatePool.initialize();
          
          // Create work items
          final workItems = <TestWorkItem>[];
          for (int i = 0; i < 20; i++) {
            workItems.add(TestWorkItem(
              id: 'work_$i',
              data: {'index': i, 'delay': 100}, // 100ms simulated work
            ));
          }
          
          final stopwatch = Stopwatch()..start();
          
          final futures = workItems.map((item) => isolatePool.submit(item)).toList();
          await Future.wait(futures);
          
          stopwatch.stop();
          
          results[workerCount] = stopwatch.elapsed;
          
          print('$workerCount workers: ${stopwatch.elapsedMilliseconds}ms');
          
        } finally {
          await isolatePool.dispose();
        }
      }
      
      // More workers should generally be faster (up to a point)
      if (results.containsKey(1) && results.containsKey(4)) {
        expect(results[4]!.inMilliseconds, 
               lessThanOrEqualTo(results[1]!.inMilliseconds));
      }
    });
    
    test('Isolate memory isolation', () async {
      final isolatePool = IsolatePool(maxIsolates: 2);
      
      try {
        await isolatePool.initialize();
        
        // Create memory-intensive work items
        final workItems = <MemoryTestWorkItem>[];
        for (int i = 0; i < 5; i++) {
          workItems.add(MemoryTestWorkItem(
            id: 'memory_$i',
            data: {'size': 1024 * 1024}, // 1MB allocation
          ));
        }
        
        final futures = workItems.map((item) => isolatePool.submit(item)).toList();
        final results = await Future.wait(futures);
        
        expect(results.length, equals(5));
        
        // All should complete successfully (memory should be isolated)
        for (final result in results) {
          expect(result, isA<String>());
          expect(result, contains('completed'));
        }
        
      } finally {
        await isolatePool.dispose();
      }
    });
    
    test('Isolate error handling and recovery', () async {
      final isolatePool = IsolatePool(maxIsolates: 2);
      
      try {
        await isolatePool.initialize();
        
        // Mix of successful and failing work items
        final workItems = <WorkItem>[];
        workItems.add(TestWorkItem(id: 'success_1', data: {'index': 1}));
        workItems.add(ErrorWorkItem(id: 'error_1', data: {'shouldFail': true}));
        workItems.add(TestWorkItem(id: 'success_2', data: {'index': 2}));
        workItems.add(ErrorWorkItem(id: 'error_2', data: {'shouldFail': true}));
        workItems.add(TestWorkItem(id: 'success_3', data: {'index': 3}));
        
        final results = <dynamic>[];
        final errors = <dynamic>[];
        
        for (final item in workItems) {
          try {
            final result = await isolatePool.submit(item);
            results.add(result);
          } catch (e) {
            errors.add(e);
          }
        }
        
        expect(results.length, equals(3)); // 3 successful items
        expect(errors.length, equals(2)); // 2 failed items
        
        // Pool should continue working after errors
        final stats = isolatePool.getStats();
        expect(stats.totalWorkers, equals(2)); // Workers should still be available
        
      } finally {
        await isolatePool.dispose();
      }
    });
  });
  
  group('Task Manager Performance Tests', () {
    test('Task manager throughput', () async {
      final taskManager = TaskManager();
      
      try {
        // Add many tasks quickly
        final stopwatch = Stopwatch()..start();
        
        final taskIds = <String>[];
        for (int i = 0; i < 1000; i++) {
          final taskId = taskManager.addTask(
            name: 'Task $i',
            priority: TaskPriority.values[i % TaskPriority.values.length],
          );
          taskIds.add(taskId);
        }
        
        stopwatch.stop();
        
        print('Added 1000 tasks in: ${stopwatch.elapsedMilliseconds}ms');
        
        // Should be fast
        expect(stopwatch.elapsedMilliseconds, lessThan(1000)); // < 1 second
        
        // Check task manager state
        final stats = taskManager.getStats();
        expect(stats.totalTasks, equals(1000));
        expect(stats.queuedTasks, greaterThan(0));
        
        // Test cancellation performance
        final cancelStopwatch = Stopwatch()..start();
        
        for (int i = 0; i < 100; i++) {
          taskManager.cancelTask(taskIds[i]);
        }
        
        cancelStopwatch.stop();
        
        print('Cancelled 100 tasks in: ${cancelStopwatch.elapsedMilliseconds}ms');
        expect(cancelStopwatch.elapsedMilliseconds, lessThan(500)); // < 0.5 seconds
        
      } finally {
        taskManager.dispose();
      }
    });
    
    test('Progress tracker performance', () {
      final progressTracker = ProgressTracker();
      
      try {
        // Create many trackers
        final trackers = <TaskProgressTracker>[];
        for (int i = 0; i < 100; i++) {
          final tracker = progressTracker.createTracker(
            taskId: 'task_$i',
            totalItems: 100,
          );
          trackers.add(tracker);
        }
        
        // Update progress rapidly
        final stopwatch = Stopwatch()..start();
        
        for (int iteration = 0; iteration < 10; iteration++) {
          for (int i = 0; i < trackers.length; i++) {
            progressTracker.updateProgress(
              'task_$i',
              completed: iteration * 10,
              status: ProgressStatus.running,
            );
          }
        }
        
        stopwatch.stop();
        
        print('1000 progress updates in: ${stopwatch.elapsedMilliseconds}ms');
        
        // Should handle rapid updates efficiently
        expect(stopwatch.elapsedMilliseconds, lessThan(1000)); // < 1 second
        
        // Check final state
        final activeProgress = progressTracker.getAllActiveProgress();
        expect(activeProgress.length, equals(100));
        
      } finally {
        progressTracker.dispose();
      }
    });
  });
}

/// Test work item for isolate testing
class TestWorkItem extends WorkItem<String> {
  TestWorkItem({required String id, required Map<String, dynamic> data})
      : super(id: id, data: data);
  
  @override
  Future<String> process(IsolateChannel channel) async {
    final index = data['index'] ?? 0;
    final delay = data['delay'] ?? 0;
    
    if (delay > 0) {
      await Future.delayed(Duration(milliseconds: delay));
    }
    
    return 'Processed item $index';
  }
}

/// Memory test work item
class MemoryTestWorkItem extends WorkItem<String> {
  MemoryTestWorkItem({required String id, required Map<String, dynamic> data})
      : super(id: id, data: data);
  
  @override
  Future<String> process(IsolateChannel channel) async {
    final size = data['size'] ?? 1024;
    
    // Allocate memory
    final buffer = Uint8List(size);
    
    // Do some work with the buffer
    for (int i = 0; i < buffer.length; i++) {
      buffer[i] = i % 256;
    }
    
    // Simulate processing time
    await Future.delayed(const Duration(milliseconds: 100));
    
    return 'Memory test completed: ${buffer.length} bytes';
  }
}

/// Error work item for testing error handling
class ErrorWorkItem extends WorkItem<String> {
  ErrorWorkItem({required String id, required Map<String, dynamic> data})
      : super(id: id, data: data);
  
  @override
  Future<String> process(IsolateChannel channel) async {
    final shouldFail = data['shouldFail'] ?? false;
    
    if (shouldFail) {
      throw Exception('Intentional test error');
    }
    
    return 'Should not reach here';
  }
}

/// Helper function to create test image data
PixelData _createTestImage(int width, int height, int channels, {int seed = 0}) {
  final imageSize = width * height * channels;
  final random = math.Random(seed);
  
  final data = Uint8List.fromList(
    List.generate(imageSize, (i) => random.nextInt(256))
  );
  
  final imageInfo = ImageInfo(
    width: width,
    height: height,
    channels: channels,
    format: 'RGB',
    bitDepth: 8,
    hasAlpha: false,
    colorSpace: 'RGB',
  );
  
  return PixelData.fromBytes(imageInfo, data);
}
