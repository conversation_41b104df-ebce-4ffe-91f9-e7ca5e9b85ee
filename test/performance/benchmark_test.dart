/// Performance benchmark tests cho DCT compression
/// 
/// <PERSON><PERSON><PERSON> tra hiệu năng của các thuật toán DCT, compression,
/// và so sánh performance giữa các implementation khác nhau.
library;

import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_dct_compress/flutter_dct_compress.dart';
import 'dart:typed_data';
import 'dart:math' as math;

void main() {
  group('DCT Algorithm Benchmarks', () {
    test('DCT transform performance comparison', () {
      // Create test block
      final testBlock = List.generate(8, (i) =>
        List.generate(8, (j) => (i * 8 + j).toDouble())
      );
      
      // Benchmark standard DCT
      final standardStopwatch = Stopwatch()..start();
      for (int i = 0; i < 1000; i++) {
        DctTransform.forwardDct(testBlock);
      }
      standardStopwatch.stop();
      
      // Benchmark fast DCT
      final fastStopwatch = Stopwatch()..start();
      for (int i = 0; i < 1000; i++) {
        DctTransform.fastForwardDct(testBlock);
      }
      fastStopwatch.stop();
      
      print('Standard DCT: ${standardStopwatch.elapsedMicroseconds}μs for 1000 iterations');
      print('Fast DCT: ${fastStopwatch.elapsedMicroseconds}μs for 1000 iterations');
      
      // Fast DCT should be faster or at least comparable
      expect(fastStopwatch.elapsedMicroseconds, 
             lessThanOrEqualTo(standardStopwatch.elapsedMicroseconds * 1.2));
      
      // Both should complete in reasonable time
      expect(standardStopwatch.elapsedMilliseconds, lessThan(1000)); // < 1 second
      expect(fastStopwatch.elapsedMilliseconds, lessThan(1000)); // < 1 second
    });
    
    test('Quantization performance', () {
      final dctBlock = List.generate(8, (i) =>
        List.generate(8, (j) => (i * 8 + j).toDouble())
      );
      
      final quantTable = Quantization.createQuantizationTable(75);
      
      final stopwatch = Stopwatch()..start();
      for (int i = 0; i < 10000; i++) {
        Quantization.quantize(dctBlock, quantTable);
      }
      stopwatch.stop();
      
      print('Quantization: ${stopwatch.elapsedMicroseconds}μs for 10000 iterations');
      
      // Should be very fast
      expect(stopwatch.elapsedMilliseconds, lessThan(100)); // < 100ms
      
      // Calculate operations per second
      final opsPerSecond = 10000 / (stopwatch.elapsedMicroseconds / 1000000);
      expect(opsPerSecond, greaterThan(100000)); // > 100k ops/sec
    });
    
    test('Adaptive quantization performance', () {
      final imageInfo = ImageInfo(
        width: 64,
        height: 64,
        channels: 1,
        format: 'test',
        bitDepth: 8,
        hasAlpha: false,
        colorSpace: 'grayscale',
      );
      
      final testData = Uint8List.fromList(
        List.generate(64 * 64, (i) => i % 256)
      );
      final pixelData = PixelData.fromBytes(imageInfo, testData);
      
      final stopwatch = Stopwatch()..start();
      for (int i = 0; i < 100; i++) {
        AdaptiveQuantization.createAdaptiveQuantizationTable(pixelData, 75);
      }
      stopwatch.stop();
      
      print('Adaptive Quantization: ${stopwatch.elapsedMicroseconds}μs for 100 iterations');
      
      // Should complete in reasonable time
      expect(stopwatch.elapsedMilliseconds, lessThan(5000)); // < 5 seconds
    });
  });
  
  group('Compression Performance Benchmarks', () {
    test('Image compression speed by size', () async {
      final compressor = DctCompressor();
      final sizes = [16, 32, 64, 128, 256];
      final results = <String, Duration>{};
      
      for (final size in sizes) {
        final testData = _createTestImage(size, size, 3);
        
        final stopwatch = Stopwatch()..start();
        final result = await compressor.compressImage(
          testData,
          options: CompressionOptions.quality(75),
        );
        stopwatch.stop();
        
        expect(result.isSuccess, isTrue);
        results['${size}x$size'] = stopwatch.elapsed;
        
        print('${size}x$size compression: ${stopwatch.elapsedMilliseconds}ms');
      }
      
      // Larger images should take more time (generally)
      expect(results['256x256']!.inMilliseconds, 
             greaterThan(results['16x16']!.inMilliseconds));
      
      // All should complete in reasonable time
      for (final duration in results.values) {
        expect(duration.inSeconds, lessThan(10)); // < 10 seconds
      }
    });
    
    test('Compression speed by quality', () async {
      final compressor = DctCompressor();
      final testData = _createTestImage(64, 64, 3);
      final qualities = [25, 50, 75, 95];
      final results = <int, Duration>{};
      
      for (final quality in qualities) {
        final stopwatch = Stopwatch()..start();
        final result = await compressor.compressImage(
          testData,
          options: CompressionOptions.quality(quality),
        );
        stopwatch.stop();
        
        expect(result.isSuccess, isTrue);
        results[quality] = stopwatch.elapsed;
        
        print('Quality $quality compression: ${stopwatch.elapsedMilliseconds}ms');
      }
      
      // All qualities should complete in similar time
      for (final duration in results.values) {
        expect(duration.inSeconds, lessThan(5)); // < 5 seconds
      }
    });
    
    test('Color space conversion performance', () async {
      final testData = _createTestImage(128, 128, 3);
      final compressor = DctCompressor();
      
      // Test RGB compression
      final rgbStopwatch = Stopwatch()..start();
      final rgbResult = await compressor.compressImage(
        testData,
        options: CompressionOptions(quality: 75, colorSpace: ColorSpace.rgb),
      );
      rgbStopwatch.stop();
      
      // Test YUV compression
      final yuvStopwatch = Stopwatch()..start();
      final yuvResult = await compressor.compressImage(
        testData,
        options: CompressionOptions(quality: 75, colorSpace: ColorSpace.yuv),
      );
      yuvStopwatch.stop();
      
      expect(rgbResult.isSuccess, isTrue);
      expect(yuvResult.isSuccess, isTrue);
      
      print('RGB compression: ${rgbStopwatch.elapsedMilliseconds}ms');
      print('YUV compression: ${yuvStopwatch.elapsedMilliseconds}ms');
      
      // Both should complete in reasonable time
      expect(rgbStopwatch.elapsedSeconds, lessThan(5));
      expect(yuvStopwatch.elapsedSeconds, lessThan(5));
    });
    
    test('Progressive vs non-progressive compression', () async {
      final testData = _createTestImage(128, 128, 3);
      final compressor = DctCompressor();
      
      // Test standard compression
      final standardStopwatch = Stopwatch()..start();
      final standardResult = await compressor.compressImage(
        testData,
        options: CompressionOptions(quality: 75, progressive: false),
      );
      standardStopwatch.stop();
      
      // Test progressive compression
      final progressiveStopwatch = Stopwatch()..start();
      final progressiveResult = await compressor.compressImage(
        testData,
        options: CompressionOptions(quality: 75, progressive: true),
      );
      progressiveStopwatch.stop();
      
      expect(standardResult.isSuccess, isTrue);
      expect(progressiveResult.isSuccess, isTrue);
      
      print('Standard compression: ${standardStopwatch.elapsedMilliseconds}ms');
      print('Progressive compression: ${progressiveStopwatch.elapsedMilliseconds}ms');
      
      // Progressive might be slightly slower but should be comparable
      expect(progressiveStopwatch.elapsedMilliseconds,
             lessThanOrEqualTo(standardStopwatch.elapsedMilliseconds * 2));
    });
  });
  
  group('Batch Processing Performance', () {
    test('Batch processing scalability', () async {
      final batchProcessor = BatchProcessor();
      final batchSizes = [5, 10, 20, 50];
      final results = <int, Duration>{};
      
      for (final batchSize in batchSizes) {
        final inputs = <PixelData>[];
        for (int i = 0; i < batchSize; i++) {
          inputs.add(_createTestImage(32, 32, 3, seed: i));
        }
        
        final stopwatch = Stopwatch()..start();
        final result = await batchProcessor.processBatch(
          inputs,
          options: CompressionOptions.quality(75),
          maxConcurrency: 3,
        );
        stopwatch.stop();
        
        expect(result.isCompleteSuccess, isTrue);
        results[batchSize] = stopwatch.elapsed;
        
        final itemsPerSecond = batchSize / stopwatch.elapsed.inSeconds;
        print('Batch size $batchSize: ${stopwatch.elapsedMilliseconds}ms '
              '(${itemsPerSecond.toStringAsFixed(2)} items/sec)');
      }
      
      // Processing rate should be reasonable
      for (final entry in results.entries) {
        final itemsPerSecond = entry.key / entry.value.inSeconds;
        expect(itemsPerSecond, greaterThan(0.5)); // At least 0.5 items/sec
      }
    });
    
    test('Concurrency performance comparison', () async {
      final batchProcessor = BatchProcessor();
      final inputs = <PixelData>[];
      
      // Create test batch
      for (int i = 0; i < 20; i++) {
        inputs.add(_createTestImage(16, 16, 3, seed: i));
      }
      
      final concurrencyLevels = [1, 2, 4, 8];
      final results = <int, Duration>{};
      
      for (final concurrency in concurrencyLevels) {
        final stopwatch = Stopwatch()..start();
        final result = await batchProcessor.processBatch(
          inputs,
          options: CompressionOptions.quality(75),
          maxConcurrency: concurrency,
        );
        stopwatch.stop();
        
        expect(result.isCompleteSuccess, isTrue);
        results[concurrency] = stopwatch.elapsed;
        
        print('Concurrency $concurrency: ${stopwatch.elapsedMilliseconds}ms');
      }
      
      // Higher concurrency should generally be faster (up to a point)
      expect(results[4]!.inMilliseconds, 
             lessThanOrEqualTo(results[1]!.inMilliseconds));
    });
  });
  
  group('Memory Performance Tests', () {
    test('Memory usage scaling', () async {
      final compressor = DctCompressor();
      final sizes = [32, 64, 128];
      
      for (final size in sizes) {
        final testData = _createTestImage(size, size, 3);
        
        // Estimate memory usage
        final expectedMemory = size * size * 3 * 4; // Rough estimate
        
        final result = await compressor.compressImage(
          testData,
          options: CompressionOptions.quality(75),
        );
        
        expect(result.isSuccess, isTrue);
        
        if (result.memoryUsed != null) {
          print('${size}x$size memory usage: ${result.memoryUsed} bytes');
          
          // Memory usage should be reasonable
          expect(result.memoryUsed!, lessThan(expectedMemory * 10)); // < 10x estimate
        }
      }
    });
    
    test('Memory cleanup verification', () async {
      final compressor = DctCompressor();
      
      // Process multiple images to test memory cleanup
      for (int i = 0; i < 10; i++) {
        final testData = _createTestImage(64, 64, 3, seed: i);
        
        final result = await compressor.compressImage(
          testData,
          options: CompressionOptions.quality(75),
        );
        
        expect(result.isSuccess, isTrue);
        
        // Force garbage collection (if available)
        // In a real test, you might use platform-specific memory monitoring
      }
      
      // Test should complete without memory issues
      expect(true, isTrue); // Placeholder for memory verification
    });
  });
}

/// Helper function to create test image data
PixelData _createTestImage(int width, int height, int channels, {int seed = 0}) {
  final imageSize = width * height * channels;
  final random = math.Random(seed);
  
  final data = Uint8List.fromList(
    List.generate(imageSize, (i) {
      // Create semi-random but deterministic data
      return (random.nextInt(256) + i) % 256;
    })
  );
  
  final imageInfo = ImageInfo(
    width: width,
    height: height,
    channels: channels,
    format: 'RGB',
    bitDepth: 8,
    hasAlpha: false,
    colorSpace: 'RGB',
  );
  
  return PixelData.fromBytes(imageInfo, data);
}
